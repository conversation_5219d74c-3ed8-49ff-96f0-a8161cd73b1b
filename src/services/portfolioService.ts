import {
  currenciesConfig,
  giftsConfig,
  investmentUniverseConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";
import { DateTime } from "luxon";
import logger from "../external-services/loggerService";
import analytics, {
  TrackPropertiesType,
  TrackTransactionInfoCategoryType,
  TrackTransactionInfoType
} from "../external-services/segmentAnalyticsService";
import { RedisClientService } from "../loaders/redis";
import { AccountDocument } from "../models/Account";
import { DailyPortfolioTickerDocument } from "../models/DailyTicker";
import { IntraDayPortfolioTickerDocument } from "../models/IntraDayTicker";
import { OrderDocument, OrderDTOInterface, OrderSubmissionIntentEnum } from "../models/Order";
import {
  AllocationCreationFlowEnum,
  HoldingsType,
  InitialHoldingsAllocationType,
  PersonalisationType,
  Portfolio,
  PortfolioDocument,
  PortfolioDTOInterface,
  PortfolioModeEnum,
  PortfolioPopulationFieldsEnum,
  PortfolioWithReturnsByTenorDocument
} from "../models/Portfolio";
import { Reward, RewardDocument, UnitPriceType } from "../models/Reward";
import {
  AssetTransaction,
  AssetTransactionDocument,
  AssetTransactionInterfaceDTO,
  CashbackTransaction,
  CashbackTransactionDocument,
  CashbackTransactionDTOInterface,
  ChargeTransactionDocument,
  DepositCashTransactionDocument,
  DividendTransactionDocument,
  PortfolioTransactionCategoryType,
  RebalanceTransactionDocument,
  SavingsDividendTransactionDocument,
  SavingsTopupTransaction,
  SavingsTopupTransactionDocument,
  SavingsTopupTransactionDTOInterface,
  SavingsWithdrawalTransaction,
  SavingsWithdrawalTransactionDocument,
  SavingsWithdrawalTransactionDTOInterface,
  TransactionPopulationFieldsEnum,
  TransactionStatusType
} from "../models/Transaction";
import { UserDocument, UserPopulationFieldsEnum } from "../models/User";
import DailyTickerService from "./dailyTickerService";
import IntraDayTickerService from "./intraDayTickerService";
import OrderService, { SellOrderFilteringMethodEnum } from "./orderService";
import { SAVINGS_PRODUCT_MINIMUM_WK_ORDER_AMOUNT, TransactionService } from "./transactionService";
import PortfolioUtil from "../utils/portfolioUtil";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import mongoose, { QueryOptions, UpdateQuery } from "mongoose";
import { BadRequestError, InternalServerError } from "../models/ApiErrors";
import { CashFlow, xirr } from "@webcarrot/xirr";
import { PartialRecord } from "utils";
import DbUtil from "../utils/dbUtil";
import { addBreadcrumb, captureException, captureMessage } from "@sentry/node";
import UserService from "./userService";
import DateUtil, { DateOrderingEnum } from "../utils/dateUtil";
import { envIsProd } from "../utils/environmentUtil";
import InvestmentProductService from "./investmentProductService";
import { GiftDocument } from "../models/Gift";
import GiftService from "./giftService";
import { AutomationDocument } from "../models/Automation";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import ConfigUtil from "../utils/configUtil";
import { aggregateFees, getTotalFeeAmount, getZeroFees } from "../utils/feesUtil";
import { SubscriptionDocument } from "../models/Subscription";
import { OrderSideType } from "./brokerageService";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import SubscriptionService from "./subscriptionService";
import RewardService from "./rewardService";
import SavingsProductService from "./savingsProductService";
import {
  DISPLAY_IN_INVESTMENTS_CHART_CONFIG,
  DURATIONS_MAP,
  INTRADAY_DISPLAY_CONFIG,
  ONE_WEEK_IN_DAYS,
  TenorEnum
} from "../configs/durationConfig";
import { UserDataRequestDocument } from "../models/UserDataRequest";
import CurrencyUtil from "../utils/currencyUtil";
import { ProviderEnum } from "../configs/providersConfig";
import ExecutionWindowUtil from "../utils/executionWindowUtil";
import * as InvestmentUniverseUtil from "../utils/investmentUniverseUtil";
import SubmissionWindowUtil from "../utils/submissionWindowUtil";
import CorporateEventService from "./corporateEventService";
import { StockSplitCorporateEventDocument } from "../models/CorporateEvent";
import * as TickerUtil from "../utils/tickerUtil";
import { PortfolioPriceDataPointType } from "tickers";
import { FXSpreadSide } from "../types/fees";

const { AssetClassArray, InvestmentGeographyArray, InvestmentSectorArray, RISK_CONFIG, ASSET_CONFIG } =
  investmentUniverseConfig;
const { RESTRICTED_HOLDING_PERIOD_DAYS } = giftsConfig;
const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;
const PORTFOLIO_INTRADAY_INTERVAL_MINUTES_WEEKLY = 10;
const PORTFOLIO_INTRADAY_INTERVAL_MINUTES_MONTHLY = 30;
const PORTFOLIO_INTERVAL_WEEKS_ALL_TIME = 1; // 1 week
const PORTFOLIO_ALL_TIME_SAMPLING_THRESHOLD = 502; // if more then 502 data points, then we sample

const ZERO_RETURNS: Record<TenorEnum, number> = Object.fromEntries(
  Object.values(TenorEnum).map((tenor) => [tenor, 0])
) as Record<TenorEnum, number>;

export type PendingOrdersType = {
  [key in investmentUniverseConfig.AssetType]?: PendingOrderType;
};

export type PendingOrderType = {
  side: "buy" | "sell";
  quantity?: number;
  money?: number;
};

export enum PortfolioAllocationMethodEnum {
  TARGET_ALLOCATION = "targetAllocation",
  HOLDINGS = "holdings"
}

type CashFlowWithQuantity = CashFlow & {
  quantity: number;
  isin?: string;
  side?: OrderSideType;
  wealthyhoodUnitPrice?: UnitPriceType;
  exchangeRate?: number;
};

export type PortfolioHoldingsUpdates = Pick<OrderDocument, "side" | "quantity" | "isin">;

const MINIMUM_ASSET_ALLOCATION_DIFF = 3;
const MINIMUM_TARGET_ALLOCATION_DIFF = 1;

class PortfolioService {
  /**
   * @description Calculates the average price that the shares where bought.
   *
   * It adjusts for any stock splits that have happened throughout the asset's lifetime.
   *
   * For example, consider the below scenario:
   * User bought one share of Apple (£10) three years ago
   * Stock split (1 -> 10) two years ago
   * User bought one share of Apple (£1) one year ago
   * Stock split (1 -> 2) six months ago
   * User bought one share of Apple (£0.5) yesterday
   *
   * In the above, the average price per share should be £0.5.
   *
   * @param assetId
   * @param transactions
   * @returns The price per share in the asset traded currency and the portfolio settlement
   * currency *in cents*.
   */
  public static async getAveragePricePerShare(
    assetId: investmentUniverseConfig.AssetType,
    portfolio: PortfolioDocument,
    transactions: {
      rewards: RewardDocument[];
    }
  ): Promise<{
    priceInTradedCurrency: number;
    priceInSettlementCurrency: number;
  }> {
    const { rewards } = transactions;

    const userId = portfolio.populated("owner") ? portfolio.owner.id : portfolio.owner;
    const allAssetIsinsSet = new Set(InvestmentUniverseUtil.getIsinActiveAndDeprecated(assetId));
    const orders = (
      await OrderService.getMatchedOrdersForTransactions(userId, ["AssetTransaction", "RebalanceTransaction"])
    ).filter((order) => allAssetIsinsSet.has(order.isin));
    const assetRewards = rewards.filter((reward) => reward.asset === assetId);

    // In case a user has sold all the quantity at some point,
    // keep only the orders/rewards after that point.
    const { eligibleOrders, eligibleRewards } = PortfolioService._filterOutActivityBeforeLastFullSell(
      orders,
      [],
      assetRewards
    );

    const flows: CashFlowWithQuantity[] = [
      ...eligibleOrders.map(
        ({ side, amountForReturnsAndUpBy, quantity, filledAt, wealthyhoodUnitPrice, isin, exchangeRate }) => {
          return {
            isin,
            side,
            amount: amountForReturnsAndUpBy,
            quantity: side === "Buy" ? quantity : -quantity,
            date: filledAt,
            wealthyhoodUnitPrice,
            exchangeRate: exchangeRate ?? 1
          };
        }
      ),
      ...eligibleRewards.map((reward) => ({
        isin: ASSET_CONFIG[reward.asset].isin,
        side: "Buy" as OrderSideType,
        amount: reward.consideration.amount,
        quantity: reward.quantity,
        date: new Date(reward.updatedAt ?? reward.createdAt),
        wealthyhoodUnitPrice: reward.wealthyhoodUnitPrice,
        exchangeRate: reward.exchangeRate ?? 1
      }))
    ].sort((flow1, flow2) => flow1.date.getTime() - flow2.date.getTime());

    const assetBuyFlows = flows.filter((order) => allAssetIsinsSet.has(order.isin) && order.side === "Buy");

    if (assetBuyFlows.length === 0) {
      return {
        priceInTradedCurrency: 0,
        priceInSettlementCurrency: 0
      };
    }

    // Total value bought in asset traded currency
    const totalValueBoughtTraded = assetBuyFlows
      .map((flow) => Decimal.mul(flow.wealthyhoodUnitPrice.amount, flow.quantity))
      .reduce((sum, value) => sum.plus(value), new Decimal(0));
    // Total value bought in portfolio settlement currency
    const totalValueBoughtSettlement = assetBuyFlows
      // divide the unit price by the exchange rate to always be in the settlement currency
      .map((flow) => Decimal.div(flow.wealthyhoodUnitPrice.amount, flow.exchangeRate).mul(flow.quantity))
      .reduce((sum, value) => sum.plus(value), new Decimal(0));

    // When getting the total quantity bought, we are interested in the **adjusted** quantity bought based on
    // the asset's stock splits.
    const stockSplits = await CorporateEventService.getAllStockSplits(assetId);
    const totalQuantityBought = PortfolioService._getSplitAdjustedTotalQuantityBought(assetBuyFlows, stockSplits);

    return {
      priceInTradedCurrency: Decimal.div(totalValueBoughtTraded, totalQuantityBought)
        .toDecimalPlaces(2)
        .toNumber(),
      priceInSettlementCurrency: Decimal.div(totalValueBoughtSettlement, totalQuantityBought)
        .toDecimalPlaces(2)
        .toNumber()
    };
  }

  /**
   * @description Updates the available and the reserved cash for a single
   * portfolio by the amounts given. The amounts are usually the same, but
   * with different signs, e.g. updateCashAvailability(id, 50, -50).
   *
   * @param portfolioId The ID of the portfolio to be updated.
   * @param userCurrency The user currency
   * @param cashUpBy The amount added to the (available or settled) cash. A negative value means the amount is subtracted. This parameter DOES NOT set the cash value.
   * @param options
   */
  public static async updateCashAvailability(
    portfolioId: string,
    userCurrency: currenciesConfig.MainCurrencyType,
    cashUpBy: number,
    options: { available: boolean; settled: boolean; session?: mongoose.ClientSession } = {
      available: true,
      settled: true
    }
  ): Promise<PortfolioDocument> {
    const portfolio = await Portfolio.findById(portfolioId, null, { session: options?.session });
    if (!portfolio) throw new BadRequestError("'portfolioId' does not refer to a portfolio");

    logger.info("Updating portfolio cash", {
      module: "PortfolioService",
      method: "updateCashAvailability",
      data: { userId: portfolio.owner.toString(), portfolioId, cashUpBy }
    });

    const cashUpdates: Record<string, number> = {};
    if (options.available) {
      cashUpdates[`cash.${userCurrency}.available`] = Decimal.add(
        portfolio?.cash?.[userCurrency]?.available ?? 0,
        cashUpBy
      ).toNumber();
    }
    if (options.settled) {
      cashUpdates[`cash.${userCurrency}.settled`] = Decimal.add(
        portfolio?.cash?.[userCurrency]?.settled ?? 0,
        cashUpBy
      ).toNumber();
    }

    const updatedPortfolio = await Portfolio.findOneAndUpdate(
      { _id: portfolioId },
      { $set: cashUpdates },
      {
        runValidators: true,
        upsert: false,
        new: true,
        session: options?.session
      }
    );

    logger.info(
      `Updated portfolio cash to ${updatedPortfolio?.cash?.[userCurrency]?.available} available and ${updatedPortfolio?.cash?.[userCurrency]?.settled} settled`,
      {
        module: "PortfolioService",
        method: "updateCashAvailability",
        data: { userId: portfolio.owner.toString(), portfolioId, cashUpBy, cashUpdates }
      }
    );

    return updatedPortfolio;
  }

  public static async populateTickersAndOwner(
    portfolio: PortfolioDocument,
    options: {
      initialHoldingsAllocation: boolean;
    } = {
      initialHoldingsAllocation: true
    }
  ): Promise<PortfolioDocument> {
    if (
      !portfolio.populated("currentTicker") ||
      !portfolio.populated("holdings.asset") ||
      !portfolio.holdings?.[0]?.asset?.currentTicker?._id || // Check if current ticker inside holdings.asset is populated
      (options.initialHoldingsAllocation && !portfolio.initialHoldingsAllocation?.[0]?.asset?.currentTicker?._id)
    ) {
      const populateOptions: mongoose.PopulateOptions[] = [
        { path: "owner" },
        { path: "currentTicker" },
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ];
      if (options.initialHoldingsAllocation) {
        populateOptions.push({
          path: "initialHoldingsAllocation.asset",
          populate: {
            path: "currentTicker"
          }
        });
      }
      await portfolio.populate(populateOptions);
    }
    return portfolio;
  }

  public static async getPortfolioByWealthkernelId(
    wealthkernelId: string,
    populate: { owner: boolean }
  ): Promise<PortfolioDocument> {
    return Portfolio.findOne({
      "providers.wealthkernel.id": wealthkernelId
    }).populate(DbUtil.getPopulationString(populate));
  }

  public static async getPortfolio(id: string, populateTickers = false): Promise<PortfolioDocument> {
    const portfolio = await Portfolio.findOne({ _id: id });

    if (portfolio && populateTickers) {
      await portfolio.populate([
        { path: "currentTicker" },
        { path: "owner" },
        {
          path: "initialHoldingsAllocation.asset",
          populate: {
            path: "currentTicker"
          }
        },
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ]);
    }

    return portfolio;
  }

  public static async getRealPortfolios(owner: string): Promise<PortfolioDocument[]> {
    return Portfolio.find({ owner: owner, mode: PortfolioModeEnum.REAL }).populate([
      { path: "currentTicker" },
      {
        path: "holdings.asset",
        populate: {
          path: "currentTicker"
        }
      },
      {
        path: "initialHoldingsAllocation.asset",
        populate: {
          path: "currentTicker"
        }
      }
    ]);
  }

  public static async getPortfolios(
    params: {
      _id?: string;
      owner?: string;
      mode?: PortfolioModeEnum;
      wealthkernelExists?: boolean;
    } = {},
    sort?: string,
    populateTickers = true
  ): Promise<PortfolioDocument[]> {
    const { filterParameters, queryOptions } = PortfolioService._getMongooseQueryParameters(params, sort);

    const portfolios = await Portfolio.find(filterParameters, null, queryOptions);

    if (populateTickers) {
      await Promise.all(
        portfolios.map(
          async (portfolio) =>
            await portfolio.populate([
              { path: "currentTicker" },
              { path: "owner" },
              { path: "initialHoldingsAllocation.asset" },
              {
                path: "holdings.asset",
                populate: {
                  path: "currentTicker"
                }
              }
            ])
        )
      );
    }

    return portfolios;
  }

  /**
   * @description Returns a Cursor to iterate through all portfolios that match the given filters.
   * @param params
   * @param populate
   * @param sort
   */
  public static getPortfoliosStreamed(
    params: {
      _id?: any;
      owner?: string;
      mode?: PortfolioModeEnum;
      wealthkernelExists?: boolean;
      holdings?: any;
      hasHolding?: investmentUniverseConfig.AssetType;
      initialHoldingsAllocation?: any;
      $or?: Array<any>;
    } = {},
    populate = { currentTicker: false, owner: false },
    sort?: string
  ) {
    const { filterParameters, queryOptions } = PortfolioService._getMongooseQueryParameters(params, sort);
    let filterParametersWithMongoOps = filterParameters;
    if (params["$or"]) {
      filterParametersWithMongoOps = { ...filterParameters, $or: params["$or"] };
    }

    return Portfolio.find(filterParametersWithMongoOps, null, queryOptions)
      .populate(DbUtil.getPopulationString(populate))
      .cursor()
      .addCursorFlag("noCursorTimeout", envIsProd());
  }

  public static async getPortfolioWithReturnsByTenor(
    portfolio: PortfolioDocument
  ): Promise<PortfolioWithReturnsByTenorDocument> {
    await PortfolioService.populateTickersAndOwner(portfolio, { initialHoldingsAllocation: false });

    const owner = portfolio.owner as UserDocument;
    if (!owner.hasConvertedPortfolio && portfolio.holdings.length === 0) {
      return {
        ...portfolio.toObject(),
        returnsValues: ZERO_RETURNS,
        upByValues: ZERO_RETURNS,
        holdings: []
      } as PortfolioWithReturnsByTenorDocument;
    }

    const [dividendsForReturns, rewardsForReturns] = await Promise.all([
      TransactionService.getDividendTransactionsForReturnsUpBy(owner.id),
      RewardService.getSettledRewards(owner.id)
    ]);
    const transactions = { dividendTransactions: dividendsForReturns, rewards: rewardsForReturns };

    const [returnsValues, upByValues, holdingsWithReturns] = await Promise.all([
      PortfolioService.getPortfolioReturnsAllTenors(portfolio, transactions),
      PortfolioService.getPortfolioUpByValuesAllTenors(portfolio, transactions),
      PortfolioService._getAllHoldingsReturns(portfolio, {
        dividendTransactions: transactions.dividendTransactions,
        rewards: rewardsForReturns
      })
    ]);

    return {
      ...portfolio.toObject(),
      returnsValues,
      upByValues,
      holdings: holdingsWithReturns
    } as PortfolioWithReturnsByTenorDocument;
  }

  /**
   * @description Returns the portfolio values organised by tenor.
   * The data will be used for the dashboard chart.
   *
   * @param portfolioId
   * @param userCurrency
   */
  public static async getPricesByTenor(
    portfolioId: string,
    userCurrency: currenciesConfig.MainCurrencyType
  ): Promise<Record<TenorEnum, { data: { timestamp: number; value: number; displayIntraday: boolean }[] }>> {
    // Get monthly intra-day tickers & all daily tickers
    const [intraDayMonthsPortfolioTickersResponse, dailyPortfolioTickerResponse, portfolio] = await Promise.all([
      IntraDayTickerService.getMonthsTickersForPortfolio(portfolioId),
      DailyTickerService.getDailyPortfolioTickers(
        {
          portfolio: portfolioId
        },
        null,
        "+date"
      ),
      PortfolioService.getPortfolio(portfolioId, true)
    ]);

    const filteredIntradayMonthsPortfolioTickers = this._filterTickersByMarketHours(
      intraDayMonthsPortfolioTickersResponse
    );

    // Map to data with fields timestamp & close
    const intraDayMonthsPortfolioTickers = filteredIntradayMonthsPortfolioTickers.map(
      ({ timestamp, pricePerCurrency }) => ({
        timestamp: new Date(timestamp).getTime(),
        value: pricePerCurrency[userCurrency]
      })
    ) as PortfolioPriceDataPointType[];
    const allDailyPortfolioTickers = (dailyPortfolioTickerResponse.data as DailyPortfolioTickerDocument[]).map(
      ({ pricePerCurrency, date }) => ({
        // Daily portfolio tickers have a date which is just before the end of the day.
        // Here we set to UK timezone 13:30 (middle of the day) in order to avoid having daily tickers just before the
        // end of the day which on the device it may be displayed as day after.
        timestamp: DateTime.fromJSDate(new Date(date))
          .setZone("Europe/London", { keepLocalTime: true })
          .set({
            hour: 13,
            minute: 0
          })
          .toJSDate()
          .getTime(),
        value: pricePerCurrency[userCurrency]
      })
    );

    // Organise data by tenor
    return Object.fromEntries(
      await Promise.all(
        Object.values(TenorEnum)
          .filter((tenor) => DISPLAY_IN_INVESTMENTS_CHART_CONFIG[tenor])
          .map(async (tenor) => {
            const todayDataExist = portfolio.currentTicker && DateUtil.isToday(portfolio.currentTicker.timestamp);
            const dateAtTenorStart = TickerUtil.getDateAtTenorStart(tenor, new Date(Date.now()), todayDataExist);

            let data: PortfolioPriceDataPointType[];
            let displayIntraday = INTRADAY_DISPLAY_CONFIG[tenor];
            if (tenor === TenorEnum.ONE_WEEK) {
              data = TickerUtil.sampleTickers(
                intraDayMonthsPortfolioTickers.filter(
                  ({ timestamp }) => new Date(timestamp).getTime() >= new Date(dateAtTenorStart).getTime()
                ),
                PORTFOLIO_INTRADAY_INTERVAL_MINUTES_WEEKLY
              );
            } else if (tenor === TenorEnum.ONE_MONTH) {
              data = TickerUtil.sampleTickers(
                intraDayMonthsPortfolioTickers.filter(
                  ({ timestamp }) => new Date(timestamp).getTime() >= new Date(dateAtTenorStart).getTime()
                ),
                PORTFOLIO_INTRADAY_INTERVAL_MINUTES_MONTHLY
              );
            } else if (tenor === TenorEnum.ALL_TIME) {
              // if tenor is ALL_TIME and the daily portfolio tickers are less than one month's length
              // then we display intraday
              if (allDailyPortfolioTickers.length <= DURATIONS_MAP[TenorEnum.ONE_MONTH] + 1) {
                data = [...intraDayMonthsPortfolioTickers];
                displayIntraday = true;
              } else {
                data = [...allDailyPortfolioTickers];

                if (data.length >= PORTFOLIO_ALL_TIME_SAMPLING_THRESHOLD) {
                  // we sample weekly only if we have a large number of daily tickers (PORTFOLIO_INTERVAL_WEEKS_ALL_TIME)
                  data = TickerUtil.sampleTickers(
                    allDailyPortfolioTickers,
                    PORTFOLIO_INTERVAL_WEEKS_ALL_TIME,
                    "weeks"
                  );
                }
              }
            } else {
              data = allDailyPortfolioTickers.filter(
                ({ timestamp }) => timestamp >= new Date(dateAtTenorStart).getTime()
              );
            }

            if (data?.length > 0) {
              // the first point in the chart is the latest daily portfolio ticker before the data start date
              const latestDailyPortfolioTickerBeforeTenorStart =
                await DailyTickerService.getLatestDailyPortfolioTickerBeforeDate(
                  portfolioId,
                  new Date(data?.at(0)?.timestamp)
                );

              if (latestDailyPortfolioTickerBeforeTenorStart) {
                data.unshift({
                  timestamp: DateUtil.setDateToLatestCloseMarketHours(
                    new Date(latestDailyPortfolioTickerBeforeTenorStart.date)
                  ).getTime(),
                  value: latestDailyPortfolioTickerBeforeTenorStart.pricePerCurrency[userCurrency]
                });
              }
            }

            if (intraDayMonthsPortfolioTickers.length > 0) {
              // the latest point in the chart is the latest intra-day ticker regardless of the tenor
              data = PortfolioService._addTodayIntraDayTicker(
                {
                  timestamp: DateUtil.mapDateToClosestTimeMark(
                    new Date(intraDayMonthsPortfolioTickers.at(-1).timestamp),
                    PORTFOLIO_INTRADAY_INTERVAL_MINUTES_WEEKLY,
                    "minutes"
                  ).getTime(),
                  value: portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset))
                },
                data
              );
            }

            const dataWithIntradayFlag = data.map(({ timestamp, value }) => ({
              timestamp,
              value,
              displayIntraday
            }));

            // find dates to fill in with zeros
            const datesToFillWithZeros = PortfolioService._getDatesToFillWithZeros(tenor, data[0]?.timestamp);
            const zeroTickers = datesToFillWithZeros.map((date) => {
              return {
                timestamp: DateTime.fromJSDate(new Date(date))
                  .setZone("Europe/London", { keepLocalTime: true })
                  .set({
                    hour: 13,
                    minute: 0
                  })
                  .toJSDate()
                  .getTime(),
                value: 0,
                // we display by day on zero tickers
                displayIntraday: false
              };
            });

            return [
              tenor,
              {
                data: zeroTickers.concat(dataWithIntradayFlag)
              }
            ];
          })
      )
    ) as Record<TenorEnum, { data: { timestamp: number; value: number; displayIntraday: boolean }[] }>;
  }

  /**
   * @description Handles portfolio investments.
   * Steps:
   * - Asset transaction creation
   * - Creation of corresponding buy orders (wealthkernel & db storage)
   * - Linking of orders to parent asset transaction
   * - Deduct available investor cash from portfolio
   *
   * @param portfolio
   * @param investmentAmount
   * @param options
   */
  public static async buyAssetsForPortfolio(
    portfolio: PortfolioDocument,
    investmentAmount: number,
    options: {
      allocationMethod: PortfolioAllocationMethodEnum;
      pendingDeposit?: DepositCashTransactionDocument;
      pendingGift?: GiftDocument;
      linkedAutomation?: AutomationDocument;
      executeEtfOrdersInRealtime: boolean;
    }
  ): Promise<AssetTransactionDocument> {
    if (!portfolio) throw new BadRequestError("'portfolio' does not exist");
    if (options.pendingDeposit && options.pendingGift)
      throw new BadRequestError("Both gift and deposit were used for buying portfolio");

    const [investmentProducts, user] = await Promise.all([
      InvestmentProductService.getInvestmentProductsDict("isin", true),
      UserService.getUser(portfolio.owner.id, {
        addresses: false,
        portfolios: false,
        subscription: true
      })
    ]);

    const holdingsPercentages = PortfolioService._getHoldingsPercentages(
      user.currency,
      portfolio,
      options.allocationMethod
    );
    const ordersData = await OrderService.getBuyOrdersToCreate(
      portfolio,
      user.currency,
      holdingsPercentages,
      investmentAmount,
      user.companyEntity,
      investmentProducts,
      options.executeEtfOrdersInRealtime,
      options.allocationMethod
    );
    const executionWindow = ExecutionWindowUtil.getAssetTransactionExecutionWindow(ordersData, user.currency, {
      investmentProducts: investmentProducts
    });

    const assetTransactionData: Omit<AssetTransactionInterfaceDTO, "createdAt" | "originalInvestmentAmount"> = {
      consideration: {
        currency: user.currency,
        amount: Decimal.mul(investmentAmount, 100).toNumber() // amount is stored in cents
      },
      owner: portfolio.owner as mongoose.Types.ObjectId,
      portfolio: portfolio.id,
      portfolioTransactionCategory: "buy" as PortfolioTransactionCategoryType,
      status: PortfolioService._getInitialTransactionStatus(options.pendingDeposit, options.pendingGift),
      executionWindow
    };

    if (options.pendingDeposit) {
      assetTransactionData.pendingDeposit = options.pendingDeposit._id;
    } else if (options.pendingGift) {
      assetTransactionData.pendingGift = options.pendingGift._id;
    }

    if (options.linkedAutomation) {
      assetTransactionData.linkedAutomation = options.linkedAutomation._id;
    }

    const assetTransaction = await TransactionService.createAssetTransaction(assetTransactionData);

    const ordersDataAfterFees = OrderService.applyFeesToOrders(
      user.subscription.plan,
      ordersData,
      user.currency,
      investmentProducts
    );
    const fees = aggregateFees(
      ordersDataAfterFees.map((orderData) => orderData.fees),
      user.currency
    );

    const ordersToCreate: OrderDTOInterface[] = ordersDataAfterFees.map((order) => {
      return {
        ...order,
        transaction: assetTransaction.id,
        activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
      };
    });
    const orders = await OrderService.createManyDbOrders(ordersToCreate);

    // is initially calculated in cents
    const finalOrderAmount = orders
      .map((order) => new Decimal(order.consideration.amount))
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .div(100)
      .add(getTotalFeeAmount(fees));
    const orderIDs = orders.map(({ id }) => id);

    // 2. Link orders to the parent asset transaction
    const updatedAssetTransaction = await AssetTransaction.findOneAndUpdate(
      { _id: assetTransaction.id },
      {
        $addToSet: { orders: { $each: orderIDs } },
        fees,
        consideration: {
          currency: user.currency,
          amount: finalOrderAmount.mul(100).toNumber() // amount is stored in cents
        }
      },
      {
        runValidators: true,
        new: true
      }
    ).populate("orders pendingDeposit pendingGift");

    const [cashbackAmount, buyFxRates, sellFxRates] = await Promise.all([
      TransactionService.getCashbackAmount(updatedAssetTransaction.originalInvestmentAmount, user),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.BUY),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.SELL)
    ]);

    let cashback: CashbackTransactionDocument;
    if (cashbackAmount > 0) {
      const cashbackData: CashbackTransactionDTOInterface = {
        consideration: {
          amount: cashbackAmount,
          currency: user.currency
        },
        owner: portfolio.owner as mongoose.Types.ObjectId,
        portfolio: portfolio.id,
        cashbackMonth: DateUtil.getYearAndMonth(new Date()),
        linkedAssetTransaction: assetTransaction.id,
        createdAt: new Date(),
        deposit: {
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        },
        price: user.subscription.price
      };

      cashback = await new CashbackTransaction(cashbackData).save();
    }

    const transactionSideEffectPromises = [];

    if (!options.pendingDeposit && !options.pendingGift) {
      transactionSideEffectPromises.push(
        // 3. Reduce cash availability
        PortfolioService.updateCashAvailability(portfolio.id, user.currency, finalOrderAmount.neg().toNumber(), {
          available: true,
          settled: true
        }),
        PortfolioService._emitInvestmentCreationEvent(updatedAssetTransaction, investmentProducts, cashback),
        OrderService.submitRealtimeOrdersSafely(orders, portfolio)
      );

      if (
        options.allocationMethod === PortfolioAllocationMethodEnum.TARGET_ALLOCATION &&
        !user.hasConvertedPortfolio
      ) {
        transactionSideEffectPromises.push(
          UserService.updateUser(user.id, { portfolioConversionStatus: "inProgress" })
        );
      }
    }

    // in case of a recurring buy, emit investmentCreation event instantly
    if (options.linkedAutomation) {
      transactionSideEffectPromises.push(
        PortfolioService._emitInvestmentCreationEvent(updatedAssetTransaction, investmentProducts, cashback)
      );
    }

    await Promise.all(transactionSideEffectPromises);

    return TransactionService.fillClientDisplayFields(
      user,
      updatedAssetTransaction,
      investmentProducts,
      {
        BUY: buyFxRates,
        SELL: sellFxRates
      },
      {
        displayAmount: true,
        displayQuantity: true,
        executionWindow: true,
        displayExchangeRate: true,
        isCancellable: true
      }
    ) as AssetTransactionDocument;
  }

  /**
   * @description Handles selling of assets for real portfolios given a portfolio and
   * the portfolio value that we want to sell.
   * Steps:
   * - Asset transaction creation
   * - Creation of corresponding sell orders to DB
   *    by autocalculating selling quantities based on holdings
   * - Linking of orders to parent asset transaction
   * - Creation of corresponding sell orders to Wealthkernel and update them to DB
   * @param portfolio
   * @param orderAmount
   * @param options
   */
  public static async sellAssetsForPortfolio(
    portfolio: PortfolioDocument,
    orderAmount: number,
    options: {
      linkedUserDataRequest?: string;
      orderFilteringMethod?: SellOrderFilteringMethodEnum;
    } = {
      orderFilteringMethod: SellOrderFilteringMethodEnum.FILTER_AND_DISCARD
    }
  ): Promise<AssetTransactionDocument> {
    orderAmount = new Decimal(orderAmount).toDecimalPlaces(2).toNumber();

    if (!portfolio) throw new BadRequestError("'portfolio' does not exist");

    const [investmentProducts, ordersData, user] = await Promise.all([
      InvestmentProductService.getInvestmentProductsDict("isin", true),
      OrderService.getSellOrdersToCreate(portfolio, orderAmount, {
        orderFilteringMethod: options.orderFilteringMethod,
        executeEtfOrdersInRealtime: false
      }),
      UserService.getUser(portfolio.owner.id, {
        addresses: false,
        portfolios: false,
        subscription: true
      })
    ]);

    const executionWindow = ExecutionWindowUtil.getAssetTransactionExecutionWindow(ordersData, user.currency, {
      investmentProducts: investmentProducts
    });

    const assetTransactionData = {
      consideration: {
        currency: user.currency,
        amount: Decimal.mul(orderAmount, 100).toNumber()
      },
      owner: user.id,
      portfolio: portfolio.id,
      portfolioTransactionCategory: "sell" as PortfolioTransactionCategoryType,
      status: "Pending" as TransactionStatusType,
      executionWindow,
      linkedUserDataRequest: options.linkedUserDataRequest
        ? new mongoose.Types.ObjectId(options.linkedUserDataRequest)
        : undefined
    };
    const assetTransaction = await TransactionService.createAssetTransaction(assetTransactionData);

    const ordersToCreate: OrderDTOInterface[] = ordersData.map((order) => {
      return {
        ...order,
        transaction: assetTransaction.id
      };
    });
    const orders = await OrderService.createManyDbOrders(ordersToCreate);

    const orderIds = orders.map(({ id }) => id);

    const updatedAssetTransaction = await AssetTransaction.findOneAndUpdate(
      { _id: assetTransaction.id },
      { $addToSet: { orders: { $each: orderIds } } },
      {
        runValidators: true,
        new: true
      }
    ).populate("orders pendingDeposit");

    const [buyFxRates, sellFxRates] = await Promise.all([
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.BUY),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, user.subscription.plan, FXSpreadSide.SELL),
      OrderService.submitRealtimeOrdersSafely(orders, portfolio),
      PortfolioService._emitInvestmentCreationEvent(updatedAssetTransaction, investmentProducts)
    ]);

    return TransactionService.fillClientDisplayFields(
      user,
      updatedAssetTransaction,
      investmentProducts,
      {
        BUY: buyFxRates,
        SELL: sellFxRates
      },
      {
        displayAmount: true,
        displayQuantity: true,
        executionWindow: true,
        displayExchangeRate: true,
        isCancellable: true,
        estimatedRealTimeCommission: true
      }
    ) as AssetTransactionDocument;
  }

  public static async sellWholePortfolio(
    portfolioId: string,
    options: { linkedUserDataRequest?: string; executeEtfOrdersInRealtime: boolean }
  ): Promise<AssetTransactionDocument> {
    const portfolio = await PortfolioService.getPortfolio(portfolioId, true);
    const user = portfolio.owner as UserDocument;
    const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("isin", true);

    if (!portfolio) throw new BadRequestError("'portfolio' does not exist");

    const availableHoldings = await PortfolioService.getAvailableHoldings(portfolio);

    if (availableHoldings.length === 0) {
      throw new BadRequestError("Portfolio has no holding to sell");
    }

    const ordersData: Omit<OrderDTOInterface, "transaction">[] = availableHoldings
      .map(({ quantity, assetCommonId }) => ({
        quantity: quantity,
        isin: ASSET_CONFIG[assetCommonId].isin,
        settlementCurrency: user.currency,
        side: "Sell" as OrderSideType,
        activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE]),
        consideration: {
          currency: user.currency
        }
      }))
      .map((order) => ({
        ...order,
        submissionIntent: SubmissionWindowUtil.getOrderSubmissionIntent(order, {
          executeEtfOrdersInRealtime: options.executeEtfOrdersInRealtime,
          userCurrency: user.currency,
          investmentProduct: investmentProducts[order.isin]
        })
      }));

    const executionWindow = ExecutionWindowUtil.getAssetTransactionExecutionWindow(ordersData, user.currency, {
      investmentProducts: investmentProducts
    });

    const holdingsValue = availableHoldings
      .map(({ quantity, asset }) => Decimal.mul(quantity, asset.currentTicker.getPrice(user.currency)))
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .toDecimalPlaces(2);

    const assetTransactionData = {
      consideration: {
        currency: user.currency,
        amount: holdingsValue.mul(100).toNumber()
      },
      owner: user.id,
      portfolio: portfolio.id,
      portfolioTransactionCategory: "sell" as PortfolioTransactionCategoryType,
      status: "Pending" as TransactionStatusType,
      executionWindow,
      linkedUserDataRequest: options.linkedUserDataRequest
        ? new mongoose.Types.ObjectId(options.linkedUserDataRequest)
        : undefined
    };
    const assetTransaction = await TransactionService.createAssetTransaction(assetTransactionData);

    const orders = await Promise.all(
      ordersData.map((order) => {
        const orderData = {
          ...order,
          transaction: assetTransaction.id
        };
        return OrderService.createDbOrder(orderData as OrderDTOInterface);
      })
    );
    const orderIds = orders.map(({ id }) => id);

    await assetTransaction.updateOne(
      { $addToSet: { orders: { $each: orderIds } } },
      {
        runValidators: true
      }
    );
    await OrderService.submitRealtimeOrdersSafely(orders, portfolio);

    const updatedAssetTransaction = await AssetTransaction.findById(assetTransaction._id).populate("orders");

    await PortfolioService._emitInvestmentCreationEvent(updatedAssetTransaction, investmentProducts);

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    if (!user.populated("subscription")) {
      await user.populate("subscription");
    }

    const plan = PRICE_CONFIG[(user.subscription as SubscriptionDocument).price].plan;

    const latestFXRates = {
      BUY: await TransactionService.getAllForeignCurrencyRatesWithSpread(user, plan, FXSpreadSide.BUY),
      SELL: await TransactionService.getAllForeignCurrencyRatesWithSpread(user, plan, FXSpreadSide.SELL)
    };

    return TransactionService.fillClientDisplayFields(
      user,
      updatedAssetTransaction,
      investmentProducts,
      latestFXRates,
      {
        displayAmount: true,
        displayQuantity: true,
        executionWindow: true,
        displayExchangeRate: true,
        isCancellable: true
      }
    ) as AssetTransactionDocument;
  }

  /**
   * @description Handles rebalancing of assets for real portfolios given a portfolio and
   * the target allocation we want to rebalance to.
   * Steps:
   * - Rebalance transaction creation
   * - Event emission
   * @param portfolio
   * @param targetAllocation
   * @param options
   */
  public static async rebalancePortfolio(
    portfolio: PortfolioDocument,
    targetAllocation: InitialHoldingsAllocationType[],
    options?: {
      linkedAutomation?: AutomationDocument;
    }
  ): Promise<RebalanceTransactionDocument> {
    const notStartedRebalanceTransactions = await TransactionService.getNotStartedRebalanceTransactions(
      portfolio.id
    );

    if (notStartedRebalanceTransactions.length === 1) {
      return await TransactionService.updateRebalanceTransaction(
        notStartedRebalanceTransactions[0].id,
        targetAllocation
      );
    } else if (notStartedRebalanceTransactions.length > 1) {
      throw new InternalServerError(
        `There can only be 1 rebalance transaction with 'NotStarted' status, found ${notStartedRebalanceTransactions.length}`
      );
    }

    return await TransactionService.createRebalanceTransaction(portfolio, targetAllocation, options);
  }

  /**
   * @description Creates or updates the portfolio's initialHoldingsAllocation
   * of the portfolio.
   *
   * Emits portfolio allocation event, the first time **real** portolio is allocated
   */
  public static async createOrUpdatePortfolioAllocation(
    portfolio: PortfolioDocument,
    allocation: any,
    allocationCreationFlow?: AllocationCreationFlowEnum
  ): Promise<PortfolioDocument> {
    // filter out assets with zero weight
    const filteredAllocation = Object.fromEntries(
      Object.entries(allocation).filter(([, percentage]: [string, any]) => parseFloat(percentage) > 0)
    );

    const initialHoldingsAllocation = PortfolioUtil.allocationToHoldingsPercentArray(filteredAllocation);

    const portfolioData = PortfolioService.isPortfolioAllocationCreated(portfolio)
      ? { initialHoldingsAllocation }
      : // if target allocation is not set then store the flow where it was created from
        { initialHoldingsAllocation, allocationCreationFlow };
    const updatedPortfolio: PortfolioDocument = await Portfolio.findByIdAndUpdate(portfolio._id, portfolioData, {
      upsert: false,
      runValidators: true,
      setDefaultsOnInsert: true,
      new: true
    });

    // Emit portfolio allocation event, the first time **real** portolio is allocated
    if (portfolio.isReal && !PortfolioService.isPortfolioAllocationCreated(portfolio)) {
      await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
      eventEmitter.emit(events.portfolio.portfolioAllocation.eventId, portfolio.owner as UserDocument, {
        allocationCreationFlow
      });
    }

    return updatedPortfolio;
  }

  /**
   * Saves personalisation preferences for a user.
   *
   * @param portfolio
   * @param personalisationPreferences
   * @returns
   */
  public static async submitPersonalisationPreferences(
    portfolio: PortfolioDocument,
    personalisationPreferences: PersonalisationType
  ): Promise<PortfolioDocument> {
    // Validate selection of preferences
    let assetClasses = personalisationPreferences.assetClasses.filter(
      (assetClass: investmentUniverseConfig.AssetClassType) =>
        AssetClassArray.find((allowedClass) => allowedClass === assetClass)
    );
    assetClasses = assetClasses.length > 0 ? assetClasses : AssetClassArray;
    const geography = InvestmentGeographyArray.find(
      (allowedGeo) => allowedGeo === personalisationPreferences.geography
    )
      ? personalisationPreferences.geography
      : "global";
    const sectors = assetClasses.includes("equities")
      ? personalisationPreferences.sectors.filter((sector: investmentUniverseConfig.InvestmentSectorType) =>
          InvestmentSectorArray.find((allowedSector) => allowedSector === sector)
        )
      : [];
    const risk = (personalisationPreferences.risk || RISK_CONFIG.default) / RISK_CONFIG.maxLevel;

    const personalisationData = { assetClasses, geography, risk, sectors };

    return Portfolio.findByIdAndUpdate(
      portfolio._id,
      { personalisationPreferences: personalisationData },
      {
        runValidators: true,
        upsert: false
      }
    );
  }

  /**
   * We sync with WK portfolios that are Created but not yet Active in our DB
   */
  public static async syncAllWkPortfolios() {
    const createdPortfolios: PortfolioDocument[] = await Portfolio.find({
      "providers.wealthkernel.id": { $exists: true },
      "providers.wealthkernel.status": { $eq: "Created" }
    }).populate("owner");

    for (let i = 0; i < createdPortfolios.length; i++) {
      const portfolio = createdPortfolios[i];

      try {
        await PortfolioService._syncWkEntry(portfolio);
      } catch (err) {
        captureException(err);
        logger.error(`Portfolio syncing failed for user ${portfolio.owner}`, {
          module: "PortfolioService",
          method: "syncAllWkPortfolios",
          data: { userId: portfolio.owner, portfolioId: portfolio._id, error: err }
        });
      }
    }
  }

  public static async createBrokeragePortfolio(portfolio: PortfolioDocument): Promise<PortfolioDocument> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.ACCOUNT)
    ]);

    const user = portfolio.owner as UserDocument;
    const account = portfolio.account as AccountDocument;

    logger.info(`Attempting to create brokerage portfolio for user ${user._id}`, {
      module: "PortfolioService",
      method: "createBrokeragePortfolio",
      data: { userId: user._id, portfolioId: portfolio._id }
    });

    // Running some safety checks to ensure that nothing unexpected happens
    if (!user.hasPassedKyc) {
      logger.error("Attempted to create WK portfolio for unverified user", {
        module: "PortfolioService",
        method: "createBrokeragePortfolio",
        userEmail: user.email,
        data: { userId: user._id, portfolioId: portfolio._id }
      });
      throw new BadRequestError("User has not passed kyc");
    }

    const accountId = account?.providers?.wealthkernel?.id;
    if (!accountId) {
      logger.error("Attempted to create WK portfolio for user with no account id", {
        module: "PortfolioService",
        method: "createBrokeragePortfolio",
        userEmail: user.email,
        data: { userId: user._id, portfolioId: portfolio._id }
      });
      throw new BadRequestError("User's account has no wealthkernel id");
    }

    const wkPortfolioId = portfolio.providers?.wealthkernel?.id;
    const wkPortfolios = await ProviderService.getBrokerageService(user.companyEntity).retrievePortfolios(
      accountId
    );
    if (wkPortfolios.length === 0 && wkPortfolioId) {
      logger.error(`A WK portfolio id ${wkPortfolioId} exists, but WK didn't return any portfolios`, {
        module: "PortfolioService",
        method: "createBrokeragePortfolio"
      });
      throw new BadRequestError("Wealthkernel portfolio exists already");
    }

    if (wkPortfolios.length === 0) {
      // If there is no WK portfolio for this account, we want to create one and add its ID to the portfolio document.
      return await PortfolioService._createWkPortfolio(portfolio);
    } else {
      // If there is already a created WK portfolio for this account, we want to add its ID to the portfolio document.
      if (wkPortfolios.length > 1) {
        logger.error("Found more than one wealthkernel portfolios", {
          module: "PortfolioService",
          method: "createBrokeragePortfolio",
          userEmail: user.email,
          data: { partyId: user.providers.wealthkernel.id }
        });
        throw new BadRequestError("Found more than one portfolios on Wealthkernel");
      }

      const wkPortfolio = wkPortfolios[0];
      const updatedPortfolio = await Portfolio.findOneAndUpdate(
        {
          _id: portfolio?._id
        },
        {
          "providers.wealthkernel": {
            id: wkPortfolio.id,
            status: wkPortfolio.status
          }
        },
        {
          new: true
        }
      );

      // If portfolio previously had no wealthkernel id emit user verification event
      if (!wkPortfolioId) {
        eventEmitter.emit(events.user.verification.eventId, user, { emailNotification: user.isManuallyKycPassed });
        logger.info(`Wealthkernel portfolio has just been created for user ${user.email}. User is now verified.`, {
          module: "PortfolioService",
          method: "createBrokeragePortfolio"
        });
      }

      logger.warn("Portfolio has been synced", {
        module: "PortfolioService",
        method: "createBrokeragePortfolio",
        data: { partyId: user.providers.wealthkernel.id, wkPortfolio }
      });

      return updatedPortfolio;
    }
  }

  public static async isPortfolioImbalanced(portfolio: PortfolioDocument): Promise<boolean> {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
    const owner = portfolio.owner as UserDocument;

    if (!owner.hasPassedKyc) {
      return false;
    }

    const pendingRebalanceTransactions = (await TransactionService.getRebalanceTransactions({
      portfolio: portfolio._id,
      statuses: ["Pending"]
    })) as RebalanceTransactionDocument[];
    if (pendingRebalanceTransactions.length > 0) {
      return false;
    }

    const availableHoldings = await PortfolioService.getAvailableHoldings(portfolio);
    if (!availableHoldings.length) {
      return false;
    }

    const allocationDiff = await PortfolioService._getTargetAllocationDiff(owner.currency, portfolio);
    const allocationDiffValues = Object.values(allocationDiff);

    const assetWithEligibleDiffExists = allocationDiffValues.some(
      (percentage) => percentage >= MINIMUM_ASSET_ALLOCATION_DIFF
    );

    const totalDiff = allocationDiffValues
      .map((val) => new Decimal(val))
      .reduce((sum, percentage) => sum.plus(percentage), new Decimal(0));
    const averageDiff = totalDiff.div(allocationDiffValues.length).toNumber();
    const isAverageDiffEligible = averageDiff >= MINIMUM_TARGET_ALLOCATION_DIFF;

    return assetWithEligibleDiffExists || isAverageDiffEligible;
  }

  /**
   * @description Method to calculate the up-by values of the portfolio by tenor. To do that, we do the following
   * calculation:
   *
   * Up-by value between T0 and T1 = Portfolio value (T1)
   *    - Portfolio value (T0)
   *    - Total bought amount (T0, T1)
   *    - Rewarded amount (T0, T1)
   *    + Total sold amount (T0, T1)
   *    + Dividend amount (T0, T1)
   *    + Holdings charged amount (T0, T1)
   *
   * @param portfolio
   * @param transactions
   * @param requestedTenor
   * @param referenceDate
   * @returns
   */
  public static async calculateUpByValues(
    portfolio: PortfolioDocument,
    transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    },
    requestedTenor?: TenorEnum,
    referenceDate: Date = new Date(Date.now())
  ): Promise<Record<TenorEnum, number>> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.CURRENT_TICKER)
    ]);

    const user = portfolio.owner as UserDocument;

    const { dividendTransactions, rewards } = transactions;
    const matchedOrders = await OrderService.getMatchedOrdersForTransactions(user.id, [
      "AssetTransaction",
      "RebalanceTransaction",
      "ChargeTransaction"
    ]);

    // If user has requested a specific tenor, then we only want to calculate returns for that.
    const tenorsToCalculateReturnsFor = Object.values(TenorEnum).filter(
      (tenor) => !requestedTenor || requestedTenor === tenor
    );

    const portfolioCurrentValue = portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset));

    const upByValuesByTenorPromises = tenorsToCalculateReturnsFor.map(async (tenor) => {
      // If tenor is ALL_TIME, we don't filter
      if (tenor === TenorEnum.ALL_TIME) {
        const upByValue = PortfolioService._getUpByValue(
          portfolioCurrentValue,
          0,
          matchedOrders,
          dividendTransactions,
          rewards
        );

        return [tenor, upByValue];
      }

      const todayDataExist = portfolio.currentTicker && DateUtil.isToday(portfolio.currentTicker.timestamp);
      const dateAtTenorStart = TickerUtil.getDateAtTenorStart(tenor, referenceDate, todayDataExist);

      if (
        (tenor === TenorEnum.TODAY || tenor === TenorEnum.ONE_DAY) &&
        portfolio.currentTicker.timestamp < dateAtTenorStart
      ) {
        return [tenor, 0];
      }

      // Baseline should be the previous day's close before the tenor start
      const previousDayCloseTicker = await DailyTickerService.getLatestDailyPortfolioTickerBeforeDate(
        portfolio.id,
        dateAtTenorStart
      );
      const portfolioStartingValue = previousDayCloseTicker
        ? (previousDayCloseTicker?.pricePerCurrency?.[user.currency] ?? 0)
        : 0;

      const upByValue = PortfolioService._getUpByValue(
        portfolioCurrentValue,
        portfolioStartingValue,
        matchedOrders.filter((order) => order.filledAt >= dateAtTenorStart),
        dividendTransactions.filter((dividend) => dividend.createdAt >= dateAtTenorStart),
        // There are only 22 rewards without updatedAt field, but won't affect the calculations
        // because the latest one is at mid of 2022.
        rewards.filter((reward) => reward.updatedAt >= dateAtTenorStart)
      );

      return [tenor, upByValue];
    });

    return Object.fromEntries(await Promise.all(upByValuesByTenorPromises));
  }

  /**
   * @description Returns up by value for the portfolio holding of the given asset.
   */
  public static async getHoldingUpByValue(
    portfolio: PortfolioDocument,
    holding: HoldingsType,
    transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    }
  ): Promise<number> {
    const userId = portfolio.populated("owner") ? portfolio.owner.id : portfolio.owner;
    const { dividendTransactions, rewards } = transactions;
    const allTransactionOrders = await OrderService.getMatchedOrdersForTransactions(userId, [
      "AssetTransaction",
      "RebalanceTransaction",
      "ChargeTransaction"
    ]);

    const { quantity, asset, assetCommonId } = holding;

    const allAssetIsinsSet = new Set(InvestmentUniverseUtil.getIsinActiveAndDeprecated(assetCommonId));
    const holdingValue = new Decimal(quantity).mul(asset.currentTicker.getPrice(portfolio.currency)).toNumber();
    const assetOrders = allTransactionOrders.filter((order) => allAssetIsinsSet.has(order.isin));
    const assetDividends = dividendTransactions.filter((dividend) => dividend.asset === assetCommonId);
    const assetRewards = rewards.filter((reward) => reward.asset == assetCommonId);

    const { eligibleOrders, eligibleDividends, eligibleRewards } =
      PortfolioService._filterOutActivityBeforeLastFullSell(assetOrders, assetDividends, assetRewards);

    return PortfolioService._getUpByValue(holdingValue, 0, eligibleOrders, eligibleDividends, eligibleRewards);
  }

  /**
   * @description Calculates the MWRR (money-weighted rate of return) per tenor (1 week, 1 month, etc.) for a given real portfolio.
   * @param portfolio
   * @param transactions
   * @param requestedTenor
   * @param referenceDate
   * @returns
   */
  public static async getPortfolioMWRR(
    portfolio: PortfolioDocument,
    transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    },
    requestedTenor?: TenorEnum,
    referenceDate: Date = new Date(Date.now())
  ): Promise<PartialRecord<TenorEnum, number>> {
    if (!portfolio.isReal) {
      logger.error(
        `Attempting to calculate returns for portfolio ${portfolio.id}, but portfolio is ${portfolio.mode}`,
        {
          module: "PortfolioService",
          method: "getPortfolioMWRR"
        }
      );
      throw new InternalServerError("Portfolio returns can only be calculated for real portfolios");
    }

    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.CURRENT_TICKER),
      PortfolioUtil.populatePortfolioAssetsIfNotAlreadyPopulated(portfolio)
    ]);

    const user = portfolio.owner as UserDocument;

    const { dividendTransactions, rewards } = transactions;

    const matchedOrders = await OrderService.getMatchedOrdersForTransactions(user.id, [
      "AssetTransaction",
      "RebalanceTransaction",
      "ChargeTransaction"
    ]);

    const flows: CashFlow[] = [
      ...matchedOrders.map((order) => {
        const amount = order.amountForReturnsAndUpBy;
        return {
          amount: order.side == "Buy" ? -amount : amount,
          date: order.filledAt
        };
      }),
      ...rewards.map((reward) => ({
        amount: -reward.consideration.amount,
        date: new Date(reward.updatedAt ?? reward.createdAt)
      })),
      ...dividendTransactions.map((dividend) => ({
        amount: dividend.consideration.amount,
        date: new Date(dividend.createdAt)
      }))
    ].sort((flow1, flow2) => flow1.date.getTime() - flow2.date.getTime());

    const portfolioValue = Decimal.mul(
      portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset)),
      100
    ).toNumber();

    // Add portfolio value as last flow with current date
    flows.push({
      amount: portfolioValue,
      date: new Date(portfolio.currentTicker.timestamp)
    });

    // If user has requested a specific tenor, then we only want to calculate returns for that.
    const tenorsToCalculateReturnsFor = Object.values(TenorEnum).filter(
      (tenor) => !requestedTenor || requestedTenor === tenor
    );

    const returnsByTenorPromises = tenorsToCalculateReturnsFor.map(async (tenor) => {
      // If tenor is ALL_TIME, we include all flows in the calculation.
      if (tenor === TenorEnum.ALL_TIME) {
        return [tenor, PortfolioService._xirr(flows, portfolio.id)];
      }

      const todayDataExist = portfolio.currentTicker && DateUtil.isToday(portfolio.currentTicker.timestamp);
      const dateAtTenorStart = TickerUtil.getDateAtTenorStart(tenor, new Date(Date.now()), todayDataExist);

      if (
        (tenor === TenorEnum.TODAY || tenor === TenorEnum.ONE_DAY) &&
        portfolio.currentTicker.timestamp < dateAtTenorStart
      ) {
        return [tenor, 0];
      }

      const flowsWithinPeriod = flows.filter((flow) => flow.date >= dateAtTenorStart);

      // Use the previous day's close before the tenor start as the baseline
      const previousDayCloseTickerForMwrr = await DailyTickerService.getLatestDailyPortfolioTickerBeforeDate(
        portfolio.id,
        dateAtTenorStart
      );
      const portfolioStartingValueInCents = previousDayCloseTickerForMwrr
        ? Decimal.mul(previousDayCloseTickerForMwrr.pricePerCurrency?.[user.currency] ?? 0, 100).toNumber()
        : 0;

      if (portfolioStartingValueInCents > 0) {
        flowsWithinPeriod.unshift({
          amount: -portfolioStartingValueInCents,
          date: new Date(previousDayCloseTickerForMwrr!.date) // If portfolio starting value is non-zero, we know there is an existing ticker
        });
      }

      return [tenor, PortfolioService._xirr(flowsWithinPeriod, portfolio.id) ?? 0];
    });

    return Object.fromEntries(await Promise.all(returnsByTenorPromises));
  }

  /**
   * @description This method calculates the portfolio returns using a hybrid method of MWRR & TWR.
   * More specifically it is based on
   *
   * daily_return = (live - last_cached)/last_cached
   * portfolio_return = cached_mwrr*(1+daily_return)
   *
   * The steps are:
   * 1. Get cached MWRR
   * 2. If cached MWRR fails to be retrieved or doesn't exist => calculate it & cache it
   * 3. Get return for portfolio when comparing with cached portfolio value
   * 4. Calculate TWR using cached MWRR and daily return
   *
   */
  public static async getPortfolioReturnsAllTenors(
    portfolio: PortfolioDocument,
    transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    }
  ): Promise<PartialRecord<TenorEnum, number>> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.CURRENT_TICKER)
    ]);

    // 1. Get mwrr & cached portfolio value from cache
    const [cachedMwrrByTenor, cachedPortfolioValueAtMwrr] = await Promise.all([
      RedisClientService.Instance.get<Record<TenorEnum, number>>(`portfolios:mwrr:${portfolio.id}`),
      RedisClientService.Instance.get<number>(`portfolios:value_at_mwrr:${portfolio.id}`)
    ]);

    // 2. If cache is empty or cannot be retrieved calculate mwrr & store in cache
    let mwrrByTenor: PartialRecord<TenorEnum, number>;
    let initialPortfolioValue: number;
    if (!cachedMwrrByTenor) {
      mwrrByTenor = await PortfolioService.getPortfolioMWRR(portfolio, transactions);
      initialPortfolioValue = portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset));

      // we do not have to 'await' here - this is intentional
      RedisClientService.Instance.set(`portfolios:mwrr:${portfolio.id}`, mwrrByTenor);
      RedisClientService.Instance.set(
        `portfolios:value_at_mwrr:${portfolio.id}`,
        portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset)) ?? 0
      );
    } else {
      mwrrByTenor = cachedMwrrByTenor;
      initialPortfolioValue = cachedPortfolioValueAtMwrr ?? 0;
    }

    // 3. Find portfolio return when comparing with cached portfolio value
    const dailyReturn = PortfolioUtil.getReturns({
      startValue: new Decimal(initialPortfolioValue),
      endValue: new Decimal(portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset)))
    });

    // 4. Calculate final value using cached MWRR & daily return: (1+cached_mwrr)*(1+daily_return)-1
    const twrByTenor = Object.fromEntries(
      Object.entries(mwrrByTenor).map(([tenor, mwrr]: [TenorEnum, number]) => {
        if (!mwrr) {
          addBreadcrumb({
            type: "default",
            category: "PortfolioService.getPortfolioReturnsAllTenors",
            level: "info",
            data: {
              holdings: portfolio.holdings,
              mwrrByTenor,
              dailyReturn
            }
          });
          logger.warn(`Null MWRR in portfolio ${portfolio._id} for tenor ${tenor}`, {
            module: "PortfolioService",
            method: "getPortfolioReturnsAllTenors",
            data: {
              holdings: portfolio.holdings.map(({ assetCommonId, quantity }) => [assetCommonId, quantity]),
              mwrrByTenor,
              dailyReturn
            }
          });
        }

        const twr = Decimal.add(1, dailyReturn)
          .mul(Decimal.add(1, mwrr ?? 0))
          .minus(1)
          .toNumber();
        return [tenor, twr];
      })
    ) as Record<TenorEnum, number>;

    return twrByTenor;
  }

  public static async updatePortfolioHoldings(
    portfolioId: string,
    holdingUpdates: PortfolioHoldingsUpdates[],
    options: {
      investmentProductDictToUse?: { [isin: string]: InvestmentProductDocument };
      session?: mongoose.ClientSession;
    } = {}
  ) {
    const investmentProductsDictByIsin = options.investmentProductDictToUse
      ? options.investmentProductDictToUse
      : await InvestmentProductService.getInvestmentProductsDict("isin", true);

    const portfolio = await Portfolio.findById(portfolioId, null, { session: options?.session }).populate([
      { path: "owner" },
      { path: "holdings.asset" }
    ]);

    const newQuantitiesDict: Record<string, Decimal> = Object.fromEntries(
      portfolio.holdings.map(({ asset, quantity }) => [ASSET_CONFIG[asset.commonId].isin, new Decimal(quantity)])
    );

    holdingUpdates.forEach(({ side, isin, quantity }) => {
      if (side === "Buy") {
        if (newQuantitiesDict[isin]) {
          // holding exists -> increase quantity
          newQuantitiesDict[isin] = newQuantitiesDict[isin].plus(quantity);
        } else {
          // holding doesn't exist -> set quantity
          newQuantitiesDict[isin] = new Decimal(quantity);
        }
      } else if (side === "Sell") {
        newQuantitiesDict[isin] = newQuantitiesDict[isin].sub(quantity);
      }
    });

    const newHoldings: HoldingsType[] = Object.entries(newQuantitiesDict)
      .filter(([, quantity]) => quantity.gt(0))
      .map(([isin, quantity]) => {
        const asset = investmentProductsDictByIsin[isin];
        const assetCommonId = asset.commonId;
        return { asset, assetCommonId, quantity: quantity.toNumber() };
      });

    logger.info(`Updating ${portfolio.id} value & holdings`, {
      module: "PortfolioService",
      method: "updatePortfolioHoldings",
      data: {
        oldHoldings: portfolio.holdings.map(({ assetCommonId, quantity }) => [assetCommonId, quantity]),
        newHoldings: newHoldings.map(({ assetCommonId, quantity }) => [assetCommonId, quantity])
      }
    });

    const updatedPortfolio = await Portfolio.findOneAndUpdate(
      { _id: portfolio._id },
      {
        holdings: newHoldings
      },
      {
        runValidators: true,
        upsert: false,
        setDefaultsOnInsert: true,
        new: true,
        session: options?.session
      }
    );

    // Set the portfolio intra-day ticker **after** updating the portfolio holdings
    // => it's important the update in the ticker to take place after the portfolio holdings are updated
    // otherwise the price will be calculated on the old holdings.
    const investmentProductsDictByCommonId = Object.fromEntries(
      Object.values(investmentProductsDictByIsin).map((investmentProduct) => [
        investmentProduct.commonId,
        investmentProduct
      ])
    ) as Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>;
    await IntraDayTickerService.setIntraDayPortfolioTicker(updatedPortfolio, investmentProductsDictByCommonId, {
      session: options?.session
    });

    const user = portfolio.owner as UserDocument;
    const cash = portfolio.cash[user.currency].available;
    if (portfolio.holdings.length === 0 && cash === 0) {
      analytics.identify(user, { cash: 0, portfolioValue: 0 }, { All: false, Mixpanel: true });
    }
  }

  /**
   * Returns the user's updated gifted holdings. This is called when a buy portfolio / buy asset that got created using
   * a gift got settled. In this case, we want to add the assets bought in the gifted holdings.
   * @param portfolioId
   * @param orders
   * @param giftId
   * @param options
   */
  public static async updatePortfolioGiftedHoldings(
    portfolioId: string,
    orders: OrderDocument[],
    giftId: string,
    options: {
      session?: mongoose.ClientSession;
    } = {}
  ) {
    const portfolio = await Portfolio.findById(portfolioId, null, { session: options?.session });

    const gift = await GiftService.getGift(giftId);
    const giftedHoldings = portfolio.giftedHoldings;

    orders.forEach(({ side, quantity, commonId }) => {
      if (side === "Buy") {
        const currentForCommonId = giftedHoldings.get(commonId as investmentUniverseConfig.AssetType) ?? [];
        giftedHoldings.set(
          commonId as investmentUniverseConfig.AssetType,
          currentForCommonId.concat([
            {
              unrestrictedAt: new Date(
                gift.createdAt.getTime() + RESTRICTED_HOLDING_PERIOD_DAYS * 24 * 60 * 60 * 1000
              ),
              createdAt: new Date(),
              quantity: quantity
            }
          ])
        );
      } else if (side === "Sell") {
        throw new Error("We should not be updating gifted holdings with sell orders...");
      }
    });

    await Portfolio.findOneAndUpdate(
      { _id: portfolio._id },
      {
        giftedHoldings
      },
      {
        runValidators: true,
        upsert: false,
        setDefaultsOnInsert: true,
        session: options?.session
      }
    );

    logger.info(`Updated ${portfolio._id} gifted holdings`, {
      module: "PortfolioService",
      method: "updatePortfolioGiftedHoldings",
      data: {
        portfolioId: portfolio.id,
        giftId
      }
    });
  }

  /**
   * Charges the gifted holdings of the user based on the orders given. We only subtract from the gifted holdings
   * if the orders bring the holdings of the user below their available holdings.
   * @param portfolio
   * @param orders
   */
  public static async conditionallyUpdateGiftedHoldingsOnUnavailableHoldings(
    portfolio: PortfolioDocument,
    orders: OrderDocument[]
  ) {
    const investmentProductsDict = await InvestmentProductService.getInvestmentProductsDict("isin", false);

    const giftedHoldings = portfolio.giftedHoldings;

    const availableHoldings = await PortfolioService.getAvailableHoldings(portfolio);
    const availableHoldingsDict = Object.fromEntries(
      availableHoldings.map((holding) => [ASSET_CONFIG[holding.assetCommonId].isin, holding])
    );

    orders.forEach(({ side, isin, quantity }) => {
      if (side === "Buy") {
        throw new Error("We should not be charging gifted holdings with buy orders...");
      } else if (side === "Sell") {
        const availableQuantity = availableHoldingsDict[isin]?.quantity ?? 0;
        const restrictedQuantities = (giftedHoldings.get(investmentProductsDict[isin].commonId) ?? [])
          .filter(({ unrestrictedAt }) => unrestrictedAt > new Date()) // Only get restricted holdings
          .sort((a, b) => a.unrestrictedAt.getTime() - b.unrestrictedAt.getTime());

        if (new Decimal(availableQuantity).lessThan(quantity)) {
          // Order quantity is bigger than available quantity, therefore we want to subtract that difference from the
          // gifted holdings.
          let remainingRestrictedQuantity = Decimal.sub(quantity, availableQuantity);

          const updatedRestrictedQuantities = restrictedQuantities
            .map((holding) => {
              const amountToSubtract = Decimal.min(holding.quantity, remainingRestrictedQuantity);
              const updatedQuantity = Decimal.sub(holding.quantity, amountToSubtract);

              remainingRestrictedQuantity = remainingRestrictedQuantity.sub(amountToSubtract);

              return {
                unrestrictedAt: holding.unrestrictedAt,
                createdAt: holding.createdAt,
                quantity: updatedQuantity.toNumber()
              };
            })
            .filter((restrictedQuantity) => restrictedQuantity.quantity > 0);

          if (updatedRestrictedQuantities.length === 0) {
            giftedHoldings.delete(investmentProductsDict[isin].commonId);
          } else {
            giftedHoldings.set(investmentProductsDict[isin].commonId, updatedRestrictedQuantities);
          }
        }
      }
    });

    await Portfolio.findOneAndUpdate(
      { _id: portfolio._id },
      {
        giftedHoldings
      },
      {
        runValidators: true,
        upsert: false,
        setDefaultsOnInsert: true
      }
    );

    logger.info(`Charged ${portfolio._id} gifted holdings`, {
      module: "PortfolioService",
      method: "conditionallyUpdateGiftedHoldingsOnUnavailableHoldings",
      data: {
        portfolioId: portfolio.id
      }
    });
  }

  /**
   * @description This method gives the returns of an asset.
   * It primarily uses the MWRR method and if it fails it uses the
   * average price per share based method.
   */
  public static async getAssetReturns(
    portfolio: PortfolioDocument,
    holding: HoldingsType,
    transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    }
  ): Promise<number> {
    let assetReturns = await PortfolioService._getAssetReturnsMWRR(portfolio, holding, transactions);
    if (!assetReturns) {
      // MWRR may not be calculated - fallback to this method which is less accurate
      // though in some scenarios.
      assetReturns = await PortfolioService._getAssetReturnsAveragePricePerShare(portfolio, holding.asset, {
        rewards: transactions.rewards
      });
    }
    return assetReturns;
  }

  public static async submitOrder(
    portfolioId: string,
    commonId: investmentUniverseConfig.AssetType,
    submittedOrder: PendingOrderType,
    options: {
      pendingDeposit?: DepositCashTransactionDocument;
      pendingGift?: GiftDocument;
      executeEtfOrdersInRealtime: boolean;
    }
  ): Promise<AssetTransactionDocument> {
    const [portfolio, investmentProduct] = await Promise.all([
      PortfolioService.getPortfolio(portfolioId, true),
      InvestmentProductService.getInvestmentProduct(commonId, true)
    ]);
    const user = portfolio.owner as UserDocument;

    if (!portfolio.isReal) {
      throw new BadRequestError("Operation not allowed for non-real portfolios");
    }

    if (options?.pendingDeposit && options?.pendingGift)
      throw new BadRequestError("Both gift and deposit were used for buying asset");

    if (!investmentProduct) {
      throw new BadRequestError(`Investment product ${commonId} is not available.`);
    }

    const isETF = ASSET_CONFIG[commonId].category === "etf";
    if (isETF) {
      if (options?.executeEtfOrdersInRealtime && !user.isRealtimeETFExecutionEnabled) {
        throw new BadRequestError(`User ${user.id} cannot submit ETF orders as real-time!`);
      }

      if (
        !options?.executeEtfOrdersInRealtime &&
        user.isRealtimeETFExecutionEnabled &&
        submittedOrder.side === "sell"
      ) {
        throw new BadRequestError(
          `User ${user.id} is trying to do an ETF sell as aggregate which is not allowed!`
        );
      }
    }

    const [availableHoldings, subscription] = await Promise.all([
      PortfolioService.getAvailableHoldings(portfolio),
      SubscriptionService.getSubscription(user.id)
    ]);
    const holdingsDict = Object.fromEntries(availableHoldings.map((holding) => [holding.assetCommonId, holding]));

    // 1. Verify order passes our investment criteria
    const filteredPendingOrders = PortfolioUtil.checkSubmittedOrder(
      user,
      commonId,
      submittedOrder,
      holdingsDict,
      investmentProduct
    );

    if (!filteredPendingOrders) {
      throw new BadRequestError("The submitted order does not pass our investment criteria");
    }

    const availableCash = portfolio?.cash?.[user.currency]?.available ?? 0;
    if (!options?.pendingGift && !options?.pendingDeposit) {
      if (availableCash < submittedOrder.money) {
        throw new BadRequestError(
          `You have placed buy orders of value ${CurrencyUtil.formatCurrency(
            submittedOrder.money,
            user.currency,
            ConfigUtil.getDefaultUserLocale(user.residencyCountry)
          )} but you only have ${CurrencyUtil.formatCurrency(
            availableCash,
            user.currency,
            ConfigUtil.getDefaultUserLocale(user.residencyCountry)
          )}`
        );
      }
    }

    if (
      submittedOrder.side === "sell" &&
      (!holdingsDict[commonId] || submittedOrder.quantity > holdingsDict[commonId].quantity)
    ) {
      throw new BadRequestError("You have placed a sell order with quantity larger than what you hold");
    }

    const orderData: Omit<OrderDTOInterface, "transaction"> = OrderService.getSingleOrderToCreate(submittedOrder, {
      investmentProduct,
      userCurrency: user.currency,
      userCompanyEntity: user.companyEntity,
      executeEtfOrdersInRealtime: options.executeEtfOrdersInRealtime
    });
    let orderDataWithFees = orderData;
    if (orderData.side === "Buy") {
      const ordersWithFees = OrderService.applyFeesToOrders(subscription.plan, [orderData], user.currency, {
        [ASSET_CONFIG[investmentProduct.commonId].isin]: investmentProduct
      });
      orderDataWithFees = ordersWithFees[0];
    }

    const executionWindow = ExecutionWindowUtil.getAssetTransactionExecutionWindow(
      [orderDataWithFees],
      user.currency,
      {
        investmentProducts: { [ASSET_CONFIG[investmentProduct.commonId].isin]: investmentProduct }
      }
    );

    // 2. Create asset transaction
    const assetTransactionData: Omit<AssetTransactionInterfaceDTO, "createdAt" | "originalInvestmentAmount"> = {
      consideration: {
        currency: user.currency
      },
      owner: user.id,
      portfolio: portfolio.id,
      portfolioTransactionCategory: "update" as PortfolioTransactionCategoryType,
      status: PortfolioService._getInitialTransactionStatus(options?.pendingDeposit, options?.pendingGift),
      executionWindow
    };

    if (options?.pendingDeposit) {
      assetTransactionData.pendingDeposit = options.pendingDeposit._id;
    } else if (options?.pendingGift) {
      assetTransactionData.pendingGift = options.pendingGift._id;
    }

    const assetTransaction = await TransactionService.createAssetTransaction(assetTransactionData);
    const order = await OrderService.createDbOrder({
      ...orderDataWithFees,
      transaction: assetTransaction.id
    });

    // 4. And link it to the parent asset transaction
    const [updatedAssetTransaction, buyFxRates, sellFxRates, cashbackAmount] = await Promise.all([
      AssetTransaction.findOneAndUpdate(
        { _id: assetTransaction.id },
        { orders: [order.id], fees: order.fees },
        {
          runValidators: true,
          new: true
        }
      ).populate("orders pendingDeposit pendingGift"),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, subscription.plan, FXSpreadSide.BUY),
      TransactionService.getAllForeignCurrencyRatesWithSpread(user, subscription.plan, FXSpreadSide.SELL),
      TransactionService.getCashbackAmount(
        order.consideration.originalAmount ?? order.consideration.amount ?? 0,
        user
      )
    ]);

    let cashback: CashbackTransactionDocument;
    if (order.side === "Buy" && cashbackAmount > 0) {
      const cashbackData: CashbackTransactionDTOInterface = {
        consideration: {
          amount: cashbackAmount,
          currency: user.currency
        },
        owner: portfolio.owner as mongoose.Types.ObjectId,
        portfolio: portfolio.id,
        cashbackMonth: DateUtil.getYearAndMonth(new Date()),
        linkedAssetTransaction: assetTransaction.id,
        createdAt: new Date(),
        price: subscription.price,
        deposit: {
          activeProviders: ProviderService.getProviders(user.companyEntity, [ProviderScopeEnum.BROKERAGE])
        }
      };

      cashback = await new CashbackTransaction(cashbackData).save();
    }

    const transactionSideEffectPromises = [];

    // 5. Update portfolio cash if order is made using the cash balance of the user
    if (!options?.pendingGift && !options?.pendingDeposit) {
      if (order.side === "Buy") {
        transactionSideEffectPromises.push(
          PortfolioService.updateCashAvailability(portfolio.id, user.currency, -submittedOrder.money, {
            available: true,
            settled: true
          })
        );
      }

      if (portfolio.holdings.length === 0 && !user.hasConvertedPortfolio) {
        transactionSideEffectPromises.push(
          UserService.updateUser(user.id, {
            portfolioConversionStatus: "inProgress"
          })
        );
      }

      transactionSideEffectPromises.push(
        PortfolioService._emitInvestmentCreationEvent(
          updatedAssetTransaction,
          {
            [order.isin]: investmentProduct
          },
          cashback
        ),
        OrderService.submitRealtimeOrdersSafely([order], portfolio)
      );
    }

    await Promise.all([
      ...transactionSideEffectPromises,
      DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION)
    ]);

    return TransactionService.fillClientDisplayFields(
      user,
      updatedAssetTransaction,
      { [order.isin]: investmentProduct },
      {
        BUY: buyFxRates,
        SELL: sellFxRates
      },
      {
        displayAmount: true,
        displayQuantity: true,
        executionWindow: true,
        displayExchangeRate: true,
        isCancellable: true,
        estimatedRealTimeCommission: true
      }
    ) as AssetTransactionDocument;
  }

  public static async getGeneralInvestmentPortfolio(
    user: UserDocument,
    populateTickers = false
  ): Promise<PortfolioDocument> {
    if (!user.populated("generalInvestmentAccount portfolios")) {
      await user.populate("generalInvestmentAccount portfolios");
    }

    const portfolio = user.portfolios
      .filter((portfolio) => portfolio.mode === PortfolioModeEnum.REAL)
      .find((portfolio) => portfolio.account.toString() === user.generalInvestmentAccount.id);

    if (portfolio && populateTickers) {
      await portfolio.populate([
        { path: "currentTicker" },
        { path: "initialHoldingsAllocation.asset" },
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ]);
    }

    return portfolio;
  }

  public static hasWealthkernelId(portfolio: PortfolioDocument): boolean {
    return (
      portfolio.providers?.wealthkernel?.id !== null &&
      portfolio.providers?.wealthkernel?.id !== undefined &&
      portfolio.providers?.wealthkernel?.id !== ""
    );
  }

  /**
   * @description Method to check whether a portfolio allocation has been setup. That means that the
   * initial allocation has a length.
   *
   * @param portfolio The portfolio db document to check.
   * @returns A boolean that indicates whether a portfolio has allocation defined.
   */
  public static isPortfolioAllocationCreated(portfolio: PortfolioDocument): boolean {
    return portfolio?.initialHoldingsAllocation?.length > 0;
  }

  /**
   * Returns holdings excluding any quantities in pending sell orders and restricted rewards.
   * @param portfolio
   */
  public static async getAvailableHoldings(portfolio: PortfolioDocument): Promise<HoldingsType[]> {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);

    const holdingsExcludingPendingOrders = await PortfolioService.getHoldingsExcludingPendingOrders(portfolio);

    const portfolioOwner = portfolio.owner as UserDocument;
    const activeRewards = await Reward.find({
      targetUser: portfolioOwner.id,
      unrestrictedAt: { $gt: new Date() },
      accepted: true
    });

    return holdingsExcludingPendingOrders
      .map(({ asset, assetCommonId, quantity }) => {
        const restrictedQuantity = PortfolioService._getAssetRestrictedQuantity(
          portfolio,
          assetCommonId,
          activeRewards
        );
        const availableQuantity = new Decimal(quantity).minus(restrictedQuantity);

        // If for a given asset, we have a restricted reward but also have sold part of that reward as part of a charge,
        // then the above calculation would return a negative value. Instead of returning that negative value, we want to
        // return 0 for that asset so that users of this method will not create any sell orders for it.
        const positiveAvailableQuantity = Decimal.max(availableQuantity, 0).toNumber();

        return { asset, assetCommonId, quantity: positiveAvailableQuantity };
      })
      .filter(({ quantity }) => quantity > 0);
  }

  /**
   * Finds pending orders for portfolio owner and subtract their pending quantities from holdings quantities.
   * This is needed in order to create orders for the remaining holding's quantity and not for the whole quantity (which is wrong)
   * @param portfolio
   */
  public static async getHoldingsExcludingPendingOrders(portfolio: PortfolioDocument): Promise<HoldingsType[]> {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);

    const portfolioOwner = portfolio.owner as UserDocument;

    const pendingTransactions = (await TransactionService.getPendingTransactionsWithOrders(portfolioOwner.id)) as (
      | AssetTransactionDocument
      | RebalanceTransactionDocument
      | ChargeTransactionDocument
    )[];

    const pendingOrders = pendingTransactions.flatMap(({ orders }) => orders).filter(({ side }) => side == "Sell");

    const dbPendingOrdersDict: { [key in string]: OrderDocument[] } = {};
    pendingOrders.forEach((pendingOrder) => {
      if (dbPendingOrdersDict[pendingOrder.isin] == null) {
        dbPendingOrdersDict[pendingOrder.isin] = [pendingOrder];
      } else {
        dbPendingOrdersDict[pendingOrder.isin].push(pendingOrder);
      }
    });

    return portfolio.holdings.map(({ asset, assetCommonId, quantity }) => {
      const availableQuantity = new Decimal(quantity)
        // subtract available quantities found from holdings quantities
        .minus(
          dbPendingOrdersDict[ASSET_CONFIG[asset.commonId].isin]
            ?.map((pendingOrder) => new Decimal(pendingOrder.quantity))
            .reduce((sum, quantity) => sum.plus(quantity), new Decimal(0)) ?? 0
        )
        .toNumber();
      return { asset, assetCommonId, quantity: availableQuantity };
    });
  }

  public static async getAssetRestrictionDetails(
    portfolio: PortfolioDocument,
    assetCommonId: investmentUniverseConfig.AssetType
  ): Promise<{
    hasRestrictedQuantity: boolean;
    assetCommonId: investmentUniverseConfig.AssetType;
    restrictedQuantity: number;
  }> {
    const ownerId = portfolio.populated("owner")
      ? (portfolio.owner as UserDocument).id
      : portfolio.owner.toString();

    const activeRewards = await Reward.find({
      targetUser: ownerId,
      unrestrictedAt: { $gt: new Date() },
      accepted: true,
      asset: assetCommonId
    });

    const restrictedQuantity = PortfolioService._getAssetRestrictedQuantity(
      portfolio,
      assetCommonId,
      activeRewards
    );

    return {
      assetCommonId,
      restrictedQuantity,
      hasRestrictedQuantity: restrictedQuantity > 0
    };
  }

  public static async getRestrictedHoldings(portfolio: PortfolioDocument): Promise<{
    hasRestrictedQuantity: boolean;
    restrictedAssets: { assetCommonId: investmentUniverseConfig.AssetType; restrictedQuantity: number }[];
  }> {
    portfolio = await PortfolioService.populateTickersAndOwner(portfolio, { initialHoldingsAllocation: false });

    const [holdingsExcludingPendingOrders, activeRewards] = await Promise.all([
      PortfolioService.getHoldingsExcludingPendingOrders(portfolio),
      Reward.find({
        targetUser: portfolio.owner.id,
        unrestrictedAt: { $gt: new Date() },
        accepted: true
      })
    ]);

    const restrictedAssets = holdingsExcludingPendingOrders
      .map(({ assetCommonId }) => {
        const restrictedQuantity = PortfolioService._getAssetRestrictedQuantity(
          portfolio,
          assetCommonId,
          activeRewards
        );

        return { assetCommonId, restrictedQuantity };
      })
      .filter(({ restrictedQuantity }) => restrictedQuantity > 0);

    return {
      hasRestrictedQuantity: restrictedAssets.length > 0,
      restrictedAssets
    };
  }

  public static async createGeneralInvestmentPortfolio(user: UserDocument): Promise<PortfolioDocument> {
    await user.populate("generalInvestmentAccount");
    const realPortfolioData: PortfolioDTOInterface = {
      account: user.generalInvestmentAccount._id,
      holdings: [] as HoldingsType[],
      mode: PortfolioModeEnum.REAL,
      currency: user.currency,
      owner: user._id,
      name: "General Investment Portfolio",
      initialHoldingsAllocation: []
    };

    if (user.residencyCountry) {
      realPortfolioData.activeProviders = ProviderService.getProviders(user.companyEntity, [
        ProviderScopeEnum.BROKERAGE
      ]);
    }

    return PortfolioService._createPortfolio(realPortfolioData);
  }

  public static async updateActiveProviders(
    portfolio: PortfolioDocument,
    activeProviders: ProviderEnum[]
  ): Promise<void> {
    await portfolio.updateOne({
      activeProviders
    });
  }

  /**
   * @param portfolio
   * @param savingsProductId
   * @param orderAmount in GBP/EUR/USD, not in cents
   *
   * @param options
   * @description
   * Topup savings product with the given order amount.
   * Payment methods:
   * - Cash balance
   * - Pending deposit
   * - Dividend (Cash is already sitting in the WK portfolio)
   */
  public static async topupSavings(
    portfolio: PortfolioDocument,
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    orderAmount: number,
    options?: {
      linkedAutomation?: AutomationDocument;
      pendingDeposit?: DepositCashTransactionDocument;
      dividend?: SavingsDividendTransactionDocument;
    }
  ): Promise<SavingsTopupTransactionDocument> {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
    const user = portfolio.owner as UserDocument;

    // 1. Verify order passes our investment criteria
    const isPaidWithCashBalance = !options?.pendingDeposit && !options?.dividend;
    if (isPaidWithCashBalance) {
      const availableCash = new Decimal(portfolio?.cash?.[user.currency]?.available ?? 0).toNumber();
      if (availableCash < orderAmount) {
        throw new BadRequestError(
          `You have placed a buy order of value ${CurrencyUtil.formatCurrency(
            orderAmount,
            user.currency,
            ConfigUtil.getDefaultUserLocale(user.residencyCountry)
          )} but you only have ${CurrencyUtil.formatCurrency(
            availableCash,
            user.currency,
            ConfigUtil.getDefaultUserLocale(user.residencyCountry)
          )}`
        );
      }
    }

    // 2. Create savings topup transaction
    const orderAmountInCents = Decimal.mul(orderAmount, 100).toNumber();
    const savingsTopupTransactionData: Omit<SavingsTopupTransactionDTOInterface, "createdAt"> = {
      consideration: {
        currency: user.currency,
        amount: orderAmountInCents
      },
      owner: user.id,
      portfolio: portfolio.id,
      status: PortfolioService._getInitialTransactionStatus(options?.pendingDeposit),
      savingsProduct: savingsProductId
    };

    if (options?.pendingDeposit) {
      savingsTopupTransactionData.pendingDeposit = options.pendingDeposit._id;
    }
    if (options?.linkedAutomation) {
      savingsTopupTransactionData.linkedAutomation = options.linkedAutomation._id;
    }

    const savingsTopupTransaction =
      await TransactionService.createSavingsTopupTransaction(savingsTopupTransactionData);

    logger.info(
      `Created savings topup transaction ${savingsTopupTransaction.id} with ${savingsTopupTransaction.status} status for ${user.id} user`,
      {
        module: "PortfolioService",
        method: "topupSavings"
      }
    );

    // 3. Update portfolio cash if order is made using the cash balance of the user
    if (isPaidWithCashBalance) {
      await PortfolioService.updateCashAvailability(portfolio.id, user.currency, -orderAmount, {
        available: true,
        settled: true
      });
    }

    // 4. Emit event unless top-up is a one-off 1-step savings top-up.
    if (!options?.pendingDeposit || options?.linkedAutomation) {
      PortfolioService._emitSavingsTransactionCreationEvent(user, {
        savingsProductId,
        amount: Decimal.div(savingsTopupTransaction.consideration.amount, 100).toNumber(),
        currency: savingsTopupTransaction.consideration.currency,
        side: "buy",
        repeating: !!savingsTopupTransaction.linkedAutomation
      });
    }

    // 5. Attempt to fill the topup
    await PortfolioService._fillSavingsTopup(savingsTopupTransaction);

    return savingsTopupTransaction;
  }

  public static async withdrawAllSavings(
    portfolio: PortfolioDocument,
    options?: {
      linkedUserDataRequest?: UserDataRequestDocument;
    }
  ): Promise<void> {
    const savingsToWithdraw = Array.from(portfolio.savings.entries()).filter(
      ([, savingsHolding]) => savingsHolding.amount > 0
    );

    await Promise.all(
      savingsToWithdraw.map(([savingsProductId, savingsHolding]) => {
        return PortfolioService.withdrawSavings(
          portfolio,
          savingsProductId,
          Decimal.div(savingsHolding.amount, 100).toNumber(),
          options
        );
      })
    );
  }

  public static async withdrawSavings(
    portfolio: PortfolioDocument,
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    orderAmount: number,
    options?: {
      linkedUserDataRequest?: UserDataRequestDocument;
    }
  ): Promise<SavingsWithdrawalTransactionDocument> {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
    const user = portfolio.owner as UserDocument;
    const orderAmountInCents = Decimal.mul(orderAmount, 100).toNumber();

    // 1. Verify order passes our investment criteria
    const { aggregatedSavingsAmount, availableToSell } = await SavingsProductService.getAggregatedSavingsAmount(
      portfolio,
      savingsProductId
    );
    if (orderAmountInCents > aggregatedSavingsAmount) {
      logger.error(`User ${user.id} is trying to place sell order with quantity larger than what they hold`, {
        module: "PortfolioService",
        method: "withdrawSavings",
        data: {
          orderAmountInCents,
          aggregatedSavingsAmount,
          availableToSell,
          portfolio: portfolio.id
        }
      });
      throw new BadRequestError("You have placed a sell order with quantity larger than what you hold");
    }

    // 2. Create savings withdrawal transaction
    /**
     * If user currently holds enough holdings for the saving product we make status 'Pending'
     * else we make the status 'PendingTopUp' because we're waiting for a SavingTopup to settle.
     */
    const areSavingHoldingsSufficient = availableToSell >= orderAmountInCents;
    const savingsWithdrawalTransactionData: Omit<SavingsWithdrawalTransactionDTOInterface, "createdAt"> = {
      consideration: {
        currency: user.currency,
        amount: orderAmountInCents
      },
      owner: user.id,
      portfolio: portfolio.id,
      status: areSavingHoldingsSufficient ? "Pending" : "PendingTopUp",
      linkedUserDataRequest: options?.linkedUserDataRequest?._id ?? undefined,
      savingsProduct: savingsProductId
    };
    const savingsWithdrawalTransaction = await TransactionService.createSavingsWithdrawalTransaction(
      savingsWithdrawalTransactionData
    );

    logger.info(
      `Created savings withdrawal ${savingsWithdrawalTransaction.id} with ${savingsWithdrawalTransaction.status} for ${user.id} user`,
      {
        module: "PortfolioService",
        method: "withdrawSavings"
      }
    );

    // 4. Emit event
    PortfolioService._emitSavingsTransactionCreationEvent(user, {
      savingsProductId,
      amount: Decimal.div(savingsWithdrawalTransaction.consideration.amount, 100).toNumber(),
      currency: savingsWithdrawalTransaction.consideration.currency,
      side: "sell",
      repeating: false
    });

    // 5. Attempt to fill the withdrawal
    return PortfolioService._fillSavingsWithdrawal(savingsWithdrawalTransaction);
  }

  public static async updatePortfolioSavings(
    portfolioId: string,
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    amountUpBy: number,
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<void> {
    if (amountUpBy === 0) {
      logger.info("Cannot update portfolio saving for zero amount - exiting...", {
        module: "PortfolioService",
        method: "updatePortfolioSavings",
        data: { portfolioId, amountUpBy, session: !!options?.session }
      });
      return;
    }

    const portfolio = await Portfolio.findOne({ _id: portfolioId }).session(options.session);

    logger.info(`Updating saving holdings ${savingsProductId} by ${amountUpBy}`, {
      module: "PortfolioService",
      method: "updatePortfolioSavings",
      data: { userId: portfolio.owner.toString(), portfolioId, amountUpBy, session: !!options?.session }
    });

    const { storedCurrency } = SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId];

    let update: UpdateQuery<PortfolioDocument>;

    const currentSaving = portfolio.savings?.get(savingsProductId);

    if (currentSaving) {
      const newAmount = Decimal.add(currentSaving.amount, amountUpBy).toNumber();

      if (newAmount < 0) {
        throw new BadRequestError("Cannot update savings with negative amount");
      } else if (newAmount === 0) {
        // Remove the savings entry if the amount is zero
        update = {
          $unset: {
            [`savings.${savingsProductId}`]: true
          }
        };
      } else {
        // Update the amount
        update = {
          $set: {
            [`savings.${savingsProductId}.amount`]: newAmount
          }
        };
      }
    } else {
      // Add a new savings entry
      if (amountUpBy < 0) {
        throw new BadRequestError("Cannot initialize savings with negative amount");
      }

      update = {
        $set: {
          [`savings.${savingsProductId}`]: {
            amount: amountUpBy,
            currency: storedCurrency
          }
        }
      };
    }

    await Portfolio.updateOne({ _id: portfolioId }, update).session(options.session);

    logger.info("Updated saving holdings", {
      module: "PortfolioService",
      method: "updatePortfolioSavings",
      data: { userId: portfolio.owner.toString(), portfolioId, amountUpBy, session: !!options?.session }
    });
  }

  public static async getPortfolioUpByValuesAllTenors(
    portfolio: PortfolioDocument,
    transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    }
  ): Promise<Record<TenorEnum, number>> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.CURRENT_TICKER)
    ]);

    const portfolioCalculatedPrice = portfolio.getCalculatedPrice(
      portfolio.holdings.map((holding) => holding.asset)
    );

    // 1. Get up by values & portfolio value from cache
    const [cachedUpByValues, cachedPortfolioValueAtUpBy] = await Promise.all([
      RedisClientService.Instance.get<Record<TenorEnum, number>>(`portfolios:up_by:${portfolio.id}`),
      RedisClientService.Instance.get<number>(`portfolios:value_at_up_by:${portfolio.id}`)
    ]);

    // 2. If cache is empty or cannot be retrieved calculate up by values & store in cache
    let upByValues: Record<TenorEnum, number>;
    let initialPortfolioValue: number;
    if (!cachedUpByValues) {
      const { dividendTransactions, rewards } = transactions;
      upByValues = await PortfolioService.calculateUpByValues(portfolio, {
        dividendTransactions,
        rewards
      });
      initialPortfolioValue = portfolioCalculatedPrice;

      // we do not have to 'await' here - this is intentional
      RedisClientService.Instance.set(`portfolios:up_by:${portfolio.id}`, upByValues);
      RedisClientService.Instance.set(`portfolios:value_at_up_by:${portfolio.id}`, portfolioCalculatedPrice ?? 0);
    } else {
      upByValues = cachedUpByValues;
      initialPortfolioValue = cachedPortfolioValueAtUpBy ?? 0;
    }

    const portfolioPriceIncrease = Decimal.sub(portfolioCalculatedPrice, initialPortfolioValue);

    const adjustedUpByValues = Object.fromEntries(
      Object.entries(upByValues).map(([tenor, upByValue]) => {
        return [tenor, Decimal.add(upByValue, portfolioPriceIncrease).toNumber()];
      })
    ) as Record<TenorEnum, number>;

    return adjustedUpByValues;
  }

  public static async updateCurrencyFromOwner(
    portfolio: PortfolioDocument,
    currency: currenciesConfig.MainCurrencyType
  ) {
    if (PortfolioUtil.hasCash(portfolio)) {
      throw new Error("We cannot update currency of a portfolio that already has cash.");
    }

    await Portfolio.findByIdAndUpdate(portfolio.id, {
      currency
    });
  }

  /**
   * Returns true if given portfolio's holdings are rewards, false otherwise.
   * @param portfolio
   */
  public static async onlyHasRewardedHoldings(portfolio: PortfolioDocument): Promise<boolean> {
    const holdingsThatExistAfterSubtractingRewardQuantities =
      await PortfolioService.getHoldingsThatExistAfterSubtractingRewardQuantities(portfolio);

    return holdingsThatExistAfterSubtractingRewardQuantities.length === 0;
  }

  /**
   * Returns the holdings of the user that exist after subtracting the reward quantities they have been given.
   * The above means that holdings returned will include:
   * 1. Non-rewarded holdings that the user has purchased themselves (e.g. I have been given a Starbucks reward, and I have purchased a Microsoft holding which is returned)
   * 2. Rewarded holdings that the user has re-invested upon and thus are considered "non-rewarded" now (e.g. I have been given 0.02 shares of Starbucks as a reward,
   * and I have purchased 0.15 more shares of it which makes it get returned).
   * @param portfolio The portfolio of the user
   * @returns The holdings that exist in the user's portfolio after subtracting the given reward quantities
   */
  public static async getHoldingsThatExistAfterSubtractingRewardQuantities(
    portfolio: PortfolioDocument
  ): Promise<HoldingsType[]> {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
    const user = portfolio.owner as UserDocument;

    const rewards = await Reward.find({
      targetUser: user.id
    });

    const assetRewardsDict: PartialRecord<investmentUniverseConfig.AssetType, Decimal> = rewards
      .filter(({ status }) => status == "Settled")
      .reduce(
        (assetRewardsDict, reward) => {
          if (assetRewardsDict[reward.asset]) {
            assetRewardsDict[reward.asset] = assetRewardsDict[reward.asset].plus(reward.quantity);
          } else {
            assetRewardsDict[reward.asset] = new Decimal(reward.quantity);
          }
          return assetRewardsDict;
        },
        {} as PartialRecord<investmentUniverseConfig.AssetType, Decimal>
      );

    return portfolio.holdings
      .filter(({ asset, quantity }) => {
        const availableQuantity = new Decimal(quantity).minus(assetRewardsDict[asset.commonId] ?? 0).toNumber();
        return availableQuantity > 0;
      })
      .map(({ asset, assetCommonId, quantity }) => {
        return { asset, assetCommonId, quantity };
      });
  }

  /**
   * PRIVATE METHODS
   */

  /**
   * @description This method gives the MWRR returns of an asset.
   */
  private static async _getAssetReturnsMWRR(
    portfolio: PortfolioDocument,
    holding: HoldingsType,
    transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    }
  ): Promise<number> {
    const userId = portfolio.populated("owner")
      ? (portfolio.owner as UserDocument).id
      : portfolio.owner.toString();
    const userCurrency = portfolio.currency;
    const assetId = holding.assetCommonId;
    const allAssetIsinsSet = new Set(InvestmentUniverseUtil.getIsinActiveAndDeprecated(assetId));

    const { dividendTransactions, rewards } = transactions;
    const orders = (
      await OrderService.getMatchedOrdersForTransactions(userId, ["AssetTransaction", "RebalanceTransaction"])
    ).filter((order) => allAssetIsinsSet.has(order.isin));
    const assetRewards = rewards.filter((reward) => reward.asset === assetId);

    // In case a user has sold all the quantity at some point,
    // keep only the orders/rewards after that point.
    const { eligibleOrders, eligibleDividends, eligibleRewards } =
      PortfolioService._filterOutActivityBeforeLastFullSell(orders, dividendTransactions, assetRewards);

    // ascending order
    const flows = [
      ...eligibleOrders.map((order) => {
        const amount = order.amountForReturnsAndUpBy;
        return {
          amount: order.side == "Buy" ? -amount : amount,
          date: new Date(order.filledAt ?? order.updatedAt)
        };
      }),
      ...eligibleDividends.map((dividend) => ({
        amount: dividend.consideration.amount,
        date: new Date(dividend.settledAt ?? dividend.createdAt)
      })),
      ...eligibleRewards.map((reward) => {
        return {
          amount: -reward.consideration.amount,
          date: new Date(reward.updatedAt ?? reward.createdAt)
        };
      })
    ].sort((flow1, flow2) => flow1.date.getTime() - flow2.date.getTime());

    // add asset current price as last flow with current date
    flows.push({
      amount: Decimal.mul(holding.quantity, holding.asset.currentTicker.getPrice(userCurrency))
        .mul(100)
        .round()
        .toNumber(),
      date: new Date(holding.asset.currentTicker.timestamp)
    });

    return PortfolioService._xirr(flows, portfolio.id);
  }

  /**
   * @description This method gives the returns of an asset using the calculation:
   * returns = current price / average price per share
   *
   * We want the returns to be calculated on the portfolio settlement currency.
   */
  private static async _getAssetReturnsAveragePricePerShare(
    portfolio: PortfolioDocument,
    investmentProduct: InvestmentProductDocument,
    transactions: {
      rewards: RewardDocument[];
    }
  ): Promise<number> {
    const userCurrency = portfolio.currency;
    const averagePricePerShare = await PortfolioService.getAveragePricePerShare(
      investmentProduct.commonId,
      portfolio,
      transactions
    );
    return averagePricePerShare.priceInSettlementCurrency > 0
      ? Decimal.div(
          investmentProduct.currentTicker.getPrice(userCurrency),
          averagePricePerShare.priceInSettlementCurrency
        )
          .minus(1)
          .toNumber()
      : 0;
  }

  /**
   * @description This method utilises the method for asset returns "getAssetReturns" and returns
   * all holdings with a field for the corresponding return on each holding.
   *
   * @param portfolio
   * @param transactions
   * @returns
   */
  private static async _getAllHoldingsReturns(
    portfolio: PortfolioDocument,
    transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    }
  ): Promise<(HoldingsType & { sinceBuyReturns: number })[]> {
    const { rewards } = transactions;

    return Promise.all(
      portfolio.holdings.map(async (holding) => {
        const dividendTransactions = transactions.dividendTransactions.filter(
          (transaction) => transaction.asset === holding.assetCommonId
        );
        const returns = await PortfolioService.getAssetReturns(portfolio, holding, {
          dividendTransactions,
          rewards
        });

        const { asset, assetCommonId, quantity } = holding;

        return {
          asset,
          assetCommonId,
          quantity,
          sinceBuyReturns: returns
        };
      })
    );
  }

  /**
   * @description For each dashboard chart tenor it finds the dates that should be populated with zeros
   * in order to not have an empty chart. Any weekends are filtered out.
   *
   * @param tenor
   * @param oldestTickerTimestamp
   * @returns
   */
  private static _getDatesToFillWithZeros(tenor: TenorEnum, oldestTickerTimestamp: number): Date[] {
    // If the oldest ticker timstamp is defined this means that the user is invested.
    const userIsInvested = oldestTickerTimestamp > 0;

    // 1. Get start date of the tenor - for ALL_TIME if the user is invested for less than a
    // week, we set the date one week ago as the starting date.
    const oldestTickerDate = userIsInvested ? new Date(oldestTickerTimestamp) : new Date(Date.now());
    let tenorStartDate = DateUtil.getDateOfDaysAgo(new Date(Date.now()), DURATIONS_MAP[tenor]);
    if (tenor === TenorEnum.ALL_TIME) {
      if (
        DateUtil.isFutureDate(oldestTickerDate, DateUtil.getDateOfDaysAgo(new Date(Date.now()), ONE_WEEK_IN_DAYS))
      ) {
        tenorStartDate = DateUtil.getDateOfDaysAgo(new Date(Date.now()), ONE_WEEK_IN_DAYS);
      } else {
        tenorStartDate = oldestTickerDate;
      }
    }

    // If the user is not invested at all then we set the tenor start date to one week ago.
    if (!userIsInvested) {
      tenorStartDate = DateUtil.getDateOfDaysAgo(new Date(Date.now()), ONE_WEEK_IN_DAYS);
    }

    // 2. Get all dates between start date and oldest ticker date to fill them with zeros
    // if the user is not invested, today should also be a zero day, otherwise it's the day before the latest
    // ticker.
    // Any added zeros that correspond to weekends are filtered out.
    const latestDateWithZeros = userIsInvested ? DateUtil.getYesterday(oldestTickerDate) : new Date(Date.now());
    const datesToFillWithZeros = DateUtil.getAllDatesBetweenTwoDates(tenorStartDate, latestDateWithZeros, {
      order: DateOrderingEnum.LEAST_RECENT_FIRST
    })
      .map((dateToFillWithZero) => DateUtil.getStartAndEndOfDay(dateToFillWithZero).start)
      .filter((date) => !DateUtil.isWeekend(date));

    return datesToFillWithZeros;
  }

  /**
   * @description Method can be used for up-by calculations either for portfolio
   * or for specific holdings.
   *
   * @param currentValue
   * @param startingValue
   * @param orders
   * @param dividends
   * @param rewards
   * @returns
   */
  private static _getUpByValue(
    currentValue: number,
    startingValue: number,
    orders: OrderDocument[],
    dividends: DividendTransactionDocument[],
    rewards: RewardDocument[]
  ): number {
    const totalBoughtValue = orders
      .filter((order) => order.side == "Buy" && order.isMatched)
      .map((order) => new Decimal(order.amountForReturnsAndUpBy).div(100)) // pounds conversion
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0));
    const totalSoldValue = orders
      .filter((order) => order.side == "Sell" && order.isMatched)
      .map((order) => new Decimal(order.amountForReturnsAndUpBy).div(100)) // pounds conversion
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0));
    const rewardsValue = rewards
      .filter((reward) => reward.status === "Settled")
      .map((reward) => new Decimal(reward.consideration.amount))
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .div(100);
    const dividendsValue = dividends
      .filter((dividend) => dividend.status === "Settled")
      .map((dividend) => new Decimal(dividend.consideration.amount))
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .div(100);

    return new Decimal(currentValue)
      .minus(startingValue)
      .minus(totalBoughtValue)
      .minus(rewardsValue)
      .plus(totalSoldValue)
      .plus(dividendsValue)
      .toDecimalPlaces(2)
      .toNumber();
  }

  private static async _syncWkEntry(portfolio: PortfolioDocument): Promise<void> {
    logger.info(`Attempting to sync WK entry for portfolio ${portfolio.id}`, {
      module: "PortfolioService",
      method: "_syncWkEntry",
      data: { portfolio: portfolio.id }
    });

    const portfolioId = portfolio.providers?.wealthkernel?.id;
    if (!portfolioId) {
      logger.error("Attempted to sync portfolio to Wealthkernel but has no WK id", {
        module: "PortfolioService",
        method: "_syncWkEntry",
        data: { portfolio: portfolio.id }
      });
      throw new Error("Portfolio has no WK ID!");
    }

    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);
    const user = portfolio.owner as UserDocument;

    const wkPortfolio = await ProviderService.getBrokerageService(user.companyEntity).retrievePortfolio(
      portfolioId
    );
    await Portfolio.findOneAndUpdate(
      { _id: portfolio._id },
      {
        "providers.wealthkernel": { id: wkPortfolio.id, status: wkPortfolio.status }
      },
      { runValidators: true }
    );
  }

  private static _getHoldingsPercentages(
    userCurrency: currenciesConfig.MainCurrencyType,
    portfolio: PortfolioDocument,
    allocationMethod: PortfolioAllocationMethodEnum
  ) {
    if (allocationMethod === PortfolioAllocationMethodEnum.HOLDINGS) {
      if (!portfolio.holdings.length || !portfolio.holdings[0]?.asset?.currentTicker) {
        // assets to buy should come with linked investment product populated
        throw new BadRequestError("No assets to buy or no linked investment products");
      }
      return PortfolioUtil.mapHoldingsToAllocationFormat(userCurrency, portfolio.holdings).assets;
    } else if (allocationMethod === PortfolioAllocationMethodEnum.TARGET_ALLOCATION) {
      if (!portfolio.isTargetAllocationSetup) {
        throw new BadRequestError(
          `Target allocation method was selected but portfolio ${portfolio.id} has no target allocation set`
        );
      } else if (!portfolio.initialHoldingsAllocation[0]?.asset?.currentTicker) {
        throw new BadRequestError("Assets have no prices");
      }
      const holdingsPercentages: PartialRecord<investmentUniverseConfig.AssetType, number> = {};
      portfolio.initialHoldingsAllocation.forEach((asset) => {
        if (asset.percentage > 0) {
          holdingsPercentages[asset.assetCommonId] = asset.percentage;
        }
      });
      return holdingsPercentages;
    }
  }

  private static async _createWkPortfolio(portfolio: PortfolioDocument): Promise<PortfolioDocument> {
    await Promise.all([
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER),
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.ACCOUNT)
    ]);

    const account = portfolio.account as AccountDocument;
    const user = portfolio.owner as UserDocument;

    const wealthkernelResponse = await ProviderService.getBrokerageService(user.companyEntity).createPortfolio(
      account,
      user
    );

    // Update user's portfolio document with WK portfolio id
    const updatedPortfolio = Portfolio.findOneAndUpdate(
      { _id: portfolio.id },
      {
        "providers.wealthkernel": {
          id: wealthkernelResponse.id,
          status: "Created"
        }
      },
      {
        new: true,
        runValidators: true,
        setDefaultsOnInsert: true
      }
    );

    eventEmitter.emit(events.user.verification.eventId, user, { emailNotification: user.isManuallyKycPassed });
    logger.info(`Wealthkernel portfolio has just been created for user ${user.email}. User is now verified.`, {
      module: "PortfolioService",
      method: "_createWkPortfolio"
    });

    return updatedPortfolio;
  }

  /**
   * This method filters orders, rewards and dividends including only those after
   * the last full sell of an asset.
   *
   * @private
   */
  private static _filterOutActivityBeforeLastFullSell(
    orders: OrderDocument[],
    dividends: DividendTransactionDocument[],
    rewards: RewardDocument[]
  ): {
    eligibleOrders: OrderDocument[];
    eligibleDividends: DividendTransactionDocument[];
    eligibleRewards: RewardDocument[];
  } {
    let totalQuantity = 0;
    let lastFullSellDate: Date;

    // find last full sell date by orders & rewards
    [
      ...orders.map((order) => {
        const amount = order.amountForReturnsAndUpBy;
        return {
          amount: order.side == "Buy" ? -amount : amount,
          date: new Date(order.filledAt ?? order.updatedAt),
          quantity: order.side == "Buy" ? order.quantity : -order.quantity
        };
      }),
      ...rewards.map((reward) => {
        return {
          amount: -reward.consideration.amount,
          date: new Date(reward.updatedAt ?? reward.createdAt),
          quantity: reward.quantity
        };
      })
    ]
      .sort((flow1, flow2) => flow1.date.getTime() - flow2.date.getTime())
      .forEach((flow) => {
        totalQuantity += flow.quantity;
        if (totalQuantity === 0) {
          lastFullSellDate = flow.date;
        }
      });

    if (lastFullSellDate) {
      orders = orders.filter((order) => new Date(order.filledAt ?? order.updatedAt) > lastFullSellDate);
      dividends = dividends.filter(
        (dividend) => new Date(dividend.settledAt ?? dividend.createdAt) > lastFullSellDate
      );
      rewards = rewards.filter((reward) => new Date(reward.updatedAt ?? reward.createdAt) > lastFullSellDate);
    }

    return { eligibleOrders: orders, eligibleDividends: dividends, eligibleRewards: rewards };
  }

  /**
   * @description
   * Instructions:
   * 1. Calculate XIRR
   * 2. For every cashflow calculate it's cumulative return: cumulative_cashflow_return = (1+xirr)^fraction_of_the_year -1, where faction_of_the_year = ((Today-cashflow_date)/365)
   * 3. Calculate weighted average with absolute cashflows: Return_value = [ abs(cashflow1) * cumulative_cashflow_return1 + abs(cashflow2) * cumulative_cashflow_return2 + … + abs(cashflowN) * cumulative_cashflow_returnN ] / sum(abs(cashflows))
   *
   * Notes:
   * 1. The last cashflow is always TODAY and we exclude it from the weighted average calculation
   */
  private static _xirr(flows: CashFlow[], portfolioId: string): number {
    const guessRates = [0.1, -0.1, -0.5, -0.9, -0.99, -0.9999, -0.999999, -0.99999999, -0.9999999999999999];

    // exits if successfully calculates portfolio return for an applied guess rate
    for (const guessRate of guessRates) {
      try {
        // note: money weighted return compounds for one less day similar to moving the initial date one day forward
        const xirrValue = xirr(
          flows,
          guessRate /*guess rate*/,
          1e-8 /*max epsilon*/,
          200 /*max scans*/,
          100 /*max iterations*/
        );

        let weightedSum = new Decimal(0);
        let cashflowSum = new Decimal(0);
        for (let i = 0; i < flows.length - 1; i++) {
          const flow = flows[i];

          const diffDays = Decimal.abs(DateUtil.dateDiffInExactDays(flows[flows.length - 1].date, flow.date));
          const fractionOfTheYear = diffDays.div(365);
          const cumulativeReturn = Decimal.pow(Decimal.add(1, xirrValue), fractionOfTheYear).minus(1).toNumber();

          weightedSum = Decimal.add(weightedSum, Decimal.abs(flow.amount).mul(cumulativeReturn).toNumber());
          cashflowSum = Decimal.add(cashflowSum, Decimal.abs(flow.amount).toNumber());
        }

        const returns = Decimal.div(weightedSum, cashflowSum).toNumber();
        if (Number.isNaN(returns)) {
          throw new Error(`XIRR returns is NaN for portfolio ${portfolioId} with guess rate ${guessRate}`);
        }

        return returns;
      } catch (err) {
        logger.warn("Could not calculate xirr for guess rate : " + guessRate, {
          module: "portfolioService",
          method: "_xirr",
          data: { flows, guessRate, portfolioId, error: err }
        });
      }
    }

    logger.warn("Could not calculate xirr", {
      module: "portfolioService",
      method: "_xirr",
      data: { portfolioId }
    });
    return null;
  }

  private static _getMongooseQueryParameters(
    params: {
      _id?: any;
      owner?: string;
      mode?: PortfolioModeEnum;
      wealthkernelExists?: boolean;
      holdings?: any;
      hasHolding?: investmentUniverseConfig.AssetType;
      initialHoldingsAllocation?: any;
    } = {},
    sort?: string
  ): {
    filterParameters: { [p: string]: any };
    queryOptions: QueryOptions;
  } {
    const actualParams: {
      _id?: any;
      owner?: string;
      mode?: PortfolioModeEnum;
      "providers.wealthkernel.id"?: {
        $exists: boolean;
      };
      holdings?: any;
      "holdings.assetCommonId": investmentUniverseConfig.AssetType;
      initialHoldingsAllocation?: any;
    } = {
      _id: null,
      owner: null,
      mode: null,
      "providers.wealthkernel.id": null,
      holdings: null,
      "holdings.assetCommonId": null,
      initialHoldingsAllocation: null
    };
    Object.keys(actualParams).forEach((key) => {
      if ((params as any)[key] != null) {
        (actualParams as any)[key] = (params as any)[key];
      }
    });

    if (params.wealthkernelExists != null) {
      actualParams["providers.wealthkernel.id"] = { $exists: params.wealthkernelExists };
    }

    if (params.hasHolding != null) {
      actualParams["holdings.assetCommonId"] = params.hasHolding;
    }

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    const paramsToUse = Object.fromEntries(Object.entries(actualParams).filter(([, value]) => value != null));

    return { filterParameters: paramsToUse, queryOptions: options };
  }

  private static _getInitialTransactionStatus(
    pendingDeposit?: DepositCashTransactionDocument,
    pendingGift?: GiftDocument
  ): TransactionStatusType {
    if (pendingDeposit) {
      return "PendingDeposit";
    } else if (pendingGift) {
      return "PendingGift";
    } else return "Pending";
  }

  private static async _emitInvestmentCreationEvent(
    transaction: AssetTransactionDocument,
    investmentsProductsDict: { [isin: string]: InvestmentProductDocument },
    cashback?: CashbackTransactionDocument
  ): Promise<void> {
    const { portfolioTransactionCategory, orders, fees, consideration, originalInvestmentAmount } = transaction;

    const ownerId = transaction.populated("owner")
      ? transaction.owner._id.toString()
      : transaction.owner.toString();

    const transactionIsAssetBuy =
      transaction.portfolioTransactionCategory === "update" &&
      transaction.orders.length === 1 &&
      transaction.orders[0].side === "Buy";
    const transactionIsAssetSell =
      transaction.portfolioTransactionCategory === "update" &&
      transaction.orders.length === 1 &&
      transaction.orders[0].side === "Sell";

    const [owner, isFirst] = await Promise.all([
      UserService.getUser(ownerId, {
        addresses: false,
        portfolios: false,
        subscription: true
      }),
      UserService.userHasSingleInvestment(ownerId)
    ]);

    let amount: Decimal;
    let fxFee: Decimal;
    let commissionFee: Decimal;
    let executionSpreadFee: Decimal;
    let category: TrackTransactionInfoCategoryType;
    let assetName: string;
    if (transactionIsAssetBuy) {
      const investmentProduct = investmentsProductsDict[orders[0].isin];

      amount = new Decimal(orders[0].consideration.originalAmount ?? orders[0].consideration?.amount).div(100);
      fxFee = new Decimal(fees?.fx?.amount ?? 0);
      commissionFee = new Decimal(fees?.commission?.amount ?? 0);
      executionSpreadFee = new Decimal(fees?.executionSpread?.amount ?? 0);

      // Determine asset name and category based on whether the order is stock or ETF
      assetName = ASSET_CONFIG[investmentProduct.commonId].simpleName;
      category = ASSET_CONFIG[investmentProduct.commonId].category;
    } else if (transactionIsAssetSell) {
      const investmentProduct = investmentsProductsDict[orders[0].isin];

      const plan = (owner.subscription as SubscriptionDocument).plan;

      const currentTickerPrice = investmentProduct?.currentTicker?.getPrice(owner.currency) ?? 0;

      amount = new Decimal(currentTickerPrice).mul(orders[0].quantity).toDecimalPlaces(2);

      // For asset sell transactions, since we don't have the fees yet, we estimate them
      const ordersAfterFees = OrderService.applyFeesToOrders(
        plan,
        orders,
        owner.currency,
        investmentsProductsDict
      );
      const fees = aggregateFees(
        ordersAfterFees.map((order) => order.fees),
        owner.currency
      );

      fxFee = new Decimal(fees?.fx?.amount ?? 0);
      commissionFee = new Decimal(fees?.commission?.amount ?? 0);
      executionSpreadFee = new Decimal(fees?.executionSpread?.amount ?? 0);

      // Determine asset name and category based on whether the order is stock or ETF
      assetName = ASSET_CONFIG[investmentProduct.commonId].simpleName;
      category = ASSET_CONFIG[investmentProduct.commonId].category;
    } else if (portfolioTransactionCategory === "buy") {
      amount = new Decimal(originalInvestmentAmount ?? consideration?.amount).div(100);
      fxFee = new Decimal(fees?.fx?.amount ?? 0);
      commissionFee = new Decimal(fees?.commission?.amount ?? 0);
      executionSpreadFee = new Decimal(fees?.executionSpread?.amount ?? 0);
      category = "portfolio";
    } else if (portfolioTransactionCategory === "sell") {
      amount = new Decimal(originalInvestmentAmount ?? consideration?.amount).div(100);

      // For portfolio sell transactions, since we don't have the fees yet, we estimate them

      const plan = (owner.subscription as SubscriptionDocument).plan;

      // For asset sell transactions, since we don't have the fees yet, we estimate them
      const ordersAfterFees = OrderService.applyFeesToOrders(
        plan,
        orders,
        owner.currency,
        investmentsProductsDict
      );
      const fees = aggregateFees(
        ordersAfterFees.map((order) => order.fees),
        owner.currency
      );

      fxFee = new Decimal(fees?.fx?.amount ?? 0);
      commissionFee = new Decimal(fees?.commission?.amount ?? 0);
      executionSpreadFee = new Decimal(fees?.executionSpread?.amount ?? 0);
      category = "portfolio";
    }

    const transactionInfo: TrackTransactionInfoType = amount
      ? {
          side: (portfolioTransactionCategory == "update"
            ? orders[0].side.toLowerCase()
            : portfolioTransactionCategory) as "buy" | "sell",
          category,
          assetName,
          frequency: transaction.linkedAutomation ? "repeating" : "one-off",
          cashbackAmount: Decimal.div(cashback?.consideration?.amount ?? 0, 100).toNumber(),
          amount: amount.toNumber(),
          currency: transaction.consideration?.currency ?? owner.currency,
          fxFees: fxFee.toNumber(),
          commissionFees: commissionFee.toNumber(),
          executionSpreadFees: executionSpreadFee.toNumber(),
          redeemedGift: !!transaction.pendingGift
        }
      : {};

    const properties: TrackPropertiesType = {
      isFirst,
      ...transactionInfo
    };

    if (isFirst) {
      eventEmitter.emit(events.transaction.firstInvestmentCreation.eventId, owner);
    }

    eventEmitter.emit(events.transaction.investmentCreation.eventId, owner, properties);
  }

  private static async _getTargetAllocationDiff(
    userCurrency: currenciesConfig.MainCurrencyType,
    realPortfolio: PortfolioDocument
  ): Promise<PartialRecord<investmentUniverseConfig.AssetType, number>> {
    const availableHoldings = await PortfolioService.getAvailableHoldings(realPortfolio);
    const { assets } = PortfolioUtil.mapHoldingsToAllocationFormat(userCurrency, availableHoldings);

    const allocationDiff: PartialRecord<investmentUniverseConfig.AssetType, number> = {};
    // check diffs from target allocation
    realPortfolio.initialHoldingsAllocation.forEach(({ assetCommonId, percentage }) => {
      const currentPercentage = assets[assetCommonId] ?? 0;
      allocationDiff[assetCommonId] = Decimal.sub(percentage, currentPercentage).abs().toNumber();
    });
    // check for assets that are not present in target allocation
    Object.entries(assets).forEach(([key, percentage]: [investmentUniverseConfig.AssetType, number]) => {
      if (allocationDiff[key] == null) {
        allocationDiff[key] = percentage;
      }
    });

    return allocationDiff;
  }

  private static _getAssetRestrictedQuantity(
    portfolio: PortfolioDocument,
    assetCommonId: investmentUniverseConfig.AssetType,
    activeRewards: RewardDocument[]
  ): number {
    const restrictedRewardQuantity = activeRewards
      .filter(({ status }) => status == "Settled")
      .filter((reward) => reward.asset === assetCommonId)
      .map(({ quantity }) => quantity)
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0));

    const restrictedGiftedQuantity = (portfolio.giftedHoldings.get(assetCommonId) ?? [])
      .filter(({ unrestrictedAt }) => unrestrictedAt > new Date())
      .map(({ quantity }) => quantity)
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0));

    return restrictedRewardQuantity.plus(restrictedGiftedQuantity).toNumber();
  }

  private static readonly _createPortfolio = async (
    portfolioData: PortfolioDTOInterface
  ): Promise<PortfolioDocument> => {
    logger.info(`Creating ${portfolioData.mode} portfolio for owner ${portfolioData.owner}`, {
      module: "PortfolioService",
      method: "_createPortfolio"
    });

    const existingPortfolio = (
      await PortfolioService.getPortfolios({
        owner: portfolioData.owner.toString(),
        mode: portfolioData.mode
      })
    )?.[0];
    if (existingPortfolio) {
      const owner = existingPortfolio.owner as UserDocument;
      logger.error(
        `User ${owner?._id ? owner._id : owner} already has ${portfolioData.mode} portfolio ${
          existingPortfolio._id
        }`,
        {
          module: "PortfolioService",
          method: "_createPortfolio",
          userEmail: owner.email
        }
      );
      throw new BadRequestError(
        `User has already created a ${existingPortfolio.mode.toLocaleLowerCase()} portfolio`
      );
    }

    return Portfolio.findOneAndUpdate(
      { owner: new mongoose.Types.ObjectId(portfolioData.owner.toString()), mode: portfolioData.mode },
      portfolioData,
      {
        runValidators: true,
        setDefaultsOnInsert: true,
        upsert: true,
        new: true
      }
    );
  };

  private static _emitSavingsTransactionCreationEvent(
    user: UserDocument,
    options: {
      savingsProductId: savingsUniverseConfig.SavingsProductType;
      repeating: boolean;
      amount: number;
      currency: currenciesConfig.MainCurrencyType;
      side: "buy" | "sell";
    }
  ): void {
    const properties: TrackTransactionInfoType = {
      side: options.side,
      category: "savings",
      assetName: options.savingsProductId,
      amount: options.amount,
      currency: options.currency,
      cashbackAmount: 0,
      fxFees: 0,
      commissionFees: 0,
      executionSpreadFees: 0,
      frequency: options?.repeating ? "repeating" : "one-off"
    };

    eventEmitter.emit(events.transaction.investmentCreation.eventId, user, properties);
  }

  /**
   * @description
   * This method tries to internally fill the savings withdrawal from the unsubmitted topups,
   * only if we're at least 1 hour outside of the savings product execution window.
   * --
   * At the end we check if the withdrawal can be settled instantly.
   */
  private static async _fillSavingsWithdrawal(
    savingsWithdrawal: SavingsWithdrawalTransactionDocument
  ): Promise<SavingsWithdrawalTransactionDocument> {
    await DbUtil.populateIfNotAlreadyPopulated(savingsWithdrawal, TransactionPopulationFieldsEnum.ORDERS);

    // 1. Exit if we're within 1 hour of the savings product execution window, to avoid any order creation conflicts
    if (SubmissionWindowUtil.isCurrentTimeWithinOneHourOfSavingsProductSubmissionWindow())
      return savingsWithdrawal;

    // 2.Retrieve topups that aren't submitted to broker
    const pendingTopups = await SavingsTopupTransaction.find({
      owner: savingsWithdrawal.owner,
      savingsProduct: savingsWithdrawal.savingsProduct,
      status: { $in: ["Pending"] }
    }).populate("orders pendingDeposit");
    const unsubmittedTopups = pendingTopups
      // If a topup has only InternallyFilled orders it means it's not submitted to the broker.
      // This will return true also for transactions that have no orders yet.
      .filter((topup) => topup.orders.every((order) => order.status === "InternallyFilled"))
      .filter((topup) => topup.remainingAmountToSubmit > 0)
      .sort((a, b) => b.remainingAmountToSubmit - a.remainingAmountToSubmit);

    // 3. Check every topup if it can be used to fill the withdrawal
    for (let i = 0; i < unsubmittedTopups.length; i++) {
      const topup = unsubmittedTopups[i];

      await PortfolioService._attemptToInternallyFillOrders(topup, savingsWithdrawal);

      savingsWithdrawal = await SavingsWithdrawalTransaction.findById(savingsWithdrawal.id).populate("orders");
    }

    // 4. Check if the withdrawal can be settled instantly
    return TransactionService.syncSavingsWithdrawalTransaction(savingsWithdrawal);
  }

  /**
   * @description
   * This method tries to internally fill the savings topup from the unsubmitted withdrawals,
   * only if we're at least 1 hour outside of the savings product execution window.
   */
  private static async _fillSavingsTopup(savingsTopup: SavingsTopupTransactionDocument): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(savingsTopup, TransactionPopulationFieldsEnum.ORDERS);

    // 1. Exit if we're within 1 hour of the savings product execution window, to avoid any order creation conflicts
    if (SubmissionWindowUtil.isCurrentTimeWithinOneHourOfSavingsProductSubmissionWindow()) return;

    // 2. Retrieve withdrawals that aren't submitted to broker
    const pendingWithdrawals = await SavingsWithdrawalTransaction.find({
      owner: savingsTopup.owner,
      savingsProduct: savingsTopup.savingsProduct,
      status: { $in: ["Pending", "PendingTopUp"] }
    }).populate("orders");
    // If a withdrawal has only InternallyFilled orders it means it's not submitted to the broker
    const unsubmittedWithdrawals = pendingWithdrawals
      .filter((withdrawal) =>
        // This will return true also for transactions that have no orders yet.
        withdrawal.orders.every((order) => order.status === "InternallyFilled")
      )
      .sort((a, b) => b.remainingAmountToSubmit - a.remainingAmountToSubmit);

    // 3. Check every withdrawal if it can be used to fill the topup
    for (let i = 0; i < unsubmittedWithdrawals.length; i++) {
      const withdrawal = unsubmittedWithdrawals[i];

      await PortfolioService._attemptToInternallyFillOrders(savingsTopup, withdrawal);

      savingsTopup = await SavingsTopupTransaction.findById(savingsTopup.id).populate("orders");
    }
  }

  /**
   * @description
   * This method attempts to fill the topup and withdrawal internally.
   * If the below conditions pass:
   * 1. The topup and withdrawal are for the same savings product
   * 2. The topup and withdrawal can be filled (this depends on the remaining post the fill amount)
   * If these condtions pass, we create internal orders for the topup and withdrawal.
   */
  private static async _attemptToInternallyFillOrders(
    savingsTopup: SavingsTopupTransactionDocument,
    savingsWithdrawal: SavingsWithdrawalTransactionDocument
  ): Promise<void> {
    // 1. Check if the topup and withdrawal are for the same savings product
    if (savingsTopup.savingsProduct != savingsWithdrawal.savingsProduct) {
      throw new Error("Cannot fill internal orders for different savings products");
    }

    const minAllowedSavingsInvestment = Decimal.mul(SAVINGS_PRODUCT_MINIMUM_WK_ORDER_AMOUNT, 100).toNumber();
    const internallyFilledAmount = Decimal.min(
      savingsTopup.remainingAmountToSubmit,
      savingsWithdrawal.remainingAmountToSubmit
    ).toNumber();

    // 2. Check that the topup and withdrawal can be filled
    /**
     * We check if the amount post fill:
     * 1. Is greater than the minimum allowed investment (this cannot be submitted to the broker)
     * 2. Is zero (this means the transaction will be completely filled)
     */
    const withdrawalAmountPostFill = Decimal.sub(
      savingsWithdrawal.remainingAmountToSubmit,
      internallyFilledAmount
    );
    const canWithdrawalBeFilled =
      withdrawalAmountPostFill.isZero() || withdrawalAmountPostFill.gte(minAllowedSavingsInvestment);

    const topupAmountPostFill = Decimal.sub(savingsTopup.remainingAmountToSubmit, internallyFilledAmount);
    const canTopupBeFilled = topupAmountPostFill.isZero() || topupAmountPostFill.gte(minAllowedSavingsInvestment);

    if (!canWithdrawalBeFilled || !canTopupBeFilled) {
      captureMessage(
        `Internally filled amountis less than the minimum for filling topup-withdrawal ${savingsTopup.id}-${savingsWithdrawal.id}`
      );
      return;
    }

    await DbUtil.populateIfNotAlreadyPopulated(savingsTopup, TransactionPopulationFieldsEnum.OWNER);
    const user = savingsTopup.owner as UserDocument;

    // 3. Create internal orders for the topup and withdrawal
    const isin = SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsTopup.savingsProduct].isin;
    const buyOrderData: OrderDTOInterface = {
      status: "InternallyFilled",
      isin: isin,
      settlementCurrency: user.currency,
      side: "Buy",
      activeProviders: [],
      transaction: savingsTopup._id,
      consideration: {
        currency: user.currency,
        amount: internallyFilledAmount,
        originalAmount: internallyFilledAmount,
        amountSubmitted: internallyFilledAmount
      },
      fees: getZeroFees(user.currency),
      submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
    };
    const sellOrderData: OrderDTOInterface = {
      status: "InternallyFilled",
      isin: isin,
      settlementCurrency: user.currency,
      side: "Sell",
      activeProviders: [],
      transaction: savingsWithdrawal._id,
      consideration: {
        currency: user.currency,
        amount: internallyFilledAmount,
        originalAmount: internallyFilledAmount,
        amountSubmitted: internallyFilledAmount
      },
      fees: getZeroFees(user.currency),
      submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
    };

    logger.info(`About to create internal savings orders for ${savingsTopup._id} and ${savingsWithdrawal._id}`, {
      module: "PortfolioService",
      method: "_attemptToInternallyFillOrders"
    });

    await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
      await OrderService.createDbOrder(buyOrderData, {
        session
      });
      await OrderService.createDbOrder(sellOrderData, {
        session
      });
    });

    logger.info(
      `Created internal savings orders for ${savingsTopup._id} and ${savingsWithdrawal._id} and updated portfolio cash`,
      {
        module: "PortfolioService",
        method: "_attemptToInternallyFillOrders"
      }
    );
  }

  /**
   * This method either adds the latest portfolio value in the end of the data array, or replaces the last
   * daily portfolio ticker with the latest portfolio value (if a daily portfolio ticker is already present).
   *
   * We do this because we want the last element in the tickers array to **match** the current live value of the
   * portfolio which is not the case with daily portfolio tickers as they are created once every evening.
   *
   * @param latestPortfolioValueDataPoint
   * @param pricesByTenorDataPoints
   * @private
   */
  private static _addTodayIntraDayTicker(
    latestPortfolioValueDataPoint: PortfolioPriceDataPointType,
    pricesByTenorDataPoints: PortfolioPriceDataPointType[]
  ): PortfolioPriceDataPointType[] {
    if (
      pricesByTenorDataPoints.length > 0 &&
      DateUtil.isToday(new Date(pricesByTenorDataPoints.at(-1).timestamp))
    ) {
      return [...pricesByTenorDataPoints.slice(0, -1), latestPortfolioValueDataPoint];
    } else {
      return [...pricesByTenorDataPoints, latestPortfolioValueDataPoint];
    }
  }

  /**
   * Get the total quantity bought from an array of cash flows, adjusted based on previous stock splits.
   *
   * @param cashFlows
   * @param stockSplits
   */
  private static _getSplitAdjustedTotalQuantityBought(
    cashFlows: CashFlowWithQuantity[],
    stockSplits: StockSplitCorporateEventDocument[]
  ): number {
    let adjustedCashFlows = cashFlows;

    stockSplits.forEach((split) => {
      adjustedCashFlows = adjustedCashFlows.map((cashFlow) => {
        if (DateUtil.isPastDate(cashFlow.date, split.date)) {
          return {
            ...cashFlow,
            quantity: Decimal.div(cashFlow.quantity, split.multiplier).mul(split.divider).toNumber()
          };
        } else return cashFlow;
      });
    });

    return adjustedCashFlows
      .map((flow) => new Decimal(flow.quantity))
      .reduce((sum, quantity) => sum.plus(quantity), new Decimal(0))
      .toNumber();
  }

  /**
   * @description Filters timestamps that are within market hours for their respective dates.
   * Caches market hours per day to avoid recalculating for timestamps on the same date.
   *
   * @param tickers Array of intraday portfolio tickers with timestamp field
   * @returns Filtered array of tickers that fall within market hours
   */
  private static _filterTickersByMarketHours(
    tickers: IntraDayPortfolioTickerDocument[]
  ): IntraDayPortfolioTickerDocument[] {
    // Cache to store market hours per date (YYYY-MM-DD format)
    const marketHoursCache = new Map<
      string,
      { earliestOpen: { HOUR: number; MINUTES: number }; latestClose: { HOUR: number; MINUTES: number } }
    >();

    return tickers.filter(({ timestamp }) => {
      const date = new Date(timestamp);
      const dateKey = date.toISOString().split("T")[0]; // Get YYYY-MM-DD format

      // Check if we already calculated market hours for this date
      if (!marketHoursCache.has(dateKey)) {
        const { earliestOpen, latestClose } = DateUtil.getEarliestOpenAndLatestCloseMarketHours(date);
        marketHoursCache.set(dateKey, { earliestOpen, latestClose });
      }

      const { earliestOpen, latestClose } = marketHoursCache.get(dateKey)!;
      return DateUtil.isDateWithinTimeBounds(date, earliestOpen, latestClose);
    });
  }
}

export default PortfolioService;
