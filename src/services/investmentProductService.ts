import { DateTime } from "luxon";
import {
  InvestmentProduct,
  InvestmentProductDocument,
  InvestmentProductInterface
} from "../models/InvestmentProduct";
import {
  currenciesConfig,
  entitiesConfig,
  investmentUniverseConfig,
  localeConfig,
  marketHoursConfig
} from "@wealthyhood/shared-configs";
import { OrderSideType } from "../external-services/wealthkernelService";
import { BadRequestError, InternalServerError } from "../models/ApiErrors";
import { AssetRecentActivityTransactionType, TransactionService } from "./transactionService";
import { AssetType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import RewardService, { AssetRecentActivityRewardItemType } from "./rewardService";
import eodService, {
  EodETFFundamentalsResponseType,
  EodFundamentalsResponseType,
  EodSectorType,
  EodStockFundamentalsResponseType,
  EodWorldRegionType
} from "../external-services/eodService";
import {
  DISPLAY_IN_INVESTMENTS_CHART_CONFIG,
  DURATIONS_MAP,
  INTRADAY_DISPLAY_CONFIG,
  TenorEnum
} from "../configs/durationConfig";
import StatisticsConfig from "../configs/statisticsConfig";
import axios from "axios";
import Decimal from "decimal.js";
import { capitalizeFirstLetter, slugify } from "../utils/stringUtil";
import logger from "../external-services/loggerService";
import { addBreadcrumb, captureException, captureMessage } from "@sentry/node";
import IntraDayTickerService from "./intraDayTickerService";
import PortfolioService from "./portfolioService";
import CurrencyUtil from "../utils/currencyUtil";
import { PortfolioDocument, PortfolioPopulationFieldsEnum } from "../models/Portfolio";
import * as CacheUtil from "../utils/cacheUtil";
import DateUtil from "../utils/dateUtil";
import { InvestmentProductsDictType } from "investmentProducts";
import { AssetNewsDocument } from "../models/AssetNews";
import AssetNewsService from "./assetNewsService";
import { AssetPriceDataPointType } from "tickers";
import DbUtil from "../utils/dbUtil";
import { UserDocument } from "../models/User";
import PortfolioUtil from "../utils/portfolioUtil";
import ConfigUtil from "../utils/configUtil";
import { formatPercentage } from "../utils/formatterUtil";
import * as InvestmentUniverseUtil from "../utils/investmentUniverseUtil";
import { getBaseCurrency } from "../utils/investmentUniverseUtil";
import * as TickerUtil from "../utils/tickerUtil";
import { IntraDayAssetTicker } from "../models/IntraDayTicker";
import CorporateEventService from "./corporateEventService";
import EodUtil from "../utils/eodUtil";
import { PartialRecord } from "utils";
import { concatenateUniqueByKey } from "../utils/arrayUtil";
import { RedisClientService } from "../loaders/redis";

const { ASSET_CONFIG, ASSET_CLASS_CONFIG, ASSET_PROVIDER_CONFIG, SECTOR_CONFIG } = investmentUniverseConfig;
const { CURRENCY_SYMBOLS } = currenciesConfig;
const { MARKET_TRADING_HOURS } = marketHoursConfig;

type InvestmentProductDictKeyType = "commonId" | "isin" | "id";

const FUNDAMENTALS_FORMAL_TICKER_CACHE_EXPIRATION = 60 * 60 * 12; // 12 hours
const ASSET_INTRADAY_INTERVAL_MINUTES_WEEKLY = 10;
const ASSET_INTRADAY_INTERVAL_MINUTES_MONTHLY = 30;
const ASSET_INTERVAL_WEEKS_ALL_TIME = 1; // 1 week
const ASSET_ALL_TIME_SAMPLING_THRESHOLD = 502; // if more then 502 data points, then we sample
const MINIMUM_MARKET_CAP_TO_SHOW = 50000000;
const MINIMUM_REGION_DISTRIBUTION_TO_SHOW = 1; // 1%
const MINIMUM_SECTOR_DISTRIBUTION_TO_SHOW = 1; // 1%

export type EODHoldingType = { name: string; weight: string; logoUrl: string };

type BondIndexStatsType = { expectedReturn: string; annualRisk: string; coupon: string; bondYield: string };
export type NonBondIndexStatsType = {
  expectedReturn: string;
  annualRisk: string;
  fpEarnings: string;
  dividendYield: string;
};

type IndexStatsType = BondIndexStatsType | NonBondIndexStatsType;

enum AssetTagEnum {
  FRACTIONAL = "FRACTIONAL",
  ADR = "ADR",
  SMART_EXECUTION = "SMART_EXECUTION",
  COMMISSION_FREE = "COMMISSION_FREE",
  MARKET_OPEN = "MARKET_OPEN",
  MARKET_CLOSED = "MARKET_CLOSED"
}

type MarketInfoType = {
  isOpen: boolean;
  nextMarketOpen: number; // unix milliseconds
};

export type AssetDataResponseType = {
  fundamentals: AssetFundamentalsType;
  currentPrice: number;
  tradedCurrency: currenciesConfig.MainCurrencyType;
  tags: AssetTagEnum[];
  marketInfo?: MarketInfoType;
  kid?: string;
};

type AssetFundamentalsType = ETFAssetFundamentalsType | StockAssetFundamentalsType;

export type ETFAssetFundamentalsType = {
  topHoldings: EODHoldingType[];
  holdingsCount: number;
  expenseRatio: string;
  indexStats: IndexStatsType;
  baseCurrency: string;
  geographyDistribution?: ETFAssetGeographyDistributionType;
  sectorDistribution?: ETFAssetSectorDistributionType;
  about: ETFAboutType;
  news: AssetNewsDocument[];
};

export type ETFAssetGeographyDistributionType = { name: WorldRegion; percentage: number }[];

type WorldRegion =
  | "North America"
  | "Latin America"
  | "UK"
  | "Europe"
  | "Asia"
  | "Africa/Middle East"
  | "Australia";

export type ETFAssetSectorDistributionType = { name: ETFAssetSector; percentage: number }[];
type ETFAssetSector = Capitalize<investmentUniverseConfig.InvestmentSectorType> | "Real Estate";

export type StockAssetFundamentalsType = {
  analystViews: StockAnalystViewsType;
  metrics: StockMetricsType;
  about: StockAboutType;
  news: AssetNewsDocument[];
};

type AnalystViewsPositionType = "buy" | "sell" | "hold";

export type StockAnalystViewsType = {
  averagePriceTarget: string;
  priceTargetPercentageDifference: string;
  isPriceTargetPercentageDifferencePositive: boolean;
  totalAnalysts: number;
  percentageBuy: number;
  percentageSell: number;
  percentageHold: number;
  isMajority: AnalystViewsPositionType;
};

type StockAboutType = {
  ticker: string;
  exchange: string;
  sector: string;
  industry: string;
  description: string;
  employees: string;
  website: string;
  ceo: string;
  headquarters: string;
  isin: string;
};

type ETFAboutType = {
  exchange: string;
  isin: string;
  ticker: string;
  assetClass: string;
  sector: string;
  advancedName: string;
  description: string;
  provider: string;
  income: investmentUniverseConfig.ETFAssetIncomeType;
  replication: investmentUniverseConfig.ReplicationType;
  index: string;
};

type StockMetricsType = {
  marketCap: string;
  peRatio: string;
  eps: string;
  dividendYield: string;
  beta: string;
  forwardPE: string;
};

export type AssetRecentActivityItemType = AssetRecentActivityTransactionType | AssetRecentActivityRewardItemType;

export type AssetPricesWithReturnsType = {
  data: AssetPriceDataPointType[];
  returns: number;
  displayIntraday: boolean;
};

type UserInvestmentType = {
  currentValue: string;
  performanceValue: string;
  performancePercentage: string;
  numberOfShares: string;
  portfolioAllocation: string;
  isPerformancePositive: boolean;
  averagePricePerShare: string;
  totalDividends: string;
};

const WORLD_REGION_MAPPINGS: Record<EodWorldRegionType, WorldRegion> = {
  "Europe Developed": "Europe",
  "Europe Emerging": "Europe",
  "United Kingdom": "UK",
  "North America": "North America",
  "Latin America": "Latin America",
  "Asia Developed": "Asia",
  "Asia Emerging": "Asia",
  Japan: "Asia",
  "Africa/Middle East": "Africa/Middle East",
  Australasia: "Australia"
};
const SECTOR_MAPPINGS: Record<EodSectorType, ETFAssetSector> = {
  "Basic Materials": "Materials",
  "Communication Services": "Communication",
  "Consumer Cyclicals": "Consumer",
  "Consumer Defensive": "Consumer",
  "Financial Services": "Financials",
  "Real Estate": "Real Estate",
  Energy: "Energy",
  Healthcare: "Healthcare",
  Industrials: "Industrials",
  Technology: "Technology",
  Utilities: "Utilities"
};

const CACHE_EXPIRATION = 60 * 10; // 10 minutes

export default class InvestmentProductService {
  /**
   * PUBLIC METHODS
   */

  /**
   * @description Calculates and caches weekly returns for all assets in the investment universe.
   * Stores each return in Redis under key: asset:weeklyReturn:<assetId>
   */
  public static async cacheAllAssetsWeeklyReturns(): Promise<void> {
    const assetEntries = Object.entries(investmentUniverseConfig.ASSET_CONFIG).filter(
      ([asset]) => !investmentUniverseConfig.ASSET_CONFIG[asset as investmentUniverseConfig.AssetType].deprecated
    );
    const BATCH_SIZE = 10;

    for (let i = 0; i < assetEntries.length; i += BATCH_SIZE) {
      const batch = assetEntries.slice(i, i + BATCH_SIZE);
      const results = await Promise.all(
        batch.map(async ([assetIdStr]) => {
          const assetId = assetIdStr as investmentUniverseConfig.AssetType;
          try {
            const historicalPriceWeek = await eodService.getIntradayPrices(assetId, {
              from: DateUtil.getStartAndEndOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7)).start
            });

            const mondayPrice = historicalPriceWeek[0].close;
            const fridayPrice = historicalPriceWeek[historicalPriceWeek.length - 1].close;

            return [
              `asset:weeklyReturn:${assetId}`,
              PortfolioUtil.getReturns({
                startValue: new Decimal(mondayPrice),
                endValue: new Decimal(fridayPrice)
              }).toNumber()
            ];
          } catch (error) {
            logger.error(`Failed to process weekly return for asset: ${assetId}`, {
              module: "InvestmentProductService",
              method: "cacheAllAssetsWeeklyReturns",
              data: { assetId, error }
            });
            captureException(error, { extra: { assetId, scope: "cacheAllAssetsWeeklyReturns" } });
            return null;
          }
        })
      );

      const validBatchWeeklyReturns = results.filter((result) => result !== null) as [string, number][];
      if (validBatchWeeklyReturns.length > 0) {
        await RedisClientService.Instance.mSet(Object.fromEntries(validBatchWeeklyReturns));
      }
    }
  }

  /**
   * @returns the investment product document for the given commonId
   */
  public static async getInvestmentProduct(
    commonId: string,
    populateTicker: boolean
  ): Promise<InvestmentProductDocument> {
    let investmentProduct: InvestmentProductDocument;
    if (populateTicker) {
      investmentProduct = await InvestmentProduct.findOne({
        commonId: commonId as investmentUniverseConfig.AssetType
      }).populate("currentTicker");
    } else {
      investmentProduct = await InvestmentProduct.findOne({
        commonId: commonId as investmentUniverseConfig.AssetType
      });
    }

    return investmentProduct;
  }

  public static async getInvestmentProductByIsin(
    isin: string,
    populateTicker: boolean
  ): Promise<InvestmentProductDocument> {
    const commonId = InvestmentUniverseUtil.getAssetIdFromIsin(isin);

    return InvestmentProductService.getInvestmentProduct(commonId, populateTicker);
  }

  /**
   * @description Returns all the available investment products with an option to populate their ticker or not.
   * @param populateTicker boolean that indicates whether we should populate the ticker if each product
   * @param useCache boolean that indicates whether we should fetch the data from the cache or not
   * @param listedOnly
   * @returns an array of all the investment products
   */
  public static async getInvestmentProducts({
    populateTicker,
    useCache,
    listedOnly
  }: {
    populateTicker: boolean;
    useCache: boolean;
    listedOnly: boolean;
  }): Promise<InvestmentProductDocument[]> {
    const populationString = DbUtil.getPopulationString({ currentTicker: populateTicker });

    let investmentProducts: InvestmentProductDocument[];
    let deprecatedInvestmentProducts: InvestmentProductDocument[];
    if (!useCache) {
      [investmentProducts, deprecatedInvestmentProducts] = await Promise.all([
        InvestmentProduct.find({ listed: true }).populate(populationString),
        InvestmentProduct.find({ listed: false }).populate(populationString)
      ]);
    } else {
      const cacheKey = populateTicker ? "investmentProducts" : "investmentProductsWithTickers";
      const [cachedInvestmentProducts, cachedDeprecatedInvestmentProducts] = await Promise.all([
        CacheUtil.getCachedDataWithFallback<InvestmentProductInterface[]>(
          cacheKey,
          async () => {
            return InvestmentProduct.find({}).populate(populationString);
          },
          (_) => CACHE_EXPIRATION
        ),
        CacheUtil.getCachedDataWithFallback<InvestmentProductInterface[]>(
          "deprecatedInvestmentProducts",
          async () => {
            return InvestmentProduct.find({ listed: false });
          },
          (_) => CACHE_EXPIRATION
        )
      ]);

      investmentProducts = cachedInvestmentProducts.map(InvestmentProductService._hydrateInvestmentProduct);
      deprecatedInvestmentProducts = cachedDeprecatedInvestmentProducts.map(
        InvestmentProductService._hydrateInvestmentProduct
      );
    }

    // Filtering for listed only products takes place after caching so that we always cache all products, both
    // listed & deprecated.
    return [...investmentProducts, ...deprecatedInvestmentProducts].filter(
      (investmentProduct) =>
        !!ASSET_CONFIG[investmentProduct.commonId] && (listedOnly ? investmentProduct.listed : true)
    );
  }

  /**
   * @description Returns investment products in a dictionary keyed by the passed field.
   * @param key
   * @param populateTicker boolean that indicates whether we should populate the ticker if each product
   * @param options
   */
  public static async getInvestmentProductsDict(
    key: InvestmentProductDictKeyType,
    populateTicker: boolean,
    options: { listedOnly: boolean } = {
      listedOnly: true
    }
  ): Promise<InvestmentProductsDictType> {
    const investmentProducts: InvestmentProductDocument[] = await InvestmentProductService.getInvestmentProducts({
      populateTicker,
      useCache: true,
      listedOnly: options.listedOnly
    });
    if (populateTicker) {
      InvestmentProductService._validateCurrentTickerPopulation(investmentProducts);
    }

    if (key === "isin") {
      return Object.fromEntries(
        investmentProducts.map((investmentProduct) => [
          ASSET_CONFIG[investmentProduct.commonId].isin,
          investmentProduct
        ])
      );
    }

    return Object.fromEntries(
      investmentProducts.map((investmentProduct) => [investmentProduct.get(key), investmentProduct])
    );
  }

  public static async cacheAssetHistoricalPrices(): Promise<void> {
    const investmentProducts = await InvestmentProductService.getInvestmentProducts({
      populateTicker: false,
      useCache: false,
      listedOnly: true
    });
    const today = new Date(Date.now());

    await CacheUtil.fetchAndCacheDataMulti<InvestmentProductDocument>(
      investmentProducts,
      async (investmentProduct: InvestmentProductDocument): Promise<Record<string, any>> => {
        try {
          const { commonId } = investmentProduct;

          const [historicalPriceWeek, historicalPriceMonth, historicalPriceAll, stockSplit] = await Promise.all([
            eodService.getIntradayPrices(commonId, {
              from: DateUtil.getStartAndEndOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7)).start
            }),
            eodService.getIntradayPrices(commonId, {
              from: DateUtil.getStartAndEndOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 30)).start
            }),
            eodService.getHistoricalPrices(commonId, {
              from: DateUtil.getYearAndMonthAndDay(DateUtil.getDateOfYearsAgo(today, 10)),
              period: "d"
            }),
            CorporateEventService.getMostRecentStockSplit(commonId)
          ]);

          return {
            [`eod:historical:w:${commonId}`]: historicalPriceWeek,
            [`eod:historical:m:${commonId}`]: historicalPriceMonth,
            [`eod:historical:${commonId}`]: historicalPriceAll,
            [`eod:price_1d_ago:${commonId}`]: historicalPriceAll.at(-1).close,
            [`eod:price_30d_ago:${commonId}`]: TickerUtil.adjustPriceForSplit(
              historicalPriceMonth[0].close,
              stockSplit
            )
          };
        } catch (error) {
          logger.error(`Failed to process historical prices for asset: ${investmentProduct.commonId}`, {
            module: "InvestmentProductService",
            method: "cacheAssetHistoricalPrices",
            data: { assetId: investmentProduct.commonId, error }
          });
          addBreadcrumb({
            type: "default",
            category: "InvestmentProductService.cacheAssetHistoricalPrices",
            level: "info",
            data: {
              assetId: investmentProduct.commonId
            }
          });
          captureException(error);
        }
      },
      (err: any) => {
        captureException(err);
        logger.error("Caching asset historical data failed.", {
          module: "InvestmentProductService",
          method: "cacheAssetHistoricalPrices",
          data: { error: err }
        });
      }
    );
  }

  public static async cacheAssetFundamentalsData(): Promise<void> {
    const investmentProducts = await InvestmentProductService.getInvestmentProducts({
      populateTicker: false,
      useCache: true,
      listedOnly: true
    });

    await CacheUtil.fetchAndCacheDataMulti<InvestmentProductDocument>(
      investmentProducts,
      async (investmentProduct: InvestmentProductDocument): Promise<Record<string, any>> => {
        const fundamentals = await eodService.getAssetFundamentalsData(investmentProduct.commonId);

        return {
          [`eod:fundamentals:${investmentProduct.commonId}`]: fundamentals
        };
      },
      (err: any) => {
        captureException(err);
        logger.error("Caching asset fundamentals data in Redis failed.", {
          module: "InvestmentProductService",
          method: "cacheAssetFundamentalsData",
          data: { error: err }
        });
      }
    );
  }

  public static async getAssetData(
    assetCommonId: investmentUniverseConfig.AssetType,
    locale: localeConfig.LocaleType,
    companyEntity: entitiesConfig.CompanyEntityEnum,
    options?: { isRealtimeETFExecutionEnabled: boolean }
  ): Promise<AssetDataResponseType> {
    const assetCategory = ASSET_CONFIG[assetCommonId].category;

    const [assetFundamentals, investmentProduct] = await Promise.all([
      CacheUtil.getCachedDataWithFallback<EodFundamentalsResponseType>(
        `eod:fundamentals:${assetCommonId}`,
        async (): Promise<EodFundamentalsResponseType> => {
          return eodService.getAssetFundamentalsData(assetCommonId) as Promise<EodFundamentalsResponseType>;
        }
      ),
      InvestmentProductService.getInvestmentProduct(assetCommonId, true)
    ]);

    const tradedCurrency = InvestmentProductService.getMainTradedCurrency(assetCommonId);
    const latestNews = await AssetNewsService.getAssetNews(investmentProduct.id, 3);

    if (assetCategory === "etf") {
      const { formalTicker, formalExchange, fundamentalsTicker } = ASSET_CONFIG[
        assetCommonId
      ] as investmentUniverseConfig.ETFAssetConfigType;

      // For certain metrics (e.g. expense ratio), we need to retrieve EOD fundamentals from the formal ticker instead
      // of the fundamentals ticker.
      let assetFormalTickerFundamentals: EodETFFundamentalsResponseType;
      if (fundamentalsTicker !== `${formalTicker}.${formalExchange}`) {
        assetFormalTickerFundamentals = (await CacheUtil.getCachedDataWithFallback<EodFundamentalsResponseType>(
          `eod:fundamentals:formalticker:${assetCommonId}`,
          async (): Promise<EodFundamentalsResponseType> => {
            return eodService.getAssetFundamentalsData(assetCommonId, {
              useFormalTicker: true
            }) as Promise<EodFundamentalsResponseType>;
          },
          (_) => FUNDAMENTALS_FORMAL_TICKER_CACHE_EXPIRATION
        )) as EodETFFundamentalsResponseType;
      }

      return InvestmentProductService._getETFAssetData(
        assetFundamentals as EodETFFundamentalsResponseType,
        assetFormalTickerFundamentals as EodETFFundamentalsResponseType,
        tradedCurrency,
        investmentProduct,
        latestNews,
        locale,
        companyEntity,
        options?.isRealtimeETFExecutionEnabled
      );
    } else if (assetCategory === "stock") {
      return InvestmentProductService._getStockAssetData(
        assetFundamentals as EodStockFundamentalsResponseType,
        tradedCurrency,
        investmentProduct,
        latestNews,
        locale
      );
    } else {
      throw new InternalServerError(`We do not support assets of category ${assetCategory}`);
    }
  }

  public static async getPricesAndReturnsByTenor(
    assetCommonId: investmentUniverseConfig.AssetType
  ): Promise<PartialRecord<TenorEnum, AssetPricesWithReturnsType>> {
    // Fetch historical & intraday data
    // todaysAssetTickers may be an empty array
    const [todaysAssetTickers, historicalPriceWeek, historicalPriceMonth, historicalPriceAll, stockSplit] =
      await Promise.all([
        IntraDayTickerService.getTodaysTickersForAsset(assetCommonId),
        CacheUtil.getCachedDataWithFallback<{ timestamp: number; close: number }[]>(
          `eod:historical:w:${assetCommonId}`,
          async () => {
            const historicalPriceWeek = await eodService.getIntradayPrices(assetCommonId, {
              from: DateUtil.getStartAndEndOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 7)).start
            });
            return historicalPriceWeek;
          }
        ),
        CacheUtil.getCachedDataWithFallback<{ timestamp: number; close: number }[]>(
          `eod:historical:m:${assetCommonId}`,
          async () => {
            const historicalPriceMonth = await eodService.getIntradayPrices(assetCommonId, {
              from: DateUtil.getStartAndEndOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), 30)).start
            });
            return historicalPriceMonth;
          }
        ),
        CacheUtil.getCachedDataWithFallback<{ date: string; close: number }[]>(
          `eod:historical:${assetCommonId}`,
          async () => {
            const today = new Date(Date.now());
            const historicalPriceAll = await eodService.getHistoricalPrices(assetCommonId, {
              from: DateUtil.getYearAndMonthAndDay(DateUtil.getDateOfYearsAgo(today, 10)),
              period: "d"
            });
            return historicalPriceAll;
          }
        ),
        CorporateEventService.getMostRecentStockSplit(assetCommonId)
      ]);

    const historicalPriceAllWithTimestamp = historicalPriceAll.map(
      ({ date, close }: { date: string; close: number }) => ({
        timestamp: DateTime.fromISO(date, { zone: "GMT" }).toJSDate().getTime(),
        close
      })
    );
    const todaysPricesWithTimestamp = todaysAssetTickers.map(({ timestamp, tradedPrice }) => ({
      timestamp: DateTime.fromJSDate(new Date(timestamp), { zone: "GMT" }).toJSDate().getTime(),
      close: tradedPrice
    }));

    // Organise data by tenor
    return Object.fromEntries(
      Object.values(TenorEnum)
        .filter((tenor) => DISPLAY_IN_INVESTMENTS_CHART_CONFIG[tenor])
        .map((tenor) => {
          let data: {
            timestamp: number;
            close: number;
          }[];
          const todayDataExist =
            todaysPricesWithTimestamp.length > 0 ||
            (historicalPriceAllWithTimestamp.length > 0 &&
              DateUtil.isToday(new Date(historicalPriceAllWithTimestamp.at(-1)?.timestamp)));
          const dateAtTenorStart = TickerUtil.getDateAtTenorStart(tenor, new Date(Date.now()), todayDataExist);

          if (tenor === TenorEnum.ONE_WEEK) {
            data = concatenateUniqueByKey(
              TickerUtil.sampleTickers(
                historicalPriceWeek.filter(({ timestamp }) => timestamp >= dateAtTenorStart.getTime()),
                ASSET_INTRADAY_INTERVAL_MINUTES_WEEKLY
              ),
              todaysPricesWithTimestamp.map((ticker) =>
                TickerUtil.mapTickerToClosestTimeMark(ticker, ASSET_INTRADAY_INTERVAL_MINUTES_WEEKLY, "minutes")
              ),
              "timestamp"
            );

            data = TickerUtil.addPreviousDayCloseAsStartingPoint(
              assetCommonId,
              data,
              historicalPriceAllWithTimestamp
            );

            if (stockSplit) {
              data = TickerUtil.adjustPricesForSplit(data, stockSplit);
            }
            data = data.sort((pointA, pointB) => pointA.timestamp - pointB.timestamp);
          } else if (tenor === TenorEnum.ONE_MONTH) {
            data = concatenateUniqueByKey(
              TickerUtil.sampleTickers(
                historicalPriceMonth.filter(({ timestamp }) => timestamp >= dateAtTenorStart.getTime()),
                ASSET_INTRADAY_INTERVAL_MINUTES_MONTHLY
              ),
              todaysPricesWithTimestamp.map((ticker) =>
                TickerUtil.mapTickerToClosestTimeMark(ticker, ASSET_INTRADAY_INTERVAL_MINUTES_MONTHLY, "minutes")
              ),
              "timestamp"
            );

            data = TickerUtil.addPreviousDayCloseAsStartingPoint(
              assetCommonId,
              data,
              historicalPriceAllWithTimestamp
            );

            if (stockSplit) {
              data = TickerUtil.adjustPricesForSplit(data, stockSplit);
            }
            data = data.sort((pointA, pointB) => pointA.timestamp - pointB.timestamp);
          } else if (tenor === TenorEnum.ALL_TIME) {
            data = [...historicalPriceAllWithTimestamp];

            if (data.length >= ASSET_ALL_TIME_SAMPLING_THRESHOLD) {
              data = TickerUtil.sampleTickers(data, ASSET_INTERVAL_WEEKS_ALL_TIME, "weeks");
            }

            const latestPrice = todaysPricesWithTimestamp.slice(-1)[0];
            if (latestPrice) {
              data.push({ ...latestPrice });
            }
          } else {
            data = historicalPriceAllWithTimestamp.filter(
              ({ timestamp }: { timestamp: number; close: number }) =>
                timestamp >= new Date(dateAtTenorStart).getTime()
            );
            data = TickerUtil.addPreviousDayCloseAsStartingPoint(
              assetCommonId,
              data,
              historicalPriceAllWithTimestamp
            );

            const latestPrice = todaysPricesWithTimestamp.slice(-1)[0];
            if (latestPrice) {
              data.push({ ...latestPrice });
            }
          }

          return [
            tenor,
            {
              data,
              returns: data.length > 0 ? PortfolioUtil.getReturnsOnArray(data.map(({ close }) => close)) : 0,
              displayIntraday: INTRADAY_DISPLAY_CONFIG[tenor]
            }
          ];
        })
    ) as PartialRecord<TenorEnum, AssetPricesWithReturnsType>;
  }

  public static getMainTradedCurrency(
    assetId: investmentUniverseConfig.AssetType
  ): currenciesConfig.MainCurrencyType {
    /**
     * We use ? optional operator here, because in some rare-cases the asset might not be present in the config.
     * This happens in some tests.
     */
    const tradedCurrency = ASSET_CONFIG[assetId]?.tradedCurrency;
    return CurrencyUtil.getMainCurrency(tradedCurrency);
  }

  /**
   * @description Returns a boolean that indicates whether an asset is within market hours.
   *
   * An argument, includeAfterCloseMinutes can be passed to return true even if slightly later than trading hours close.
   * i.e. passing includeAfterCloseMinutes -> 30 will return true up to 30 minutes after trading close.
   *
   * An argument, doNotIncludeAfterOpenMinutes can be passed to return false even if slightly later than trading hours open.
   * i.e. passing doNotIncludeAfterOpenMinutes -> 30 will return false up to 30 minutes after trading open.
   *
   * NOTE: This method has not been implemented to take into account non-UK or non-US bank holidays
   */
  public static isAssetCurrentlyTraded(
    assetCommonId: investmentUniverseConfig.AssetType,
    options?: { doNotIncludeAfterOpenMinutes?: number; includeAfterCloseMinutes?: number }
  ): boolean {
    if (ASSET_CONFIG[assetCommonId].deprecated) {
      return false;
    }

    const { formalExchange } = ASSET_CONFIG[assetCommonId];

    if (DateUtil.isWeekend()) return false;
    if (formalExchange === "LSE" && !DateUtil.isUKWorkDay(new Date(Date.now()))) return false;
    if (formalExchange === "US" && !DateUtil.isUSWorkDay(new Date(Date.now()))) return false;

    const currentTime = DateTime.utc();
    const marketOpening = DateTime.utc().setZone(MARKET_TRADING_HOURS[formalExchange].timeZone).set({
      hour: MARKET_TRADING_HOURS[formalExchange].start.HOUR,
      minute: MARKET_TRADING_HOURS[formalExchange].start.MINUTES
    });
    const marketClosing = DateTime.utc().setZone(MARKET_TRADING_HOURS[formalExchange].timeZone).set({
      hour: MARKET_TRADING_HOURS[formalExchange].end.HOUR,
      minute: MARKET_TRADING_HOURS[formalExchange].end.MINUTES
    });

    return (
      currentTime.toMillis() >=
        marketOpening.toMillis() + (options?.doNotIncludeAfterOpenMinutes ?? 0) * 60 * 1000 &&
      currentTime.toMillis() <= marketClosing.toMillis() + (options?.includeAfterCloseMinutes ?? 0) * 60 * 1000
    );
  }

  public static async pauseOrders(assetDatabaseId: string, side: OrderSideType): Promise<void> {
    const investmentProduct: InvestmentProductDocument = await InvestmentProduct.findById(assetDatabaseId);

    if (side === "Buy") {
      if (!investmentProduct.buyLine.active) {
        throw new BadRequestError(`Investment product ${investmentProduct.id} is already paused for buys`);
      }

      await InvestmentProduct.findByIdAndUpdate(assetDatabaseId, { "buyLine.active": false });
    } else if (side === "Sell") {
      if (!investmentProduct.sellLine.active) {
        throw new BadRequestError(`Investment product ${investmentProduct.id} is already paused for sells`);
      }

      await InvestmentProduct.findByIdAndUpdate(assetDatabaseId, { "sellLine.active": false });
    }
  }

  public static async resumeOrders(assetDatabaseId: string, side: OrderSideType): Promise<void> {
    const investmentProduct: InvestmentProductDocument = await InvestmentProduct.findById(assetDatabaseId);

    if (side === "Buy") {
      if (investmentProduct.buyLine.active) {
        throw new BadRequestError(`Investment product ${investmentProduct.id} is already active for buys`);
      }

      await InvestmentProduct.findByIdAndUpdate(assetDatabaseId, { "buyLine.active": true });
    } else if (side === "Sell") {
      if (investmentProduct.sellLine.active) {
        throw new BadRequestError(`Investment product ${investmentProduct.id} is already active for sells`);
      }

      await InvestmentProduct.findByIdAndUpdate(assetDatabaseId, { "sellLine.active": true });
    }
  }

  public static async isActive(
    commonId: investmentUniverseConfig.AssetType,
    side: OrderSideType
  ): Promise<boolean> {
    const investmentProduct: InvestmentProductDocument = await InvestmentProduct.findOne({
      commonId
    });

    return side === "Buy" ? investmentProduct.buyLine.active : investmentProduct.sellLine.active;
  }

  public static async getAssetRecentActivity(
    userId: string,
    assetId: AssetType,
    limit?: number
  ): Promise<AssetRecentActivityItemType[]> {
    const [assetRecentActivityTransactions, assetRecentActivityRewards] = await Promise.all([
      TransactionService.getAssetRecentActivityTransactions(userId, assetId),
      RewardService.getAssetRecentActivityRewards(userId, assetId, limit)
    ]);

    const assetRecentActivityItems = [...assetRecentActivityTransactions, ...assetRecentActivityRewards];

    let sortedItems = assetRecentActivityItems.sort((a, b) => {
      if (a.item.displayDate < b.item.displayDate) {
        return 1;
      } else if (a.item.displayDate > b.item.displayDate) {
        return -1;
      }
    });

    if (limit) {
      sortedItems = sortedItems.slice(0, limit);
    }

    return sortedItems;
  }

  public static async getUserInvestmentDetails(
    portfolio: PortfolioDocument,
    assetId: AssetType
  ): Promise<UserInvestmentType> {
    if (!portfolio.populated("holdings.asset")) {
      await portfolio.populate([
        { path: "currentTicker" },
        {
          path: "holdings.asset",
          populate: {
            path: "currentTicker"
          }
        }
      ]);
    }

    const userId = portfolio.populated("owner") ? portfolio.owner.id : portfolio.owner;

    const [dividendsForReturns, rewards, investmentProduct, totalDividends] = await Promise.all([
      TransactionService.getDividendTransactionsForReturnsUpBy(userId),
      RewardService.getSettledRewards(userId),
      InvestmentProductService.getInvestmentProduct(assetId, true),
      TransactionService.getTotalDividendsForAsset(assetId, userId),
      DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER)
    ]);
    const dividendTransactions = dividendsForReturns.filter((transaction) => transaction.asset === assetId);
    const user = portfolio.owner as UserDocument;

    const holding = portfolio.holdings.find((holding) => holding.assetCommonId === assetId);
    const [holdingReturnsPerformance, averagePricePerShare] = await Promise.all([
      PortfolioService.getAssetReturns(portfolio, holding, {
        dividendTransactions,
        rewards
      }),
      PortfolioService.getAveragePricePerShare(assetId, portfolio, {
        rewards
      })
    ]);

    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);
    const currentValueInUserCurrency = Decimal.mul(
      investmentProduct.currentTicker.pricePerCurrency[user.currency],
      holding.quantity
    ).toNumber();

    // Metric: Portfolio allocation
    const assetAllocation = PortfolioUtil.mapHoldingsToAllocationFormat(user.currency, portfolio.holdings).assets;
    const portfolioAllocation = formatPercentage(
      Decimal.div(assetAllocation[holding.assetCommonId], 100).toNumber(),
      locale
    );

    // Metric: number of shares
    const numberOfShares = holding.quantity.toLocaleString(locale, { maximumFractionDigits: 4 });

    // Metric: % upby/downby
    const holdingUpBy = await PortfolioService.getHoldingUpByValue(portfolio, holding, {
      dividendTransactions,
      rewards
    });
    let performancePercentage = "";
    if (holdingReturnsPerformance < 0) {
      performancePercentage = `Down by ${formatPercentage(holdingReturnsPerformance, locale, 2, 2, "never")}`;
    } else if (holdingReturnsPerformance > 0) {
      performancePercentage = `Up by ${formatPercentage(holdingReturnsPerformance, locale, 2, 2, "never")}`;
    }

    // Metric: Performance calculated in £ up/down-by
    let holdingValuePerformance = CurrencyUtil.formatCurrency(
      holdingUpBy,
      user.currency,
      ConfigUtil.getDefaultUserLocale(user.residencyCountry)
    );
    if (!holdingValuePerformance.startsWith("-")) {
      holdingValuePerformance = `+${holdingValuePerformance}`;
    }

    return {
      currentValue: CurrencyUtil.formatCurrency(
        currentValueInUserCurrency,
        user.currency,
        ConfigUtil.getDefaultUserLocale(user.residencyCountry)
      ),
      portfolioAllocation,
      numberOfShares,
      performanceValue: holdingValuePerformance,
      performancePercentage,
      isPerformancePositive: holdingReturnsPerformance > 0,
      averagePricePerShare: CurrencyUtil.formatCurrency(
        averagePricePerShare.priceInTradedCurrency,
        CurrencyUtil.getMainCurrency(ASSET_CONFIG[assetId].tradedCurrency),
        ConfigUtil.getDefaultUserLocale(user.residencyCountry),
        true
      ),
      totalDividends: CurrencyUtil.formatCurrency(
        totalDividends,
        user.currency,
        ConfigUtil.getDefaultUserLocale(user.residencyCountry),
        true
      )
    };
  }

  /**
   * @description this method takes the world region distribution for an asset returned from EOD, and converts it to our
   * structure. To do this, we:
   * 1. Convert their regions into our regions
   * 2. Remove all regions with < 1% percentage
   * 3. Update to two decimal points
   * 4. Normalise and assign any remainder to the top region
   * @param response
   * @private
   */
  public static getGeographyDistribution(
    response: EodETFFundamentalsResponseType
  ): ETFAssetGeographyDistributionType {
    // If EOD does not have weights for this asset, we return immediately.
    const eodWorldRegionWeights = response.ETF_Data.World_Regions;
    if (
      !eodWorldRegionWeights ||
      Object.entries(eodWorldRegionWeights).every(([, { "Equity_%": percentage }]) => percentage === "0")
    ) {
      return;
    }

    const worldRegions = Object.entries(eodWorldRegionWeights)
      .map(([eodRegion, { "Equity_%": percentage }]) => {
        // We map the EOD regions to our own.
        return {
          region: WORLD_REGION_MAPPINGS[eodRegion as EodWorldRegionType],
          percentage: new Decimal(percentage)
        };
      })
      .reduce((accumulator, current) => {
        // Because same EOD regions map to the same Wealthyhood region, the previous operation has created multiple
        // entries for the same Wealthyhood region, which we sum up here.
        const region = current.region;
        const percentage = current.percentage;
        const existingRegion = accumulator.find((mapping) => mapping.region === region);

        if (existingRegion) {
          existingRegion.percentage = existingRegion.percentage.add(percentage);
        } else accumulator.push(current);

        return accumulator;
      }, [])
      .filter(({ percentage }) => percentage.greaterThan(MINIMUM_REGION_DISTRIBUTION_TO_SHOW));

    // After mapping, reducing and filtering, we normalise the remaining weight entries to 100.
    const totalPercentage = worldRegions
      .map(({ percentage }) => percentage)
      .reduce((sum, estimatedAmount) => Decimal.add(sum, estimatedAmount), new Decimal(0));

    const normalisedAndSortedWorldRegions = worldRegions
      .map(({ region, percentage }) => {
        const normalizedPercentage = Decimal.div(percentage, totalPercentage)
          .mul(100)
          .toDecimalPlaces(2, Decimal.ROUND_DOWN);

        return {
          region,
          percentage: normalizedPercentage
        };
      })
      .sort((regionA, regionB) => {
        return regionB.percentage.sub(regionA.percentage).toNumber();
      });

    const totalPercentageAfterNormalization = normalisedAndSortedWorldRegions
      .map(({ percentage }) => percentage)
      .reduce((sum, estimatedAmount) => Decimal.add(sum, estimatedAmount), new Decimal(0));

    if (totalPercentageAfterNormalization.lessThan(100)) {
      const diff = new Decimal(100).sub(totalPercentageAfterNormalization);
      const highestPercentageRegion = normalisedAndSortedWorldRegions[0];

      normalisedAndSortedWorldRegions[0] = {
        region: highestPercentageRegion.region,
        percentage: highestPercentageRegion.percentage.add(diff)
      };
    }

    return normalisedAndSortedWorldRegions.map(({ region, percentage }) => {
      return { name: region, percentage: percentage.toNumber() };
    });
  }

  /**
   * @description this method behaves similarly to {@link getGeographyDistribution} but for sector weights.
   * @param response
   * @private
   */
  public static getSectorDistribution(response: EodETFFundamentalsResponseType): ETFAssetSectorDistributionType {
    // If EOD does not have weights for this asset, we return immediately.
    const eodSectorWeights = response.ETF_Data.Sector_Weights;
    if (
      !eodSectorWeights ||
      Object.entries(eodSectorWeights).every(([, { "Equity_%": percentage }]) => percentage === "0")
    ) {
      return;
    }

    const sectors = Object.entries(eodSectorWeights)
      .map(([eodSector, { "Equity_%": percentage }]) => {
        // We map the EOD sectors to our own.
        return {
          sector: SECTOR_MAPPINGS[eodSector as EodSectorType],
          percentage: new Decimal(percentage)
        };
      })
      .reduce((accumulator, current) => {
        // Because same EOD sectors map to the same Wealthyhood sector, the previous operation has created multiple
        // entries for the same Wealthyhood sector, which we sum up here.
        const sector = current.sector;
        const percentage = current.percentage;
        const existingRegion = accumulator.find((mapping) => mapping.sector === sector);

        if (existingRegion) {
          existingRegion.percentage = existingRegion.percentage.add(percentage);
        } else accumulator.push(current);

        return accumulator;
      }, [])
      .filter(({ percentage }) => percentage.greaterThan(MINIMUM_SECTOR_DISTRIBUTION_TO_SHOW));

    const totalPercentage = sectors
      .map(({ percentage }) => percentage)
      .reduce((sum, estimatedAmount) => Decimal.add(sum, estimatedAmount), new Decimal(0));

    const normalisedAndSortedSectors = sectors
      .map(({ sector, percentage }) => {
        const normalizedPercentage = Decimal.div(percentage, totalPercentage)
          .mul(100)
          .toDecimalPlaces(2, Decimal.ROUND_DOWN);

        return {
          sector,
          percentage: normalizedPercentage
        };
      })
      .sort((sectorA, sectorB) => {
        return sectorB.percentage.sub(sectorA.percentage).toNumber();
      });

    const totalPercentageAfterNormalization = normalisedAndSortedSectors
      .map(({ percentage }) => percentage)
      .reduce((sum, estimatedAmount) => Decimal.add(sum, estimatedAmount), new Decimal(0));

    if (totalPercentageAfterNormalization.lessThan(100)) {
      const diff = new Decimal(100).sub(totalPercentageAfterNormalization);
      const highestPercentageRegion = normalisedAndSortedSectors[0];

      normalisedAndSortedSectors[0] = {
        sector: highestPercentageRegion.sector,
        percentage: highestPercentageRegion.percentage.add(diff)
      };
    }

    return normalisedAndSortedSectors.map(({ sector, percentage }) => {
      return { name: sector, id: slugify(sector), percentage: percentage.toNumber() };
    });
  }

  public static getStockAnalystViews(
    response: EodStockFundamentalsResponseType,
    tradedCurrency: currenciesConfig.MainCurrencyType,
    userLocale: localeConfig.LocaleType,
    currentPrice?: number,
    options: { includeTargetPercentageDifference: boolean } = { includeTargetPercentageDifference: false }
  ): StockAnalystViewsType {
    const totalAnalysts = EodUtil.getTotalAnalysts(response.AnalystRatings);
    if (
      totalAnalysts === 0 ||
      (!currentPrice && options?.includeTargetPercentageDifference) ||
      !response.Highlights?.WallStreetTargetPrice
    ) {
      return;
    }

    const totalBuyAnalystViews = response.AnalystRatings.StrongBuy + response.AnalystRatings.Buy;
    const totalSellAnalystViews = response.AnalystRatings.StrongSell + response.AnalystRatings.Sell;
    const totalHoldAnalystViews = response.AnalystRatings.Hold;

    // We also consider analyst ratings as missing data if any of buy/sell/hold are null/undefined.
    if (
      [totalSellAnalystViews, totalHoldAnalystViews, totalBuyAnalystViews].some(
        (value) => value === null || value === undefined
      )
    ) {
      return;
    }

    // We find the percentages for buy, sell, and hold. Since we remove decimals from the percentages, if the total
    // is less than 100 after rounding, we add the remainder to the buy percentage.
    const percentageBuy = new Decimal(totalBuyAnalystViews).div(totalAnalysts).mul(100).floor();
    const percentageSell = new Decimal(totalSellAnalystViews).div(totalAnalysts).mul(100).floor().toNumber();
    const percentageHold = new Decimal(totalHoldAnalystViews).div(totalAnalysts).mul(100).floor().toNumber();

    let finalPercentageBuy = percentageBuy.toNumber();
    if (new Decimal(percentageBuy).add(percentageSell).add(percentageHold).lessThan(100)) {
      const differenceFromTotal = new Decimal(100).sub(percentageSell).sub(percentageHold).sub(percentageBuy);
      finalPercentageBuy = percentageBuy.add(differenceFromTotal).toNumber();
    }

    let isMajority: AnalystViewsPositionType;
    if (finalPercentageBuy >= percentageSell && finalPercentageBuy >= percentageHold) {
      isMajority = "buy";
    } else if (percentageSell >= finalPercentageBuy && percentageSell >= percentageHold) {
      isMajority = "sell";
    } else {
      isMajority = "hold";
    }

    let formattedPriceTargetPercentageDifference: string;
    let isPriceTargetPercentageDifferencePositive: boolean;
    if (options?.includeTargetPercentageDifference) {
      const priceTargetPercentageDifference = new Decimal(response.Highlights.WallStreetTargetPrice)
        .div(currentPrice)
        .sub(1);

      isPriceTargetPercentageDifferencePositive = priceTargetPercentageDifference.isPositive();
      formattedPriceTargetPercentageDifference = formatPercentage(
        priceTargetPercentageDifference.toNumber(),
        userLocale
      );
      if (isPriceTargetPercentageDifferencePositive) {
        formattedPriceTargetPercentageDifference = `+${formattedPriceTargetPercentageDifference}`;
      }
    }

    return {
      averagePriceTarget: CurrencyUtil.formatCurrency(
        response.Highlights.WallStreetTargetPrice,
        tradedCurrency,
        userLocale,
        true
      ),
      priceTargetPercentageDifference: formattedPriceTargetPercentageDifference,
      isPriceTargetPercentageDifferencePositive: isPriceTargetPercentageDifferencePositive,
      totalAnalysts,
      percentageBuy: finalPercentageBuy,
      percentageSell,
      percentageHold,
      isMajority
    };
  }

  /**
   * PRIVATE METHODS
   */

  private static async _getETFAssetData(
    etfAssetFundamentals: EodETFFundamentalsResponseType,
    etfAssetFormalTickerFundamentals: EodETFFundamentalsResponseType,
    tradedCurrency: currenciesConfig.MainCurrencyType,
    investmentProduct: InvestmentProductDocument,
    news: AssetNewsDocument[],
    userLocale: localeConfig.LocaleType,
    companyEntity: entitiesConfig.CompanyEntityEnum,
    isRealtimeETFExecutionEnabled: boolean
  ): Promise<AssetDataResponseType> {
    const holdingsCount = InvestmentProductService._getHoldingsCount(etfAssetFundamentals);
    const expenseRatio = InvestmentProductService._getExpenseRatio(
      etfAssetFormalTickerFundamentals ?? etfAssetFundamentals,
      userLocale
    );
    const assetClass = ASSET_CONFIG[investmentProduct.commonId].assetClass;
    const replication = (ASSET_CONFIG[investmentProduct.commonId] as investmentUniverseConfig.ETFAssetConfigType)
      .replication;

    const [topHoldings, indexStats] = await Promise.all([
      ["commodities", "realEstate", "readyMade"].includes(assetClass) || replication === "Synthetic"
        ? undefined
        : EodUtil.getTopHoldings(etfAssetFundamentals, investmentProduct.commonId, userLocale),
      InvestmentProductService._getIndexStats(investmentProduct.commonId, etfAssetFundamentals, userLocale)
    ]);
    const baseCurrency = getBaseCurrency(investmentProduct.commonId);
    const geographyDistribution = InvestmentProductService.getGeographyDistribution(etfAssetFundamentals);
    const sectorDistribution = InvestmentProductService.getSectorDistribution(etfAssetFundamentals);
    const about = InvestmentProductService._getETFAbout(investmentProduct.commonId, companyEntity);
    const assetTags = InvestmentProductService._getAssetTags(investmentProduct.commonId, {
      isRealtimeETFExecutionEnabled: isRealtimeETFExecutionEnabled
    });
    const kid = (ASSET_CONFIG[investmentProduct.commonId] as investmentUniverseConfig.ETFAssetConfigType).kid;
    const marketInfo = InvestmentProductService._getMarketInfo(investmentProduct.commonId);

    return {
      fundamentals: {
        topHoldings,
        expenseRatio,
        indexStats,
        holdingsCount,
        baseCurrency,
        geographyDistribution,
        sectorDistribution,
        about,
        news
      },
      tradedCurrency,
      currentPrice: investmentProduct.currentTicker.tradedPrice,
      tags: assetTags,
      marketInfo,
      kid
    };
  }

  private static async _getStockAssetData(
    stockAssetFundamentals: EodStockFundamentalsResponseType,
    tradedCurrency: currenciesConfig.MainCurrencyType,
    investmentProduct: InvestmentProductDocument,
    news: AssetNewsDocument[],
    userLocale: localeConfig.LocaleType
  ): Promise<AssetDataResponseType> {
    const analystViews = InvestmentProductService.getStockAnalystViews(
      stockAssetFundamentals,
      tradedCurrency,
      userLocale,
      investmentProduct.currentTicker.tradedPrice,
      { includeTargetPercentageDifference: true }
    );
    const about = InvestmentProductService._getStockAbout(
      stockAssetFundamentals,
      investmentProduct.commonId,
      userLocale
    );
    const metrics = InvestmentProductService._getStockMetrics(
      stockAssetFundamentals,
      investmentProduct.commonId,
      userLocale
    );
    const assetTags = InvestmentProductService._getAssetTags(investmentProduct.commonId);
    const marketInfo = InvestmentProductService._getMarketInfo(investmentProduct.commonId);

    return {
      fundamentals: {
        about,
        metrics,
        analystViews,
        news
      },
      tradedCurrency,
      currentPrice: investmentProduct.currentTicker.tradedPrice,
      tags: assetTags,
      marketInfo
    };
  }

  private static _getExpenseRatio(
    response: EodETFFundamentalsResponseType,
    userLocale: localeConfig.LocaleType
  ): string {
    return parseFloat(response.ETF_Data.Ongoing_Charge).toLocaleString(userLocale);
  }

  private static _getHoldingsCount(response: EodETFFundamentalsResponseType): number {
    return response.ETF_Data.Holdings_Count;
  }

  private static _getStockAbout(
    response: EodStockFundamentalsResponseType,
    assetCommonId: investmentUniverseConfig.AssetType,
    userLocale: localeConfig.LocaleType
  ): StockAboutType {
    const headquarters = response.General.AddressData
      ? `${response.General.AddressData.City}, ${response.General.AddressData.Country}`
      : "-";

    return {
      ticker: ASSET_CONFIG[assetCommonId].tickerWithCurrency,
      exchange: response.General.Exchange ?? "-",
      sector: capitalizeFirstLetter(ASSET_CONFIG[assetCommonId].sector),
      industry: response.General.GicIndustry ?? "-",
      description: response.General.Description ?? "-",
      employees:
        response.General.FullTimeEmployees > 500
          ? response.General.FullTimeEmployees.toLocaleString(userLocale) // Adds thousand separators in numbers
          : "-",
      website: response.General.WebURL?.replace("https://www.", "") ?? "-",
      ceo: EodUtil.getCEO(response),
      headquarters,
      isin: ASSET_CONFIG[assetCommonId].isin
    };
  }

  private static _getETFAbout(
    assetCommonId: investmentUniverseConfig.AssetType,
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): ETFAboutType {
    const exchange = (ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType).displayExchange;
    const isin = ASSET_CONFIG[assetCommonId].isin;
    const ticker = ASSET_CONFIG[assetCommonId].tickerWithCurrency;
    const assetClass = ASSET_CLASS_CONFIG[companyEntity]?.[ASSET_CONFIG[assetCommonId].assetClass]?.fieldName;
    const sector = SECTOR_CONFIG[companyEntity]?.[ASSET_CONFIG[assetCommonId].sector]?.fieldName;
    const advancedName = (ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType).simpleName;
    const description = (ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType).about;
    const provider =
      ASSET_PROVIDER_CONFIG[(ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType).provider]
        .displayName;
    const income = (ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType).income;
    const replication = (ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType).replication;
    const index = (ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType).index;

    return {
      exchange,
      isin,
      ticker,
      assetClass,
      sector,
      advancedName,
      description,
      provider,
      income,
      replication,
      index
    };
  }

  private static _getStockMetrics(
    response: EodStockFundamentalsResponseType,
    assetCommonId: investmentUniverseConfig.AssetType,
    userLocale: localeConfig.LocaleType
  ): StockMetricsType {
    const tradedCurrency = InvestmentProductService.getMainTradedCurrency(assetCommonId);
    const formattedMarketCap = InvestmentProductService._getFormattedMarketCap(
      response.Highlights.MarketCapitalization,
      tradedCurrency,
      userLocale
    );

    return {
      marketCap: formattedMarketCap,
      beta: response.Technicals.Beta
        ? new Decimal(response.Technicals.Beta).toNumber().toLocaleString(userLocale, { maximumFractionDigits: 2 })
        : "-",
      peRatio: response.Highlights.PERatio
        ? new Decimal(response.Highlights.PERatio)
            .toNumber()
            .toLocaleString(userLocale, { maximumFractionDigits: 2 })
        : "-",
      eps: CurrencyUtil.formatCurrency(response.Highlights.EarningsShare, tradedCurrency, userLocale),
      forwardPE: response.Valuation.ForwardPE
        ? new Decimal(response.Valuation.ForwardPE)
            .toNumber()
            .toLocaleString(userLocale, { maximumFractionDigits: 2 })
        : "-",
      dividendYield: response.Highlights.DividendYield
        ? formatPercentage(response.Highlights.DividendYield, userLocale)
        : "-"
    };
  }

  /**
   * This method converts numbers such as 2975405965312 to a format such as 1.23T / 12.5B / 152M to show
   * in the stock page.
   * @param marketCap
   * @param tradedCurrency
   * @param userLocale
   * @private
   */
  private static _getFormattedMarketCap(
    marketCap: number,
    tradedCurrency: currenciesConfig.CurrencyType,
    userLocale: localeConfig.LocaleType
  ): string {
    if (!marketCap || marketCap < MINIMUM_MARKET_CAP_TO_SHOW) {
      return "-";
    }

    const marketCapDecimal = new Decimal(marketCap);
    const formatOptions = [
      { threshold: 1e8, divisor: 1e6, suffix: "M", maximumFractionDigits: 1 },
      { threshold: 1e9, divisor: 1e6, suffix: "M", maximumFractionDigits: 0 },
      { threshold: 1e10, divisor: 1e9, suffix: "B", maximumFractionDigits: 2 },
      { threshold: 1e11, divisor: 1e9, suffix: "B", maximumFractionDigits: 1 },
      { threshold: 1e12, divisor: 1e9, suffix: "B", maximumFractionDigits: 0 },
      { threshold: 1e13, divisor: 1e12, suffix: "T", maximumFractionDigits: 2 },
      { threshold: 1e14, divisor: 1e12, suffix: "T", maximumFractionDigits: 1 }
    ];

    for (const { threshold, divisor, suffix, maximumFractionDigits } of formatOptions) {
      if (marketCapDecimal.lessThan(threshold)) {
        const formattedNumber = marketCapDecimal.div(divisor).toNumber().toLocaleString(userLocale, {
          maximumFractionDigits
        });
        return `${CURRENCY_SYMBOLS[tradedCurrency]}${formattedNumber}${suffix}`;
      }
    }

    return "-";
  }

  private static async _getIndexStats(
    assetCommonId: investmentUniverseConfig.AssetType,
    eodResponse: EodETFFundamentalsResponseType,
    userLocale: localeConfig.LocaleType
  ): Promise<IndexStatsType> {
    const indexStatsMethod =
      ASSET_CONFIG[assetCommonId].assetClass === "bonds"
        ? InvestmentProductService._getIndexStatsBonds
        : InvestmentProductService._getIndexStatsNonBonds;
    return indexStatsMethod(assetCommonId, eodResponse, userLocale);
  }

  private static async _getIndexStatsBonds(
    assetCommonId: investmentUniverseConfig.AssetType,
    eodResponse: any,
    userLocale: localeConfig.LocaleType
  ): Promise<{ expectedReturn: string; annualRisk: string; coupon: string; bondYield: string }> {
    const response = await axios.get(StatisticsConfig.URLS.indexStatsV3, {
      headers: { "Content-Type": "application/json" },
      params: {
        asset: assetCommonId
      }
    });

    // Expected returns + Risk
    const expectedReturn = formatPercentage(
      Decimal.div(response.data.expected_return, 100).toNumber(),
      userLocale
    );
    const annualRisk = formatPercentage(Decimal.div(response.data.annual_risk, 100).toNumber(), userLocale);

    // Bond yield + Coupon
    const bondYield = Decimal.div(eodResponse.ETF_Data.Fixed_Income.YieldToMaturity["Fund_%"], 100)
      .toNumber()
      .toLocaleString(userLocale, {
        style: "percent",
        maximumFractionDigits: 2
      });
    const coupon = Decimal.div(eodResponse.ETF_Data.Fixed_Income.Coupon["Fund_%"], 100)
      .toNumber()
      .toLocaleString(userLocale, {
        style: "percent",
        maximumFractionDigits: 2
      });

    return { expectedReturn, annualRisk, bondYield, coupon };
  }

  private static async _getIndexStatsNonBonds(
    assetCommonId: investmentUniverseConfig.AssetType,
    eodResponse: any,
    userLocale: localeConfig.LocaleType
  ): Promise<{ expectedReturn: string; annualRisk: string; fpEarnings: string; dividendYield: string }> {
    const response = await axios.get(StatisticsConfig.URLS.indexStatsV3, {
      headers: { "Content-Type": "application/json" },
      params: {
        asset: assetCommonId
      }
    });

    // Expected returns + Risk
    const expectedReturn = formatPercentage(
      Decimal.div(response.data.expected_return, 100).toNumber(),
      userLocale
    );
    const annualRisk = formatPercentage(Decimal.div(response.data.annual_risk, 100).toNumber(), userLocale);

    // Forward Price earnings + Dividend yield
    const unformattedFpEarnings =
      eodResponse.ETF_Data.Valuations_Growth.Valuations_Rates_Portfolio["Price/Prospective Earnings"];
    const fpEarnings = unformattedFpEarnings
      ? new Decimal(unformattedFpEarnings).toDecimalPlaces(2).toNumber().toLocaleString(userLocale)
      : "-";

    const unformattedDividendYield =
      eodResponse.ETF_Data.Valuations_Growth.Valuations_Rates_Portfolio["Dividend-Yield Factor"];
    const dividendYield = unformattedDividendYield
      ? Decimal.div(unformattedDividendYield, 100).toNumber().toLocaleString(userLocale, {
          style: "percent",
          maximumFractionDigits: 2
        })
      : "-";

    return { expectedReturn, annualRisk, fpEarnings, dividendYield };
  }

  private static _validateCurrentTickerPopulation(investmentProducts: InvestmentProductDocument[]): void {
    const productsWithoutTicker = investmentProducts
      .filter((investmentProduct) => investmentProduct.listed)
      .filter(
        (investmentProduct: InvestmentProductDocument) => !investmentProduct?.currentTicker?.pricePerCurrency
      );

    if (productsWithoutTicker.length > 0) {
      const assetIDs = productsWithoutTicker.map(({ commonId }) => commonId);

      addBreadcrumb({
        type: "default",
        category: "InvestmentProductService._validateCurrentTickerPopulation",
        level: "info",
        data: {
          assetIDs
        }
      });
      logger.error("Ticker population failed for some investment products", {
        module: "InvestmentProductService",
        method: "_validateCurrentTickerPopulation",
        data: { assetIDs }
      });
      captureMessage("Failed to populate all tickers");
    }
  }

  private static _getAssetTags(
    assetCommonId: investmentUniverseConfig.AssetType,
    options?: {
      isRealtimeETFExecutionEnabled: boolean;
    }
  ): AssetTagEnum[] {
    const assetTags = [];

    const isWithinMarketHours = InvestmentProductService.isAssetCurrentlyTraded(assetCommonId);

    // 1. [Only for stocks] Add commission free & market open/closed asset tag
    const { category, tags } = ASSET_CONFIG[assetCommonId];
    if (category === "stock") {
      assetTags.push(AssetTagEnum.COMMISSION_FREE);
      assetTags.push(isWithinMarketHours ? AssetTagEnum.MARKET_OPEN : AssetTagEnum.MARKET_CLOSED);
    }

    // 2. [Only for ETFs] If ETF realtime execution is enabled, add smart execution tag & market open/closed. If not, only add commission-free tag.
    if (category === "etf") {
      if (options?.isRealtimeETFExecutionEnabled) {
        assetTags.push(AssetTagEnum.SMART_EXECUTION);
        assetTags.push(isWithinMarketHours ? AssetTagEnum.MARKET_OPEN : AssetTagEnum.MARKET_CLOSED);
      } else {
        assetTags.push(AssetTagEnum.COMMISSION_FREE);
      }
    }

    // 3. Add American Deposit Receipt (ADR) tag
    if (tags.includes("adr")) {
      assetTags.push(AssetTagEnum.ADR);
    }

    // 4. Add fractional investment tag
    if (tags.includes("fractional")) {
      assetTags.push(AssetTagEnum.FRACTIONAL);
    }

    return assetTags;
  }

  private static _getMarketInfo(assetCommonId: investmentUniverseConfig.AssetType): MarketInfoType {
    const { formalExchange, category } = ASSET_CONFIG[assetCommonId];

    /**
     * Edge case:
     * When the current time is before the market opening time, we want to return today's market opening time.
     */
    const currentTime = DateTime.utc();
    const marketOpening = DateTime.utc().setZone(MARKET_TRADING_HOURS[formalExchange].timeZone).set({
      hour: MARKET_TRADING_HOURS[formalExchange].start.HOUR,
      minute: MARKET_TRADING_HOURS[formalExchange].start.MINUTES
    });

    const { isWorkDay, calculateNextWorkDay } =
      category === "etf"
        ? { isWorkDay: DateUtil.isUKWorkDay, calculateNextWorkDay: DateUtil.calculateNextUKWorkDay }
        : {
            isWorkDay: DateUtil.isUSWorkDay,
            calculateNextWorkDay: DateUtil.calculateNextUSWorkDay
          };

    if (isWorkDay(new Date(Date.now())) && currentTime.toMillis() < marketOpening.toMillis()) {
      return {
        isOpen: false,
        nextMarketOpen: marketOpening.toMillis()
      };
    }

    const isWithinMarketHours = InvestmentProductService.isAssetCurrentlyTraded(assetCommonId);
    const nextUsWorkDay = calculateNextWorkDay(new Date(Date.now()));
    const nextMarketOpen = DateTime.fromJSDate(nextUsWorkDay)
      .setZone(MARKET_TRADING_HOURS[formalExchange].timeZone, { keepLocalTime: true })
      .set({
        hour: MARKET_TRADING_HOURS[formalExchange].start.HOUR,
        minute: MARKET_TRADING_HOURS[formalExchange].start.MINUTES
      });

    return {
      isOpen: isWithinMarketHours,
      nextMarketOpen: nextMarketOpen.toMillis()
    };
  }

  private static _hydrateInvestmentProduct(
    investmentProductObj: InvestmentProductInterface
  ): InvestmentProductDocument {
    const hydratedDocument = InvestmentProduct.hydrate(investmentProductObj);
    // If the currentTicker is populated, we hydrate it as well.
    if (hydratedDocument?.currentTicker?.id) {
      hydratedDocument.currentTicker = IntraDayAssetTicker.hydrate(hydratedDocument.currentTicker);
    }

    return hydratedDocument;
  }
}
