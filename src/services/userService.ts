import { captureException, captureMessage, setUser } from "@sentry/node";
import {
  bannersConfig,
  countriesConfig,
  currenciesConfig,
  entitiesConfig,
  indexesConfig,
  investmentUniverseConfig,
  usersConfig,
  localeConfig,
  plansConfig,
  publicInvestmentUniverseConfig,
  rewardsConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import { PUBLIC_ASSET_CONFIG } from "@wealthyhood/shared-configs/dist/publicInvestmentUniverse";
import { ApiResponse, PaginatedUsersResponse } from "apiResponse";
import { PostIdentitiesRequestProviderEnum } from "auth0";
import Decimal from "decimal.js";
import disposableDomains from "disposable-email-domains";
import { UsersFilter } from "filters";
import { DateTime } from "luxon";
import mongoose, { PopulateOptions, QueryOptions } from "mongoose";
import { ReferredStatus } from "referral";
import { CreateParticipantData, CreateUserPartial } from "requestBody";
import { TenorEnum } from "../configs/durationConfig";
import { ProviderEnum } from "../configs/providersConfig";
import { DEFAULT_SAVINGS_PRODUCT_CONFIG } from "../configs/savingsConfig";
import events from "../event-handlers/events";
import { auth0ManagementClient } from "../external-services/auth0ManagementService";
import { GoCardlessPaymentsService } from "../external-services/goCardlessPaymentsService";
import logger from "../external-services/loggerService";
import { SaltedgeService } from "../external-services/saltedgeService";
import { MixpanelAccountStatusEnum } from "../external-services/segmentAnalyticsService";
import { StripeService } from "../external-services/stripeService";
import eventEmitter from "../loaders/eventEmitter";
import { AddressDocument } from "../models/Address";
import { BadRequestError, InternalServerError } from "../models/ApiErrors";
import { AutomationDocument } from "../models/Automation";
import { ContentEntryContentTypeEnum } from "../models/ContentEntry";
import { HoldingsSnapshotPortfolioComponentType } from "../models/DailySummarySnapshot";
import { DailyPortfolioSavingsTickerDocument } from "../models/DailyTicker";
import { GiftDocument } from "../models/Gift";
import { IndexPriceDocument } from "../models/IndexPrice";
import { KycOperationStatusType } from "../models/KycOperation";
import { EmailNotificationSettingEnum } from "../models/NotificationSettings";
import { ParticipantDocument, ParticipantRoleType } from "../models/Participant";
import { Portfolio, PortfolioDocument } from "../models/Portfolio";
import { ReferralCode, ReferralCodeDocument } from "../models/ReferralCode";
import {
  Reward,
  RewardActivityFilterEnum,
  RewardDocument,
  RewardInvestmentActivityFilterEnum
} from "../models/Reward";
import { SavingsProductDocument } from "../models/SavingsProduct";
import { SubscriptionDocument } from "../models/Subscription";
import { SundownDigestDocument } from "../models/SundownDigest";
import {
  AssetTransaction,
  CashbackTransaction,
  CashbackTransactionDocument,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  DividendTransaction,
  DividendTransactionDocument,
  SavingsDividendTransaction,
  SavingsDividendTransactionDocument,
  SavingsTopupTransaction,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransaction,
  SavingsWithdrawalTransactionDocument,
  Transaction,
  TransactionActivityFilterEnum,
  TransactionDocument,
  TransactionInvestmentActivityFilterEnum,
  WealthyhoodDividendTransaction,
  WealthyhoodDividendTransactionDocument,
  WithdrawalCashTransaction,
  WithdrawalCashTransactionDocument
} from "../models/Transaction";
import {
  AuthProviderType,
  KycStatusEnum,
  KycStatusType,
  PlatformArray,
  PlatformType,
  PortfolioConversionStatusType,
  User,
  UserDocument,
  UserPopulationFieldsEnum,
  UserTypeEnum
} from "../models/User";
import { ReferralCodeRepository } from "../repositories/referralCodeRepository";
import { UserRepository } from "../repositories/userRepository";
import { WalletRepository } from "../repositories/walletRepository";
import {
  ChartLabelOnlyType,
  DailySummariesType,
  DailySummaryPortfolioType,
  DailySummaryType,
  DailySummaryWithDataType,
  MarketSummaryType,
  PerformersType,
  ReturnsType,
  SentimentLabelEnum,
  SentimentScoreWithLabelsType,
  TodayMarketsType
} from "../types/summaries";
import { DepositMethodEnum } from "../types/transactions";
import ConfigUtil from "../utils/configUtil";
import CurrencyUtil from "../utils/currencyUtil";
import DateUtil from "../utils/dateUtil";
import DbUtil, { PopulationOptions } from "../utils/dbUtil";
import { envIsProd } from "../utils/environmentUtil";
import { formatPercentage } from "../utils/formatterUtil";
import IndexPriceUtil from "../utils/indexPriceUtil";
import * as InvestmentUniverseUtil from "../utils/investmentUniverseUtil";
import PaginationUtil from "../utils/paginationUtil";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import PortfolioUtil from "../utils/portfolioUtil";
import AccountService from "./accountService";
import AddressService from "./addressService";
import AppRatingService, { AppRatingPromptType } from "./appRatingService";
import AutomationService from "./automationService";
import DailySummarySnapshotService, { SentimentScoreType } from "./dailySummarySnapshotService";
import DailyTickerService from "./dailyTickerService";
import GiftService from "./giftService";
import IndexPriceService from "./indexPriceService";
import KycOperationService, { FormattedPassportDetails } from "./kycOperationService";
import NotificationSettingsService from "./notificationSettingsService";
import OrderService from "./orderService";
import ParticipantService from "./participantService";
import PortfolioService from "./portfolioService";
import ProviderService, { ProviderScopeEnum } from "./providerService";
import ReferralCodeService from "./referralCodeService";
import RewardInvitationService from "./rewardInvitationService";
import RewardService from "./rewardService";
import RiskAssessmentService from "./riskAssessmentService";
import SavingsProductService, { SavingsProductFeeDetailsType } from "./savingsProductService";
import SubscriptionService from "./subscriptionService";
import { PassportDetails } from "./sumsubBasedKycService";
import SundownDigestService from "./sundownDigestService";
import { TransactionService } from "./transactionService";
import WalletService from "./walletService";
import WealthyhubService, { AnalystInsightType } from "./wealthyhubService";
import { IdDocTypeEnum } from "../external-services/sumsubService";

const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;
const { getBanners, CATEGORY_CONFIG, CategoryEnum, BannerEnum } = bannersConfig;
const { ASSET_CONFIG } = investmentUniverseConfig;
const { INDEX_CONFIG_GLOBAL, IndexArrayConst } = indexesConfig;
const { MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY, MIN_DEPOSIT_AMOUNT_FOR_REWARD_ELIGIBILITY } = rewardsConfig; // in euros/pounds

export interface UpdateUserOptions {
  fieldsToDelete: { referredByEmail: boolean };
}

export type TransactionActivityItemType =
  | TransactionActivityRewardItemType
  | TransactionActivityTransactionItemType;

type TransactionActivityRewardItemType = {
  type: "reward";
  item: RewardDocument;
  activityFilter: RewardActivityFilterEnum | RewardInvestmentActivityFilterEnum;
};

type TransactionActivityTransactionItemType = {
  type: "transaction";
  item: TransactionDocument;
  activityFilter: TransactionActivityFilterEnum | TransactionInvestmentActivityFilterEnum;
};

type BannerPrerequisitesType = [
  PortfolioDocument,
  { data: RewardDocument[] },
  { data: GiftDocument[] },
  SubscriptionDocument,
  { data: AutomationDocument[] },
  AnalystInsightType,
  AnalystInsightType,
  AnalystInsightType,
  boolean,
  SavingsProductFeeDetailsType,
  boolean
];

export const VerificationStatusArray = ["verified", "verifying"] as const;
export type VerificationStatusType = (typeof VerificationStatusArray)[number];

const DATE_TO_START_SHOWING_SUMMARY_SAVINGS_DETAILS = new Date("2025-02-28");

const DEFAULT_MAX_VALUE_DIFFERENCE = 100;
const DEFAULT_MAX_VALUE_DIFFERENCES = {
  cash: DEFAULT_MAX_VALUE_DIFFERENCE,
  savings: DEFAULT_MAX_VALUE_DIFFERENCE,
  holdings: DEFAULT_MAX_VALUE_DIFFERENCE,
  total: DEFAULT_MAX_VALUE_DIFFERENCE
};

export const DAYS_AGO_TO_HAVE_SIGNED_UP_FOR_REWARD_ELIGIBILITY = 10;
export const DAYS_TO_INCLUDE_IN_DAILY_SUMMARIES = 365;
export const CHART_LABEL_ONLY_POINTS_TO_INCLUDE = 3;

const { CURRENCY_SYMBOLS } = currenciesConfig;

// A mapping between Auth0 providers -> our own namings for those providers
const AUTH_PROVIDERS_MAPPING: Record<string, AuthProviderType> = {
  email: "email",
  apple: "apple",
  "google-oauth2": "google",
  auth0: "username-password"
};

const EXEMPT_EMAIL_PREFIXES_FROM_DUPLICATE_CHECK = [
  "alexandroschristodoulakis8",
  "konstantinos019",
  "pavlos.kosmetatos",
  "g.fakorellis"
];
const EXEMPT_EMAIL_PREFIXES_FROM_PASSPORT_CHECK = ["alexandroschristodoulakis8"];
const EXEMPT_EMAIL_SUFFIX_FROM_DUPLICATE_CHECK = "@wealthyhood.com";

const FULL_USER_POPULATIONS = [
  {
    path: "addresses"
  },
  {
    path: "bankAccounts"
  },
  {
    path: "accounts"
  },
  {
    path: "participant",
    populate: [{ path: "referrer" }]
  },
  {
    path: "oneTimeReferralCode"
  },
  {
    path: "wallet"
  },
  {
    path: "subscription"
  },
  {
    path: "userDataRequests"
  },
  {
    path: "portfolios",
    populate: [
      {
        path: "currentTicker"
      },
      {
        path: "holdings.asset",
        populate: {
          path: "currentTicker"
        }
      }
    ]
  },
  {
    path: "kycOperation"
  }
];

/**
 * User prompt types
 */

export const ModalTypeArray = [
  "Gift",
  "Reward",
  "WealthyhoodDividend",
  "AppRatingPrompt",
  "RewardSettled"
] as const;
export type ModalTypeType = (typeof ModalTypeArray)[number];

export const PromptTypeArray = ["modal", "banner"] as const;
export type PromptTypeType = (typeof PromptTypeArray)[number];

type GiftModalPromptDataType = {
  gifts?: GiftDocument[];
};
type RewardModalPromptDataType = {
  rewards?: RewardDocument[];
};
type WealthyhoodDividendModalPromptDataType = {
  wealthyhoodDividends?: WealthyhoodDividendTransactionDocument[];
};
type AppRatingPromptModalPromptDataType = {
  appRatingId?: string;
};

export type ModalPromptType = {
  order: number;
  modalType: ModalTypeType;
  data:
    | GiftModalPromptDataType
    | RewardModalPromptDataType
    | WealthyhoodDividendModalPromptDataType
    | AppRatingPromptModalPromptDataType;
};

export type BannerPromptDataType = {
  rewards?: RewardDocument[];
  gifts?: GiftDocument[];
  title?: string;
  imageURL?: string;
  modalTitle?: string;
  analystInsightId?: string;
  // array of markdown strings
  modalContent?: string[];
  modalButtonText?: string;
  slug?: string;
};

export type BannerPromptType = {
  order: number;
  bannerId: bannersConfig.BannerEnum;
  data?: BannerPromptDataType;
};

export type PromptResponseType = {
  bannerPrompts?: BannerPromptType[];
  modalPrompts?: ModalPromptType[];
};

export type PromptSeenType = {
  modalType: ModalTypeType;
  ids: string[];
};

export type AccountStatementActivity = AccountStatementDataEntry[];

type AccountStatementDataEntry = {
  amount: number;
  currency: currenciesConfig.MainCurrencyType;
  date: Date;
  type:
    | "order"
    | "cashback"
    | "bonus"
    | "deposit"
    | "dividend"
    | "withdrawal"
    | "interest"
    | "interest reinvestment"
    | "reward";
  isin?: string;
  asset?: string;
  side?: "Buy" | "Sell";
};

const VALID_KYC_OPERATION_STATUSES: KycOperationStatusType[] = ["Passed", "ManuallyPassed"];

export const WK_KYC_TIMEOUT = 2; // In minutes

export default class UserService {
  /**
   * PUBLIC METHODS
   */

  /**
   * @description Returns the count of valid referrals that have been referred by the user with the
   * given email. A valid referral is a funded account, so a user with a settled deposit.
   * Note that users with pending deposits will not be counted as part of this method until their
   * deposit settles.
   * @param userEmail The user email that has made the referrals
   * @returns Count of user documents that have been referred by a user with the given email.
   */
  public static async getValidUserReferralsCount(userEmail: string): Promise<number> {
    const referrals = await User.find({ referredByEmail: userEmail });
    const referralIDs = referrals.map(({ id }) => id);
    const distinctFundedUsers = await DepositCashTransaction.find({
      owner: { $in: referralIDs },
      "providers.wealthkernel.status": "Settled"
    }).distinct("owner");

    return distinctFundedUsers.length;
  }

  public static async getUser(
    userId: string,
    populate: {
      addresses?: boolean;
      portfolios?: boolean;
      subscription?: boolean;
      participant?: boolean;
      kycOperation?: boolean;
      accounts?: boolean;
      notificationSettings?: boolean;
    } = {
      addresses: true,
      portfolios: true,
      subscription: false,
      participant: false,
      kycOperation: false,
      accounts: false,
      notificationSettings: false
    }
  ): Promise<UserDocument> {
    const populationString = DbUtil.getPopulationString(populate);

    const user = await User.findById(userId).populate(populationString);

    if (!user) {
      throw new BadRequestError("User not found!");
    }

    return user;
  }

  /**
   * @description Sets kyc status to failed if user has not passed kyc verification check
   * within the timeout period of submitting all required info.
   * @param user
   * @returns
   */
  public static async flagPotentiallyFailedToVerifyUser(user: UserDocument): Promise<void> {
    // This check is already been done in the method setFailedKycStatus, but we are more verbose
    // at this point, in order to emphasize that the method won't run if the user kyc status has
    // been marked as failed already.
    if (user.hasFailedKyc) {
      return;
    }

    if (user.submittedRequiredInfoAt < DateUtil.getDateOfMinutesAgo(WK_KYC_TIMEOUT) && !user.hasPassedKyc) {
      await UserService.setFailedKycStatus(user);
    }
  }

  public static async setPassedKycStatusIfEligible(userId: string): Promise<UserDocument> {
    const user = await User.findById(userId).populate([
      UserPopulationFieldsEnum.KYC_OPERATION,
      UserPopulationFieldsEnum.ACCOUNTS
    ]);
    if (user.hasPassedKyc) {
      return user;
    }

    if (user.isPotentiallyDuplicateAccount) {
      logger.warn(`Cannot set kycStatus to 'passed', because user ${userId} is flagged as duplicate.`, {
        module: "UserService",
        method: "setPassedKycStatusIfEligible"
      });
      return user;
    }

    if (!VALID_KYC_OPERATION_STATUSES.includes(user.kycOperation?.status)) {
      logger.warn(
        `Cannot set kycStatus to 'passed', because KYC operation of user ${user.id} is not successful.`,
        {
          module: "UserService",
          method: "setPassedKycStatusIfEligible"
        }
      );
      return user;
    }

    if (user.isPassportVerified === false) {
      logger.warn(
        `Cannot set kycStatus to 'passed', because user ${userId} is flagged for passport details mismatch.`,
        {
          module: "UserService",
          method: "setPassedKycStatusIfEligible"
        }
      );
      return user;
    } else if (user.isPassportVerified === undefined) {
      /**
       * In case the passport check hasn't run yet.
       * We want to check it now, otherwise the flag would be 'undefined'.
       */
      const { checkWasProcessed, updatedUser } = await UserService._runKycProviderPassportVerificationCheck(user);
      if (!checkWasProcessed || !updatedUser.isPassportVerified) {
        logger.warn(`Cannot set kycStatus to 'passed', because user ${userId} did not pass KYC passport check.`, {
          module: "UserService",
          method: "setPassedKycStatusIfEligible",
          data: {
            checkWasProcessed,
            isPassportVerified: updatedUser.isPassportVerified
          }
        });
        return user;
      }
    }

    if (user.accounts?.[0]?.providers?.wealthkernel?.status !== "Active") {
      logger.warn(
        `Cannot set kycStatus to 'passed' for user ${userId}, because he does not have an active WK account`,
        {
          module: "UserService",
          method: "setPassedKycStatusIfEligible"
        }
      );
      return user;
    }

    try {
      const updatedUser = await User.findOneAndUpdate(
        { _id: user._id },
        { kycStatus: KycStatusEnum.PASSED, $unset: { kycFailedAt: true } },
        { runValidators: true, new: true }
      ).populate("participant");

      eventEmitter.emit(events.user.whAccountStatusUpdate.eventId, updatedUser, {
        accountStatus: MixpanelAccountStatusEnum.Active
      });

      // on verification success the subscription of the user is activated - if it exists
      // (it may already be active at that point or not have been created)
      await SubscriptionService.activateSubscription(updatedUser.id);

      // on verification success we create the risk assessment for the user
      await RiskAssessmentService.createRiskAssessment(updatedUser);

      const portfolio = await Portfolio.findOne({ owner: user._id });
      // if the user has already created a wk portfolio, this means that wk updated the users status,
      // and we want to emit an event for this case
      if (portfolio.providers?.wealthkernel?.id) {
        eventEmitter.emit(events.user.verification.eventId, updatedUser, {
          emailNotification: false
        });
      }

      logger.info(`User ${user.email} has just passed kyc`, {
        module: "UserService",
        method: "setPassedKycStatusIfEligible"
      });

      return updatedUser;
    } catch (err) {
      setUser({ email: user.email });
      captureException(err);
      logger.error(`Failed to setting kyc passed for user ${user.id}`, {
        module: "UserService",
        method: "setPassedKycStatusIfEligible",
        data: { user: user.id, error: err }
      });
    }
  }

  public static async setFailedKycStatus(user: UserDocument): Promise<UserDocument> {
    if (user.hasFailedKyc) {
      return user;
    }

    try {
      const updatedUser = await User.findOneAndUpdate(
        { _id: user.id },
        { kycStatus: KycStatusEnum.FAILED, kycFailedAt: new Date(Date.now()) },
        { runValidators: true, new: true }
      ).populate("participant subscription addresses");

      eventEmitter.emit(events.user.whAccountStatusUpdate.eventId, updatedUser, {
        accountStatus: MixpanelAccountStatusEnum.VerificationFailed
      });

      // On KYC failure, if the user has a fee-based subscription, we need to deactivate it, as we will not be
      // able to charge cash/holdings while their WK account/portfolio are not active - the subscription will be
      // activated once the account becomes active.
      const subscription = updatedUser.subscription as SubscriptionDocument;
      if (subscription?.category === "FeeBasedSubscription") {
        await SubscriptionService.deactivateSubscription(user.id);
      }

      if (updatedUser.submittedRequiredInfo && updatedUser.hasAcceptedTerms) {
        eventEmitter.emit(events.user.verificationFailure.eventId, updatedUser);
      }

      logger.info(`User ${user.email} has just failed kyc.`, {
        module: "UserService",
        method: "setFailedKycStatus"
      });

      return updatedUser;
    } catch (err) {
      setUser({ email: user.email });
      captureException(err);
      logger.error(`Failed to setting kyc failure for user ${user.id}`, {
        module: "UserService",
        method: "setFailedKycStatus",
        data: { user: user.id, error: err }
      });
    }
  }

  public static async submitDeletionFeedback(user: UserDocument, deletionFeedback: string): Promise<void> {
    const updatedUser = await User.findByIdAndUpdate(user.id, { deletionFeedback });

    eventEmitter.emit(events.user.deletionFeedbackSubmission.eventId, updatedUser);
  }

  public static async setReferrerByCode(userId: string, referralCode: string): Promise<UserDocument> {
    const user = await User.findById(userId).populate({
      path: "participant",
      populate: [{ path: "referrer" }]
    });
    if (user.referredByEmail) {
      captureMessage(
        `User ${user.email} is already referred but was able to submit referral code ${referralCode}!`
      );
      logger.warn(`User ${user.email} is already referred!`, {
        module: "UserService",
        method: "setReferrerByCode",
        userEmail: user.email,
        data: {
          referral: user.email,
          referralCode: referralCode
        }
      });
      return user;
    }

    const referralCodeDocument = await ReferralCode.findOne({
      code: referralCode.toLowerCase().trim(),
      active: true
    }).populate({
      path: "owner",
      populate: [{ path: "participant" }]
    });
    if (!referralCodeDocument) {
      throw new BadRequestError("Referral code does not exist!");
    } else if ((referralCodeDocument.owner as UserDocument).id === userId) {
      throw new BadRequestError("User tried to refer themselves!");
    }

    logger.info(`Updating referrer of '${user.email} with referral code ${referralCodeDocument.code}!`, {
      module: "UserService",
      method: "setReferrerByCode",
      userEmail: user.email,
      data: {
        referral: user.email,
        referralCode: referralCodeDocument
      }
    });

    const referral: CreateParticipantData = {
      wlthd: referralCodeDocument.code
    };
    let updatedUser = await UserService._setParticipant(user, referral);

    // Also set the joinedWithCode field to link to the referral code document
    updatedUser = await User.findByIdAndUpdate(
      userId,
      { joinedWithCode: referralCodeDocument._id },
      { new: true }
    );

    const referrerParticipant = (referralCodeDocument.owner as UserDocument).participant as ParticipantDocument;
    eventEmitter.emit(events.user.referralCodeSubmission.eventId, updatedUser, {
      referredStatus: UserService._getReferredStatus(referrerParticipant),
      joinedWithCode: referralCodeDocument.code
    });

    if (referralCodeDocument.isWhitelistCode) {
      updatedUser = await User.findByIdAndUpdate(userId, { usedWhitelistCode: true }, { new: true });
      eventEmitter.emit(events.user.usedWhitelistCode.eventId, updatedUser);
    }

    if (updatedUser.isEuWhitelisted) {
      eventEmitter.emit(events.user.gotWhitelisted.eventId, updatedUser);
    }

    return updatedUser;
  }

  public static async deleteUser(userId: string): Promise<void> {
    await User.findByIdAndDelete(userId);
  }

  public static async getUserByEmailIncludingDeleted(
    email: string,
    options: { includeDeleted: boolean } = { includeDeleted: false },
    populate?: { participant: boolean }
  ): Promise<UserDocument> {
    ParamsValidationUtil.isEmailParamValid("email", email);

    const populationString = DbUtil.getPopulationString(populate);

    const user = await User.findOne({ email }).populate(populationString);

    if (!user && options.includeDeleted) {
      return User.findOne({ email: `deleted_${email}` });
    }

    return user;
  }

  public static async getUserByStripeId(
    stripeId: string,
    populate?: { subscription: boolean; portfolios: boolean }
  ): Promise<UserDocument> {
    const populationString = DbUtil.getPopulationString(populate);

    return User.findOne({ "providers.stripe.id": stripeId }).populate(populationString);
  }

  /**
   * Sets the portfolioConversionStatus of the given user to the given portfolioConversionStatus.
   * @param user
   * @param portfolioConversionStatus
   * @param options
   */
  public static async convertUser(
    user: UserDocument,
    portfolioConversionStatus: PortfolioConversionStatusType,
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<void> {
    // Do not do anything if portfolioConversionStatus is not going to be updated or is already completed.
    if (user.portfolioConversionStatus === portfolioConversionStatus || user.hasConvertedPortfolio) {
      return;
    }

    await User.findByIdAndUpdate(user.id, { portfolioConversionStatus }, { session: options?.session });
  }

  public static async isRewardEligible(
    userToCheck: UserDocument,
    rewardData: {
      targetUserEmail?: string;
      referrer?: string;
      referral?: string;
    }
  ): Promise<boolean> {
    if (!userToCheck.populated("userDataRequests")) {
      await userToCheck.populate("userDataRequests");
    }

    if (userToCheck.hasDisassociationRequest) {
      logger.info(
        `We tried to give a reward to ${userToCheck.id} but we couldn't because the user has a disassociation request.`,
        {
          module: "UserService",
          method: "isRewardEligible",
          userEmail: userToCheck.email,
          data: rewardData
        }
      );
      return false;
    }

    if (!userToCheck.hasPassedKyc) {
      logger.info(
        `We tried to give a reward to ${userToCheck.id} but we couldn't because the user has not passed KYC.`,
        {
          module: "UserService",
          method: "isRewardEligible",
          userEmail: userToCheck.email,
          data: rewardData
        }
      );
      return false;
    }

    // Whitelist check: if user is has whitelisted code or email, skip all other checks
    if (userToCheck.usedEuWhitelistCodeOrEmail) {
      return true;
    }

    // Greek referral check: if user is Greek and has been referred, skip all other checks
    if (userToCheck.referredByEmail && userToCheck.residencyCountry === "GR") {
      return true;
    }

    // Determine which checks to apply based on companyEntity
    let requireInvestmentCheck = false;
    let requireDepositCheck = false;
    if (userToCheck.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      requireInvestmentCheck = true;
    } else if (userToCheck.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE) {
      requireDepositCheck = true;
    }

    // If we're checking whether the referred user is eligible, ensure they have the required activity
    if (userToCheck.id === rewardData?.referral) {
      if (requireInvestmentCheck) {
        const hasMinimumInvestmentsToUnlockReward = await UserService._userHasTotalInvestmentAmount(
          userToCheck,
          MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY
        );

        if (!hasMinimumInvestmentsToUnlockReward) {
          logger.info(
            `We tried to give a reward to ${userToCheck.id} but they do not have investments over ${CurrencyUtil.formatCurrency(
              MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY,
              userToCheck.currency,
              ConfigUtil.getDefaultUserLocale(userToCheck.residencyCountry)
            )}`,
            {
              module: "UserService",
              method: "isRewardEligible",
              userEmail: userToCheck.email,
              data: rewardData
            }
          );
          return false;
        }
      }

      if (requireDepositCheck) {
        const hasMinimumDepositsToUnlockReward = await UserService._userHasTotalDepositAmount(
          userToCheck,
          MIN_DEPOSIT_AMOUNT_FOR_REWARD_ELIGIBILITY
        );

        if (!hasMinimumDepositsToUnlockReward) {
          logger.info(
            `We tried to give a reward to ${userToCheck.id} but they do not have deposits over ${CurrencyUtil.formatCurrency(
              MIN_DEPOSIT_AMOUNT_FOR_REWARD_ELIGIBILITY,
              userToCheck.currency,
              ConfigUtil.getDefaultUserLocale(userToCheck.residencyCountry)
            )}`,
            {
              module: "UserService",
              method: "isRewardEligible",
              userEmail: userToCheck.email,
              data: rewardData
            }
          );
          return false;
        }
      }
    }

    return true;
  }

  public static async verifyUserStepByStep(user: UserDocument): Promise<VerificationStatusType> {
    logger.info(`Starting verification process for user ${user._id}`, {
      module: "UserService",
      method: "verifyUserStepByStep",
      userEmail: user.email
    });

    const [userAfterDuplicateChecks] = await Promise.all([
      UserService._runPotentiallyDuplicateUserChecks(user),
      UserService._createStripeEntrySafely(user),
      UserService._createSaltedgeEntrySafely(user),
      UserService.createGCEntry(user),
      WalletService.createDevengoEntry(user)
    ]);

    logger.info(`Finished creating third party entities for ${user._id}`, {
      module: "UserService",
      method: "verifyUserStepByStep",
      userEmail: user.email
    });

    // Party step
    const { userAfterPartyUpdates, createdNewBrokerageParty } =
      await UserService._createOrSyncBrokerageParty(userAfterDuplicateChecks);
    if (createdNewBrokerageParty) {
      logger.info(`Just created new brokerage party for user ${user._id}, responding with verifying...`, {
        module: "UserService",
        method: "verifyUserStepByStep",
        userEmail: user.email
      });
      return "verifying";
    }

    // Address step
    await DbUtil.populateIfNotAlreadyPopulated(userAfterPartyUpdates, UserPopulationFieldsEnum.ADDRESSES);
    const createdNewBrokerageAddress = await AddressService.createOrSyncBrokerageAddress(
      userAfterPartyUpdates,
      userAfterPartyUpdates.addresses[0]
    );
    if (createdNewBrokerageAddress) {
      logger.info(`Just created new brokerage address for user ${user._id}, responding with verifying...`, {
        module: "UserService",
        method: "verifyUserStepByStep",
        userEmail: user.email
      });
      return "verifying";
    }

    // W-8BEN form step
    const { w8BenFormCompleted } = await UserService._createOrSyncBrokerageW8BenForm(userAfterPartyUpdates);
    if (!w8BenFormCompleted) {
      logger.info(`Finished W-8BEN creation step for user ${user._id}, responding with verifying...`, {
        module: "UserService",
        method: "verifyUserStepByStep",
        userEmail: user.email
      });
      return "verifying";
    }

    // Account step
    await DbUtil.populateIfNotAlreadyPopulated(userAfterPartyUpdates, UserPopulationFieldsEnum.ACCOUNTS);
    // eslint-disable-next-line prefer-const
    let { userAfterAccountUpdates, newStatus } = await AccountService.createOrSyncBrokerageAccount(
      userAfterPartyUpdates,
      userAfterPartyUpdates.accounts[0]
    );
    if (newStatus !== "Active") {
      logger.info(`Finished account step for user ${user._id}, responding with verifying...`, {
        module: "UserService",
        method: "verifyUserStepByStep",
        userEmail: user.email
      });
      return "verifying";
    }

    // KYC Provider verification step - Validates that user passed
    const kycOperation = await KycOperationService.syncSumsubKycOperation(userAfterAccountUpdates);
    if (kycOperation.status !== "Passed") {
      logger.warn(`User ${user._id} did not pass KYC provider verification, responding with verifying...`, {
        module: "UserService",
        method: "verifyUserStepByStep",
        userEmail: user.email
      });
      return "verifying";
    }

    // Passport Details step - Validates that passport details match those extracted by our KYC provider
    const { checkWasProcessed, updatedUser } =
      await UserService._runKycProviderPassportVerificationCheck(userAfterAccountUpdates);
    if (!checkWasProcessed || !updatedUser.isPassportVerified) {
      logger.warn(
        `Cannot verify user ${user._id} because he didn't pass the KYC passport check, responding with verifying...`,
        {
          module: "UserService",
          method: "verifyUserStepByStep",
          userEmail: user.email,
          data: {
            checkWasProcessed,
            isPassportVerified: updatedUser.isPassportVerified
          }
        }
      );
      return "verifying";
    }
    userAfterAccountUpdates = updatedUser;

    if (userAfterAccountUpdates.isPotentiallyDuplicateAccount) {
      logger.info(`Completed verification process for ${user._id} but flagged as potentially duplicate!`, {
        module: "UserService",
        method: "verifyUserStepByStep",
        userEmail: user.email
      });
      return "verifying";
    }

    // Portfolios step
    await DbUtil.populateIfNotAlreadyPopulated(userAfterAccountUpdates, UserPopulationFieldsEnum.PORTFOLIOS);
    const generalInvestmentPortfolio =
      await PortfolioService.getGeneralInvestmentPortfolio(userAfterAccountUpdates);
    if (!PortfolioService.hasWealthkernelId(generalInvestmentPortfolio)) {
      await PortfolioService.createBrokeragePortfolio(generalInvestmentPortfolio);
    }

    // All steps have been completed, user must be verified
    logger.info(`Completed verification process for ${user._id}`, {
      module: "UserService",
      method: "verifyUserStepByStep",
      userEmail: user.email
    });

    return "verified";
  }

  public static async getUsers(
    filter: UsersFilter = {},
    pageConfig?: { page: number; pageSize: number },
    populate?: {
      portfolios?: boolean;
      kycOperation?: boolean;
      accounts?: boolean;
    },
    sort?: string
  ) {
    return pageConfig
      ? UserService._getUsersPaginated(filter, pageConfig, populate, sort)
      : UserService._getUsers(filter, populate, sort);
  }

  /**
   * @description Returns a Cursor to iterate through all users.
   */
  public static getUsersStreamed(filter: UsersFilter = {}, populate?: PopulationOptions) {
    const actualFilter = UserService._createDbFilter(filter);

    return User.find(actualFilter)
      .populate(DbUtil.getMongoosePopulateOption(populate))
      .cursor()
      .addCursorFlag("noCursorTimeout", envIsProd());
  }

  public static async getUserDetailed(userId: string): Promise<UserDocument> {
    return User.findOne({ _id: new mongoose.Types.ObjectId(userId) }).populate(
      "addresses bankAccounts portfolios accounts kycOperation latestRiskAssessment"
    );
  }

  /**
   * @description Returns the count of users that satisfy the given filter.
   */
  public static async countUsers(filter: UsersFilter = {}): Promise<number> {
    const actualFilter = UserService._createDbFilter(filter);

    return User.countDocuments(actualFilter);
  }

  public static async getConvertedUserIds(): Promise<string[]> {
    return (await User.find({ portfolioConversionStatus: "completed" }, { _id: 1 })).map((user) => user.id);
  }

  public static async getOwnersOfSettledTransactionsAndRewards(): Promise<string[]> {
    return (
      ((
        await Transaction.aggregate([
          // Step 1: Retrieve settled asset transactions
          {
            $match: {
              category: "AssetTransaction",
              status: "Settled"
            }
          },
          // Step 2: Union with settled rewards
          {
            $unionWith: {
              coll: "rewards",
              pipeline: [
                {
                  $match: {
                    "order.providers.wealthkernel.status": "Matched"
                  }
                }
              ]
            }
          },
          // Step 3: Retrieve owners of settled transactions and rewards and add
          // them to two lists.
          {
            $group: {
              _id: null,
              assetTransactionOwners: {
                $addToSet: "$owner"
              },
              rewardOwners: {
                $addToSet: "$targetUser"
              }
            }
          },
          // Step 4: Merge two lists and get unique values.
          {
            $project: {
              owners: {
                $setUnion: ["$assetTransactionOwners", "$rewardOwners"]
              }
            }
          }
        ])
      )?.[0]?.owners as mongoose.Types.ObjectId[]) || []
    ).map((id) => id.toString());
  }

  public static async updateUser(
    userId: string,
    userData: Partial<UserDocument>,
    options: UpdateUserOptions = null
  ): Promise<UserDocument> {
    const currentUser = await User.findById(userId);

    let updateData: mongoose.UpdateQuery<UserDocument> = userData;
    if (options && options.fieldsToDelete.referredByEmail) {
      const { referredByEmail, ...userDataWithoutReferrerEmail } = userData;
      updateData = {
        ...userDataWithoutReferrerEmail,
        $unset: { referredByEmail: true }
      };
    }

    let updatedUser = await User.findByIdAndUpdate(new mongoose.Types.ObjectId(userId), updateData, {
      new: true,
      runValidators: true,
      upsert: false
    });

    // If user just submitted their residency country, update active providers
    // for accounts/portfolios as well as assigned gifts.
    if (updatedUser.residencyCountry !== currentUser.residencyCountry) {
      eventEmitter.emit(events.user.residencyCountryChange.eventId, updatedUser);

      await WalletRepository.createWallet(updatedUser);

      const gift = (
        (await GiftService.getGifts({ targetUserEmail: updatedUser.email })) as { data: GiftDocument[] }
      ).data[0];
      if (gift) {
        await GiftService.updateGift(gift.id, {
          consideration: {
            amount: gift.consideration.amount,
            currency: updatedUser.currency
          },
          deposit: {
            activeProviders: ProviderService.getProviders(updatedUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
          }
        });
      }
      await updatedUser.populate("generalInvestmentAccount portfolios");
      const generalInvestmentAccount = updatedUser.generalInvestmentAccount;
      if (generalInvestmentAccount) {
        await AccountService.updateActiveProviders(
          generalInvestmentAccount,
          ProviderService.getProviders(updatedUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
        );
      } else {
        logger.error(`Could not add active providers to account for ${userId}`, {
          module: "UserService",
          method: "updateUser",
          data: {
            userId
          }
        });
      }

      const generalInvestmentPortfolio = await PortfolioService.getGeneralInvestmentPortfolio(updatedUser, false);
      if (generalInvestmentPortfolio) {
        await Promise.all([
          PortfolioService.updateActiveProviders(
            generalInvestmentPortfolio,
            ProviderService.getProviders(updatedUser.companyEntity, [ProviderScopeEnum.BROKERAGE])
          ),
          PortfolioService.updateCurrencyFromOwner(generalInvestmentPortfolio, updatedUser.currency)
        ]);
      } else {
        logger.error(`Could not add active providers to real portfolio for ${userId}`, {
          module: "UserService",
          method: "updateUser",
          data: {
            userId
          }
        });
      }
    }

    if (updatedUser.passportSubmitted && !currentUser.passportSubmitted) {
      eventEmitter.emit(events.user.passportDetailsSubmission.eventId, updatedUser);
    }

    if (!currentUser.taxResidencySubmitted && updatedUser.taxResidencySubmitted) {
      eventEmitter.emit(events.user.taxDetailsSubmission.eventId, updatedUser);
    }

    if (!currentUser.employmentInfoSubmitted && updatedUser.employmentInfoSubmitted) {
      eventEmitter.emit(events.user.employmentInfoSubmission.eventId, updatedUser);
      eventEmitter.emit(events.user.personalDetailsSubmission.eventId, updatedUser);
    }

    if (!currentUser.hasAcceptedTerms && updatedUser.hasAcceptedTerms) {
      // If user has just accepted terms this means that they have submitted all the required info
      // and we want to store that point of time
      if (!updatedUser.submittedRequiredInfoAt) {
        const now = new Date();

        updatedUser = await User.findByIdAndUpdate(
          new mongoose.Types.ObjectId(userId),
          { submittedRequiredInfoAt: now, "w8BenForm.completedAt": now },
          {
            new: true,
            runValidators: true,
            upsert: false
          }
        );
      }
      eventEmitter.emit(events.user.termsAccepted.eventId, updatedUser);

      // if user has just accepted terms this means that they have submitted all the required info
      // and we want to emit a verification failed event in case the user has failed kyc
      if (updatedUser.hasFailedKyc) {
        eventEmitter.emit(events.user.verificationFailure.eventId, updatedUser);
      }
    }

    if (!currentUser.joinedWaitingListAt && updatedUser.joinedWaitingListAt) {
      eventEmitter.emit(events.user.joinedWaitingList.eventId, updatedUser);
    }

    return updatedUser;
  }

  public static async submitResidencyCountry(
    userId: string,
    residencyCountry: countriesConfig.CountryCodesType
  ): Promise<UserDocument> {
    const companyEntity = ConfigUtil.getDefaultCompanyEntity(residencyCountry);

    const updatedUser = await UserService.updateUser(userId, {
      residencyCountry,
      currency: ConfigUtil.getDefaultUserCurrency(residencyCountry),
      companyEntity,
      activeProviders: ProviderService.getProviders(companyEntity, [
        ProviderScopeEnum.BROKERAGE,
        ProviderScopeEnum.DIRECT_DEBIT_MANDATES,
        ProviderScopeEnum.CARD_SUBSCRIPTION_PAYMENTS,
        ProviderScopeEnum.KYC
      ]),
      w8BenForm: {
        activeProviders: ProviderService.getProviders(companyEntity, [ProviderScopeEnum.W_8BEN])
      }
    });

    if (updatedUser.isEuWhitelisted) {
      eventEmitter.emit(events.user.gotWhitelisted.eventId, updatedUser);
    }

    return updatedUser;
  }

  public static async removeGiftSendingAbility(userId: string): Promise<UserDocument> {
    return User.findByIdAndUpdate(
      new mongoose.Types.ObjectId(userId),
      { $unset: { canSendGiftUntil: true } },
      {
        new: true,
        runValidators: true,
        upsert: false
      }
    );
  }

  /**
   * This method is responsible for creating, updating and retrieving users for the following three scenarios:
   * 1. A user has just logged in for the first time: we have to create a new user document with the information
   * passed to us by Auth0, and perhaps create a participant for them.
   * 2. A user has just logged in, but they are an existing user: we have to update their last login and role information
   * from Auth0. Also, if the user has logged in using a new auth provider, we want to link that to their original
   * Auth0 user entry.
   * 3. A client application has hit this endpoint to retrieve the user object: we just want to return a populated user
   * @param email
   * @param userData
   * @param referral
   */
  public static async createOrUpdateUser(
    email: string,
    userData: CreateUserPartial,
    referral?: CreateParticipantData
  ): Promise<UserDocument & { isNew: boolean }> {
    const existingUser: UserDocument = await User.findOne({ email });

    let user: UserDocument;
    let isNew = false;
    if (!existingUser) {
      isNew = true;
      user = await UserService._handleUserSignup(userData, referral);
    } else if (existingUser.lastLogin.getTime() !== userData.lastLogin.getTime()) {
      user = await UserService._handleExistingUserLogin(existingUser, userData, referral);
    } else {
      user = await existingUser.populate(FULL_USER_POPULATIONS);
    }

    return {
      ...user.toObject(),
      canUnlockFreeShare: await UserService.canUnlockFreeShare(user),
      canReceiveCashback: await UserService.canReceiveCashback(user),
      portfolioConversionStatus: await UserService.getPortfolioConversionStatus(user),
      isNew
    } as UserDocument & { isNew: boolean };
  }

  public static isUserAdult(user: UserDocument): boolean {
    return DateUtil.dateDiffInYears(user.dateOfBirth, new Date()) >= 18;
  }

  public static isUserOverAgeX(user: UserDocument, age: number): boolean {
    if (!age) {
      throw new InternalServerError("Age cannot be empty.");
    }

    return DateUtil.dateDiffInYears(user.dateOfBirth, new Date()) >= age;
  }

  public static async getUserPlan(userId: string): Promise<plansConfig.PlanType> {
    const owner = await UserService.getUser(userId, {
      addresses: false,
      portfolios: false,
      subscription: true
    });

    return (owner.subscription as SubscriptionDocument).plan;
  }

  /**
   * Returns true if the user has only 1 investment/reward. This method can be used right after we create the user's
   * transaction/submit their reward to determine whether what we just created was the first one.
   * @param userId
   */
  public static async userHasSingleInvestment(userId: string): Promise<boolean> {
    const numberOfInvestments = await AssetTransaction.countDocuments({
      owner: userId,
      status: { $in: ["Pending", "Settled"] }
    });
    // we also count pending investments from automation
    const numberOfAutomatedInvestments = await AssetTransaction.countDocuments({
      owner: userId,
      status: "PendingDeposit",
      linkedAutomation: { $exists: true }
    });
    const numberOfSubmittedRewards = await Reward.countDocuments({
      targetUser: userId,
      "order.providers.wealthkernel.id": { $exists: true }
    });

    return numberOfInvestments + numberOfAutomatedInvestments + numberOfSubmittedRewards === 1;
  }

  /**
   * Returns true if the user has NO **settled** investments/rewards. This method can be used right before we
   * settle the user's transaction/reward to determine whether what we are about to settle is going to be the first one.
   * @param user
   */
  public static async userHasNoSettledInvestments(user: UserDocument): Promise<boolean> {
    const [numberOfInvestments, numberOfSettledRewards] = await Promise.all([
      AssetTransaction.countDocuments({
        owner: user._id,
        status: "Settled"
      }),
      Reward.countDocuments({
        targetUser: user._id,
        status: "Settled"
      })
    ]);

    return numberOfInvestments + numberOfSettledRewards === 0;
  }

  /**
   * @description Creates Wealthkernel parties for all eligible users that have submitted the required info
   * but are missing a party entity.
   */
  public static async createAllWkParties(): Promise<void> {
    // Fetch all users that are missing wealthkernel party
    const usersMissingParty = await User.find({
      activeProviders: ProviderEnum.WEALTHKERNEL,
      submittedRequiredInfoAt: { $lt: DateUtil.getDateOfMinutesAgo(10) },
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.id": { $eq: undefined } }
      ]
    }).populate("addresses bankAccounts");

    const pendingPartyPromises = usersMissingParty
      .filter((user) => user.submittedRequiredInfo)
      .map(UserService._createOrSyncBrokerageParty);

    await Promise.allSettled(pendingPartyPromises);
  }

  /**
   * @description Creates Stripe customers for all eligible users that are missing a Stripe entity.
   */
  public static async createAllStripeCustomers(): Promise<void> {
    const usersMissingStripeEntity = await User.find({
      activeProviders: ProviderEnum.STRIPE,
      $or: [{ "providers.stripe.id": { $exists: false } }, { "providers.stripe.id": { $eq: undefined } }],
      submittedRequiredInfoAt: { $lt: DateUtil.getDateOfMinutesAgo(10) }
    });

    const userPromises = usersMissingStripeEntity.map(async (user) => UserService._createStripeEntrySafely(user));
    await Promise.all(userPromises);
  }

  /**
   * @description Creates Saltedge leads for all eligible users that are missing a Saltedge entity.
   */
  public static async createAllSaltedgeLeads(): Promise<void> {
    const usersMissingSaltedgeEntity = await User.find({
      activeProviders: ProviderEnum.SALTEDGE,
      $or: [{ "providers.saltedge.id": { $exists: false } }, { "providers.saltedge.id": { $eq: undefined } }],
      submittedRequiredInfoAt: { $lt: DateUtil.getDateOfMinutesAgo(10) }
    });

    const userPromises = usersMissingSaltedgeEntity.map(async (user) =>
      UserService._createSaltedgeEntrySafely(user)
    );
    await Promise.all(userPromises);
  }

  /**
   * @description Creates W-8BEN forms for all eligible users that are missing one.
   */
  public static async createAllWkW8BenForms(): Promise<void> {
    const usersMissingW8BenForm = await User.find({
      email: { $not: /^deleted_/ }, // We do this in the query instead of populating user data requests for performance reasons
      "w8BenForm.activeProviders": ProviderEnum.WEALTHKERNEL,
      $or: [
        { "w8BenForm.providers.wealthkernel.id": { $exists: false } },
        { "w8BenForm.providers.wealthkernel.id": { $eq: undefined } }
      ],
      submittedRequiredInfoAt: { $lt: DateUtil.getDateOfMinutesAgo(10) },
      kycStatus: KycStatusEnum.PASSED
    });

    for (let i = 0; i < usersMissingW8BenForm.length; i++) {
      const user = usersMissingW8BenForm[i];
      await UserService._createWkW8BenFormEntrySafely(user);
    }
  }

  /**
   * @description Completes W-8BEN forms for all eligible users that are missing one.
   */
  public static async completeAllWkW8BenForms(): Promise<void> {
    const usersWithIncompleteW8BenForm = await User.find({
      $and: [
        { "w8BenForm.providers.wealthkernel.id": { $exists: true } },
        { "w8BenForm.providers.wealthkernel.status": "Pending" }
      ],
      submittedRequiredInfoAt: { $lt: DateUtil.getDateOfMinutesAgo(10) }
    });

    for (let i = 0; i < usersWithIncompleteW8BenForm.length; i++) {
      const user = usersWithIncompleteW8BenForm[i];
      await UserService._completeWkW8BenFormEntrySafely(user);
    }
  }

  /**
   * Enables the user gifting capability for the given period.
   * @param user
   * @param periodInDays
   */
  public static async enableGiftingCapability(user: UserDocument, periodInDays = 7): Promise<void> {
    await User.findByIdAndUpdate(user.id, {
      canSendGiftUntil: new Date(+Date.now() + periodInDays * 24 * 60 * 60 * 1000)
    });
  }

  /**
   * Determines if a user can unlock a free share reward based on their company entity.
   *
   * Returns true if user is referred and meets entity-specific criteria:
   * - For WEALTHYHOOD_EUROPE: Not whitelisted, has no existing rewards, and insufficient deposits (< €100)
   * - For WEALTHYHOOD_UK: Signed up within 10 days, has no existing rewards, and insufficient investments (< £100)
   *
   * @param user - The user document to check
   * @returns Promise<boolean> - True if user can unlock free share, false otherwise
   */
  public static async canUnlockFreeShare(user: UserDocument): Promise<boolean> {
    const isReferred = !!user.referredByEmail;

    // Free share unlocking is used only for referral rewards
    if (!isReferred) {
      return false;
    }

    if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE) {
      return UserService._canUnlockFreeShareEurope(user);
    } else if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      return UserService._canUnlockFreeShareUK(user);
    }

    return false;
  }

  /**
   * Returns the richer portfolio conversion status of the user, based on the persisted field AND the user's
   * transactions.
   * @param user
   */
  public static async getPortfolioConversionStatus(user: UserDocument): Promise<PortfolioConversionStatusType> {
    if (user.portfolioConversionStatus === "completed") {
      if (!user.populated("portfolios")) {
        await user.populate("portfolios");
      }
      const portfolio = user.portfolios[0] as PortfolioDocument;

      if (portfolio.holdings?.length > 0) return "completed";
      else return "fullyWithdrawn";
    } else if (user.portfolioConversionStatus === "inProgress") {
      return "inProgress";
    }

    // If user conversion status that is persisted in our database is notStarted, we need to retrieve their
    // transactions to see whether we should return notStarted or inProgress.
    const hasPendingAssetTransactions = await TransactionService.hasPendingAssetTransactions(user.id);

    if (hasPendingAssetTransactions) {
      return "inProgress";
    } else return "notStarted";
  }

  /**
   * Returns true based on the following conditions:
   * 1. User is on a plan that has enabled cashbacks.
   * 2. Above subscription plan is active.
   * 3. User is in the UK company entity.
   * @param user
   */
  public static async canReceiveCashback(user: UserDocument): Promise<boolean> {
    if (!user.isCashbackFeatureEnabled) {
      return false;
    }

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.SUBSCRIPTION);
    const subscription = user.subscription as SubscriptionDocument;

    if (!subscription || !subscription.active) {
      return false;
    }

    return ["paid_low", "paid_mid"].includes(subscription.plan);
  }

  public static async getPrompts(user: UserDocument, type?: PromptTypeType): Promise<PromptResponseType> {
    try {
      const prompts: PromptResponseType = {};

      if (type === "modal") {
        prompts.modalPrompts = await UserService._getModalPrompts(user);
      } else if (type === "banner") {
        prompts.bannerPrompts = await UserService._getBannerPrompts(user);
      } else {
        const [modalPrompts, bannerPrompts] = await Promise.all([
          UserService._getModalPrompts(user),
          UserService._getBannerPrompts(user)
        ]);

        prompts.modalPrompts = modalPrompts;
        prompts.bannerPrompts = bannerPrompts;
      }

      return prompts;
    } catch (err) {
      // Return default banners, in case of an error
      // and report the issue to Sentry
      if (type === "banner") {
        captureException(err);
        return {
          bannerPrompts: UserService._getFallbackBannerPrompts(user)
        };
      }
      throw err;
    }
  }

  public static async getTransactionActivity(
    user: UserDocument,
    limit?: number
  ): Promise<TransactionActivityItemType[]> {
    const [transactions, rewards] = await Promise.all([
      TransactionService.getUserActivityTransactions(user, limit),
      RewardService.getUserActivityRewards(user.id, limit)
    ]);
    const transactionsWithTypeField: TransactionActivityTransactionItemType[] = transactions.map((transaction) => {
      /**
       * Ovewrite status to 'Pending' for savings transactions that have Cancelled,Rejected status as this not expected
       */
      if (
        ["SavingsTopupTransaction", "SavingsWithdrawalTransaction"].includes(transaction.category) &&
        ["Cancelled", "Rejected"].includes(transaction.status)
      ) {
        logger.warn(`Overwriting status to 'Pending' for ${transaction.id} transaction`, {
          module: "UserService",
          method: "getTransactionActivity"
        });
        transaction.status = "Pending";
      }

      return {
        type: "transaction",
        item: transaction,
        activityFilter: TransactionActivityFilterEnum[transaction.activityFilter]
      };
    });

    const rewardsWithTypeField: TransactionActivityRewardItemType[] = rewards.map((reward) => ({
      type: "reward",
      item: reward,
      activityFilter: RewardActivityFilterEnum[reward.activityFilter]
    }));
    const userActivityItems = [...transactionsWithTypeField, ...rewardsWithTypeField];

    let sortedDocuments = userActivityItems.sort((a, b) => {
      if (a.item.displayDate < b.item.displayDate) {
        return 1;
      } else if (a.item.displayDate > b.item.displayDate) {
        return -1;
      }
    });

    if (limit) {
      sortedDocuments = sortedDocuments.slice(0, limit);
    }
    return sortedDocuments;
  }

  /**
   * @description Returns the investment activity of the user, with the items being sorted by display date.
   * @param user
   */
  public static async getInvestmentActivity(
    user: UserDocument
  ): Promise<(TransactionDocument | RewardDocument)[]> {
    const [transactions, rewards] = await Promise.all([
      TransactionService.getUserInvestmentActivityTransactions(user),
      RewardService.getUserActivityRewards(user.id)
    ]);
    return [...transactions, ...rewards].sort((a, b) => {
      if (a.displayDate < b.displayDate) {
        return 1;
      } else if (a.displayDate > b.displayDate) {
        return -1;
      }
      return 0;
    });
  }

  public static async markPromptsAsSeen(promptSeenData: PromptSeenType, owner?: string): Promise<void> {
    const { ids, modalType } = promptSeenData;

    if (modalType === "Gift") {
      await Promise.all(
        ids.map((id) =>
          GiftService.updateGift(id, {
            hasViewedAppModal: true
          })
        )
      );
    } else if (modalType === "Reward") {
      // TODO revert this temporary fix: It it for a iOS bug that does not include the id of the reward in the array
      if (ids.length === 0) {
        const notViewedRewards = (await RewardService.getRewards({
          targetUser: owner,
          hasViewedAppModal: false
        })) as { data: RewardDocument[] };

        if (notViewedRewards.data.length > 0) {
          await Promise.all(
            notViewedRewards.data.map((reward) =>
              RewardService.updateReward(reward.id, {
                hasViewedAppModal: true
              })
            )
          );
        }
        return;
      }
      //  END of fix
      await Promise.all(
        ids.map((id) =>
          RewardService.updateReward(id, {
            hasViewedAppModal: true
          })
        )
      );
    } else if (modalType === "RewardSettled") {
      await Promise.all(
        ids.map((id) =>
          RewardService.updateReward(id, {
            hasViewedAppModal: true
          })
        )
      );
    } else if (modalType === "WealthyhoodDividend") {
      await Promise.all(
        ids.map((id) =>
          TransactionService.updateWealthyhoodDividendTransaction(id, {
            hasViewedAppModal: true
          })
        )
      );
    }
  }

  /**
   * @description Return the volume (in whole currency) of a user's buy transactions after a specific date.
   * If no date is provided it returns the volume of all the user's transactions.
   */
  public static async getVolumeOfTransactionsForUser(user: UserDocument, settledAfter?: Date): Promise<Decimal> {
    const query: any = {
      owner: user.id,
      status: "Settled"
    };

    if (settledAfter) {
      query.settledAt = { $gte: settledAfter };
    }

    const assetTransactions = await AssetTransaction.find(query).populate("orders");

    return TransactionService.getVolumeOfTransactions(assetTransactions, { includeSells: true });
  }

  public static async updateKycProviderDataIfMissing(
    userId: string,
    applicantId: string,
    passportDetails: FormattedPassportDetails
  ): Promise<void> {
    const updatedUser = await User.findOneAndUpdate(
      {
        _id: userId,
        firstName: { $exists: false },
        lastName: { $exists: false },
        dateOfBirth: { $exists: false },
        $or: [{ nationalities: { $size: 0 } }, { nationalities: { $exists: false } }]
      },
      {
        firstName: passportDetails.firstName,
        lastName: passportDetails.lastName,
        dateOfBirth: passportDetails.dateOfBirth,
        nationalities: passportDetails.nationality ? [passportDetails.nationality] : [],
        "providers.sumsub.id": applicantId,
        "idDocument.countryCode": passportDetails.documentCountryCode,
        "idDocument.documentType": passportDetails.documentType,
        "idDocument.value": passportDetails.documentNumber
      },
      {
        runValidators: true,
        new: true
      }
    );

    // If all passport details are submitted emit event
    if (updatedUser?.passportSubmitted) {
      eventEmitter.emit(events.user.passportDetailsSubmission.eventId, updatedUser);
    }
  }

  public static async viewedWealthybitesScreen(user: UserDocument, shouldSubscribe: boolean) {
    await Promise.all([
      UserService.updateUser(user.id, { viewedWealthybitesScreen: true }),
      NotificationSettingsService.updateNotificationSetting(
        user,
        EmailNotificationSettingEnum.WEALTHYBITES,
        shouldSubscribe
      )
    ]);
  }

  public static async updateDeviceToken(userId: string, platform: PlatformType, token: string): Promise<void> {
    if (!PlatformArray.includes(platform as any)) {
      throw new InternalServerError(`Platform must be one of: ${PlatformArray.join(", ")}`);
    }

    if (!token) {
      throw new InternalServerError("Device token must be a non-empty string");
    }

    await UserRepository.updateDeviceToken(userId, platform, token);
  }

  /**
   * Removes the device token for a specific platform when a user unsubscribes from notifications
   * @param userId The ID of the user
   * @param platform The platform (ios/android) to remove the token for
   */
  public static async removeDeviceToken(userId: string, platform: PlatformType): Promise<void> {
    if (!PlatformArray.includes(platform as any)) {
      throw new InternalServerError(`Platform must be one of: ${PlatformArray.join(", ")}`);
    }

    await UserRepository.removeDeviceToken(userId, platform);
  }

  public static async createGCEntry(
    user: UserDocument
  ): Promise<{ userAfterGoCardlessUpdates: UserDocument; createdNewCustomerInGoCardless: boolean }> {
    try {
      if (user.providers?.gocardless?.id) {
        // This user already has a GoCardless customer created and our database is synced!
        return { userAfterGoCardlessUpdates: user, createdNewCustomerInGoCardless: false };
      } else if (!user.activeProviders.includes(ProviderEnum.GOCARDLESS)) {
        return { userAfterGoCardlessUpdates: user, createdNewCustomerInGoCardless: false };
      }

      await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.ADDRESSES);
      const address = user.addresses[0] as AddressDocument;

      const customerData = {
        email: user.email,
        given_name: user.firstName,
        family_name: user.lastName,
        address_line1: address.line1,
        address_line2: address.line2,
        city: address.city,
        postal_code: address.postalCode,
        country_code: address.countryCode,
        metadata: {
          wealthyhoodId: user.id
        }
      };

      const response = await GoCardlessPaymentsService.Instance.createCustomer(customerData);

      const updatedUser = await User.findByIdAndUpdate(user.id, {
        "providers.gocardless": { id: response.id }
      });

      return { userAfterGoCardlessUpdates: updatedUser, createdNewCustomerInGoCardless: true };
    } catch (err) {
      setUser({ email: user.email });
      captureException(err);
      logger.error(`Creating a GoCardless entry failed for user ${user.id}`, {
        module: "UserService",
        method: "createGCEntry",
        data: { user: user.id, error: JSON.stringify(err) }
      });
    }
  }

  public static async getAccountStatementActivity(
    owner: string,
    start?: Date,
    end?: Date
  ): Promise<AccountStatementActivity> {
    const settlementDateQueryFilter = (start || end) && {
      settledAt: {
        ...(start && { $gte: start }),
        ...(end && { $lt: end })
      }
    };
    const creationDateQueryFilter = (start || end) && {
      createdAt: {
        ...(start && { $gte: start }),
        ...(end && { $lt: end })
      }
    };

    const [
      orders,
      cashbacks,
      wealthyhoodDividends,
      dividends,
      deposits,
      withdrawals,
      savingsTopUps,
      savingsWithdrawals,
      savingsDividends,
      rewards
    ] = await Promise.all([
      OrderService.getMatchedOrdersForTransactions(owner, ["AssetTransaction", "RebalanceTransaction"]),
      CashbackTransaction.find({
        owner: owner,
        status: "Settled",
        ...settlementDateQueryFilter
      }),
      WealthyhoodDividendTransaction.find({
        owner: owner,
        "deposit.providers.wealthkernel.status": "Settled",
        ...settlementDateQueryFilter
      }),
      DividendTransaction.find({
        owner: owner,
        "providers.wealthkernel.status": "Settled",
        ...creationDateQueryFilter
      }),
      DepositCashTransaction.find({
        owner: owner,
        status: "Settled",
        ...settlementDateQueryFilter
      }),
      WithdrawalCashTransaction.find({
        owner: owner,
        "providers.wealthkernel.status": "Settled",
        ...settlementDateQueryFilter
      }),
      SavingsTopupTransaction.find({
        owner: owner,
        status: "Settled",
        ...settlementDateQueryFilter
      }).populate("linkedSavingsDividend"),
      SavingsWithdrawalTransaction.find({
        owner: owner,
        status: "Settled",
        ...settlementDateQueryFilter
      }),
      SavingsDividendTransaction.find({
        owner: owner,
        status: "Settled",
        ...settlementDateQueryFilter
      }),
      Reward.find({ targetUser: owner, status: "Settled" })
    ]);

    const data: AccountStatementActivity = [];

    // 1. Add asset + rebalance transaction orders
    orders
      .filter((order) => (start ? order.updatedAt > start : true))
      .filter((order) => (end ? order.updatedAt < end : true))
      .forEach((order) => {
        const assetId = InvestmentUniverseUtil.getAssetIdFromIsin(order.isin);
        const assetConfig = ASSET_CONFIG[assetId];
        const assetName =
          assetConfig.category === "etf"
            ? (assetConfig as investmentUniverseConfig.ETFAssetConfigType).formalName
            : (assetConfig as investmentUniverseConfig.StockAssetConfigType).formalTicker;

        const line: AccountStatementDataEntry = {
          amount: Decimal.div(order.consideration.amount, 100).toNumber(),
          currency: order.consideration.currency,
          isin: order.isin,
          type: "order",
          date: order.updatedAt,
          side: order.side,
          asset: assetName
        };
        data.push(line);
      });

    // 2. Add cashback transactions
    cashbacks.forEach((txn: CashbackTransactionDocument) => {
      const line: AccountStatementDataEntry = {
        amount: Decimal.div(txn.consideration.amount, 100).toNumber(),
        currency: txn.consideration.currency,
        date: txn.settledAt,
        type: "cashback"
      };
      data.push(line);
    });

    // 3. Add WH dividend transactions
    wealthyhoodDividends.forEach((txn: WealthyhoodDividendTransactionDocument) => {
      const line: AccountStatementDataEntry = {
        amount: Decimal.div(txn.consideration.amount, 100).toNumber(),
        currency: txn.consideration.currency,
        date: txn.settledAt,
        type: "bonus"
      };
      data.push(line);
    });

    // 4. Add dividend transactions
    dividends.forEach((txn: DividendTransactionDocument) => {
      const asset = investmentUniverseConfig.ASSET_CONFIG[txn.asset];
      const assetName =
        asset.category === "etf"
          ? (asset as investmentUniverseConfig.ETFAssetConfigType).formalName
          : (asset as investmentUniverseConfig.StockAssetConfigType).formalTicker;

      const line: AccountStatementDataEntry = {
        amount: Decimal.div(txn.consideration.amount, 100).toNumber(),
        currency: txn.consideration.currency,
        date: txn.createdAt,
        isin: asset.isin,
        asset: assetName,
        type: "dividend"
      };
      data.push(line);
    });

    // 5. Add withdrawal transactions
    withdrawals.forEach((txn: WithdrawalCashTransactionDocument) => {
      const line: AccountStatementDataEntry = {
        amount: Decimal.div(txn.consideration.amount, 100).toNumber(),
        currency: txn.consideration.currency,
        date: txn.createdAt,
        type: "withdrawal"
      };
      data.push(line);
    });

    // 6. Add deposit transactions
    deposits.forEach((txn: DepositCashTransactionDocument) => {
      const line: AccountStatementDataEntry = {
        amount: Decimal.div(txn.consideration.amount, 100).toNumber(),
        currency: txn.consideration.currency,
        date: txn.settledAt,
        type: "deposit"
      };
      data.push(line);
    });

    // 7. Add savings dividend transactions
    savingsDividends.forEach((txn: SavingsDividendTransactionDocument) => {
      const { fundName, isin } = SAVINGS_PRODUCT_CONFIG_GLOBAL[txn.savingsProduct];

      const line: AccountStatementDataEntry = {
        amount: Decimal.div(txn.consideration.amount, 100).toNumber(),
        currency: txn.consideration.currency,
        date: txn.settledAt,
        isin: isin,
        asset: fundName,
        type: "interest"
      };
      data.push(line);
    });

    // 8. Add savings top-up/withdrawal transactions
    savingsTopUps
      .concat(savingsWithdrawals)
      .forEach((txn: SavingsTopupTransactionDocument | SavingsWithdrawalTransactionDocument) => {
        const { fundName, isin } = SAVINGS_PRODUCT_CONFIG_GLOBAL[txn.savingsProduct];

        const isDividendReinvestment = !!(txn as SavingsTopupTransactionDocument).linkedSavingsDividend;

        const line: AccountStatementDataEntry = {
          amount: Decimal.div(txn.consideration.amount, 100).toNumber(),
          currency: txn.consideration.currency,
          isin: isin,
          type: isDividendReinvestment ? "interest reinvestment" : "order",
          date: txn.settledAt,
          side: txn.category === "SavingsTopupTransaction" ? "Buy" : "Sell",
          asset: fundName
        };
        data.push(line);
      });

    // 9. Add rewards
    rewards
      .filter((reward) => (start ? reward.updatedAt > start : true))
      .filter((reward) => (end ? reward.updatedAt < end : true))
      .forEach((reward) => {
        const assetId = InvestmentUniverseUtil.getAssetIdFromIsin(reward.isin);
        const assetConfig = ASSET_CONFIG[assetId];
        const assetName =
          assetConfig.category === "etf"
            ? (assetConfig as investmentUniverseConfig.ETFAssetConfigType).formalName
            : (assetConfig as investmentUniverseConfig.StockAssetConfigType).formalTicker;
        const line: AccountStatementDataEntry = {
          amount: Decimal.div(reward.consideration.amount, 100).toNumber(),
          currency: reward.consideration.currency,
          isin: reward.isin,
          type: "reward",
          date: reward.updatedAt,
          asset: assetName
        };
        data.push(line);
      });

    return data.sort((a, b) => b.date.getTime() - a.date.getTime());
  }

  public static async addSumsubIdToUser(userId: string, sumsubId: string): Promise<void> {
    await User.findByIdAndUpdate(userId, { "providers.sumsub.id": sumsubId });
  }

  /**
   * @returns the daily summaries for the user.
   *
   * There are two main categories for users regarding snapshots.
   * 1. Users with snapshots - i.e. users with cash/savings/investments.
   * 2. Users with no snapshots:
   *   a) Users that have no activity
   *   b) Users that have some recent activity but no snapshots have been created yet
   *
   * @param user
   */
  public static async getDailySummaries(user: UserDocument): Promise<DailySummariesType> {
    const userHasSnapshots = await DailySummarySnapshotService.hasUserSnapshots(user);
    if (userHasSnapshots) {
      return UserService._getUserWithSnapshotsDailySummariesView(user);
    } else {
      return UserService._getUserWithNoSnapshotsDailySummariesView(user);
    }
  }

  /**
   * PRIVATE METHODS
   */

  /**
   * Goes through our duplicate account checks. If the user fails any of the checks, we flag them as potentially
   * duplicate.
   * @param user
   * @private
   */
  private static async _runPotentiallyDuplicateUserChecks(user: UserDocument): Promise<UserDocument> {
    if (
      !envIsProd() ||
      EXEMPT_EMAIL_PREFIXES_FROM_DUPLICATE_CHECK.some((prefix) => user.email.startsWith(prefix)) ||
      user.email.endsWith(EXEMPT_EMAIL_SUFFIX_FROM_DUPLICATE_CHECK)
    ) {
      return user; // In dev/staging, since we often use the same NINo, this method would block us from doing QA.
    }

    const potentiallyDuplicateUsers = (await Promise.all([
      User.findOne({
        _id: { $not: { $eq: user._id } },
        $and: [
          { "taxResidency.value": { $ne: null } },
          { "taxResidency.value": { $ne: undefined } },
          { "taxResidency.value": user.taxResidency.value }
        ],
        companyEntity: user.companyEntity
      }),
      User.findOne({
        _id: { $not: { $eq: user._id } },
        lastName: { $regex: `^${user.lastName}$`, $options: "i" },
        dateOfBirth: user.dateOfBirth,
        companyEntity: user.companyEntity
      }),
      User.findOne({
        _id: { $not: { $eq: user._id } },
        email: `deleted_${user.email}`,
        companyEntity: user.companyEntity
      })
    ])) as UserDocument[];

    if (potentiallyDuplicateUsers.some((user) => !!user)) {
      // We've found a potentially duplicate user, so we flag the user and exit before doing the address check.
      return User.findByIdAndUpdate(user._id, { isPotentiallyDuplicateAccount: true }, { new: true });
    }

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.ADDRESSES);
    const usersWithSameName = await User.find({
      _id: { $not: { $eq: user._id } },
      firstName: { $regex: `^${user.firstName}$`, $options: "i" },
      lastName: { $regex: `^${user.lastName}$`, $options: "i" },
      companyEntity: user.companyEntity
    }).populate("addresses");
    for (let i = 0; i < usersWithSameName.length; i++) {
      const address = usersWithSameName[i].addresses[0];
      if (address?.postalCode === user.addresses[0]?.postalCode) {
        return User.findByIdAndUpdate(user._id, { isPotentiallyDuplicateAccount: true }, { new: true });
      }
    }

    // We haven't exited so far so that means the user has passed all our checks.
    return user;
  }

  private static async _setParticipant(
    user: UserDocument,
    referral: CreateParticipantData
  ): Promise<UserDocument> {
    let joinedWithCode: ReferralCodeDocument;
    if (referral?.wlthd) {
      const referralCode = await ReferralCodeRepository.getReferralCodeByCode(referral.wlthd);
      if (referralCode && referralCode.active) {
        joinedWithCode = referralCode;
      }
    }

    // Fetch user from participant service - this will run on signup only so the document referral info will be empty
    const userEmail = user.email;
    let participant = await ParticipantService.getParticipantByEmail(userEmail);

    const existingRewardInvitation = (
      await RewardInvitationService.getRewardInvitations({ targetUserEmail: user.email })
    )?.[0];

    if (existingRewardInvitation) {
      const referrer = await UserService.getUser(existingRewardInvitation.referrer.toString(), {
        participant: true
      });

      referral.referrerEmail = referrer.email;
    }

    if (!participant) {
      // There is no participant *for the given email*, which means that the user:
      // 1. Signed up through web
      // 2. or Signed up through mobile with a 3rd party provider (apple, google)
      // The participant is created in advance, only when the user submits their email in the login text field
      // and the mobile clients hit the /participants endpoint.
      logger.info(`Participant does not exist for user email '${user.email}, creating one...`, {
        module: "UserService",
        method: "_setParticipant",
        userEmail: user.email,
        data: {
          referral
        }
      });

      const participantData: any = {
        appInstallInfo: referral.appInstallInfo,
        email: userEmail,
        anonymousId: referral?.anonymousId,
        appsflyerId: referral?.appsflyerId,
        googleAdsMetadata: referral?.googleAdsMetadata,
        gaClientId: referral?.gaClientId,
        pageUserLanded: referral?.pageUserLanded,
        influencerId: referral?.financeAdsSessionId,
        participantRole: "BASIC" as ParticipantRoleType,
        trackingSource: referral?.trackingSource,
        submissionTechClickId: referral?.submissionTechClickId
      };

      if (referral?.referrerEmail) {
        participantData.referrerEmail = referral.referrerEmail;
      } else if (referral?.wlthd) {
        participantData.referrerWlthdId = referral.wlthd;
      } else if (referral?.grsf) {
        participantData.referrerGrsfId = referral.grsf;
      }

      participant = await ParticipantService.createParticipant(participantData, { emitEmailSubmittedEvent: true });
    } else {
      // The participant has already been created during email submission
      // and the sign up point is where we make the final decision about who is the referrer
      // but only if referrer data exists (we may sign up with appsflyer conversion data failure).
      // Since participant was created during email submission or appsflyer conversion error, that takes place
      // only on mobile clients, the appsflyer id and the app install info have already been set, so no need to
      // update that info.
      logger.info(`Participant already exists for user email '${user.email}, updating it...`, {
        module: "UserService",
        method: "_setParticipant",
        userEmail: user.email,
        data: {
          referral
        }
      });

      const participantData: any = {
        gaClientId: referral.gaClientId,
        trackingSource: referral.trackingSource,
        googleAdsMetadata: referral?.googleAdsMetadata,
        submissionTechClickId: referral?.submissionTechClickId
      };

      // Final decision about referrer is made during sign up
      const referrerExists = Boolean(
        referral.referrerEmail || referral.wlthd || referral.grsf || referral.financeAdsSessionId
      );
      if (referrerExists) {
        participantData.referrer = {
          influencerId: referral.financeAdsSessionId,
          referrerEmail: referral.referrerEmail,
          referrerWlthdId: referral.wlthd,
          referrerGrsfId: referral.grsf
        };
      }

      participant = await ParticipantService.updateParticipant(participant, participantData);
    }

    // if participant is referred update the user document
    const referrer = participant?.referrer as ParticipantDocument;
    const referrerEmail = referrer?.email;
    let updatedUser = user;
    if (referrerEmail) {
      if (!joinedWithCode) {
        captureMessage(
          `For user ${userEmail} we are setting referrer ${referrerEmail} without active code ${referral?.wlthd}`
        );
      }

      updatedUser = await User.findOneAndUpdate(
        { email: userEmail },
        { referredByEmail: referrerEmail, joinedWithCode },
        { new: true }
      );
    }

    return updatedUser;
  }

  /**
   * @description Creates or syncs party for given user.
   *
   * If the user already has a party in its document, we do nothing.
   * If party already exists in our broker but our document does not have a reference to it, then we sync our
   * model to include one.
   *
   * @returns true if new party was created in our broker, false if either the party
   * already existed in our model or was synced in our model as part of this execution.
   */
  private static async _createOrSyncBrokerageParty(
    user: UserDocument
  ): Promise<{ userAfterPartyUpdates: UserDocument; createdNewBrokerageParty: boolean }> {
    if (user.isSubmittedToBroker) {
      // This user already has been submitted to our broker and our database is synced!
      return { userAfterPartyUpdates: user, createdNewBrokerageParty: false };
    }

    const existingParties = await ProviderService.getBrokerageService(user.companyEntity).retrieveParties(
      user.email
    );

    if (existingParties.length > 0) {
      // The user doesn't have a party in our database but the party exists in our brokerage provider.
      const partyId = existingParties[0].id;

      const updatedUser = await User.findOneAndUpdate(
        { _id: user._id },
        { "providers.wealthkernel": { id: partyId } }, // For now, we assume the only broker is Wealthkernel
        { runValidators: true, new: true }
      );

      return { userAfterPartyUpdates: updatedUser, createdNewBrokerageParty: false };
    }

    // Neither our database nor our broker have a party for this user, we should create them.
    const partyResponse = await ProviderService.getBrokerageService(user.companyEntity).createParty({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      dateOfBirth: user.dateOfBirth,
      nationalities: user.nationalities,
      taxResidency: user.taxResidency,
      employmentInfo: user.employmentInfo
    });
    const userAfterPartyUpdates = await User.findOneAndUpdate(
      { _id: user._id },
      { "providers.wealthkernel": { id: partyResponse.id } },
      {
        runValidators: true,
        setDefaultsOnInsert: true,
        new: true
      }
    );

    return {
      userAfterPartyUpdates,
      createdNewBrokerageParty: true
    };
  }

  /**
   * @description Creates & syncs wealthkernel W-8BEN form for given user.
   */
  private static async _createOrSyncBrokerageW8BenForm(
    user: UserDocument
  ): Promise<{ userAfterW8BenFormUpdated: UserDocument; w8BenFormCompleted: boolean }> {
    if (!user.w8BenForm.activeProviders.includes(ProviderEnum.WEALTHKERNEL)) {
      return { userAfterW8BenFormUpdated: user, w8BenFormCompleted: true };
    }

    if (user.w8BenForm?.providers?.wealthkernel?.status === "Completed") {
      return { userAfterW8BenFormUpdated: user, w8BenFormCompleted: true };
    } else if (user.w8BenForm?.providers?.wealthkernel?.id) {
      return UserService._completeWkW8BenFormEntrySafely(user);
    } else {
      return {
        userAfterW8BenFormUpdated: await UserService._createWkW8BenFormEntrySafely(user),
        w8BenFormCompleted: false
      };
    }
  }

  private static _getReferredStatus(referrer: ParticipantDocument): ReferredStatus {
    let referred: ReferredStatus = "False";
    if (referrer?.email) {
      if (referrer.isAmbassador) {
        referred = "Affiliate";
      } else {
        referred = "True";
      }
    }
    return referred;
  }

  private static async _createStripeEntrySafely(
    user: UserDocument
  ): Promise<{ userAfterStripeUpdates: UserDocument; createdNewCustomerInStripe: boolean }> {
    try {
      if (user.providers?.stripe?.id) {
        // This user already has a Stripe customer created and our database is synced!
        return { userAfterStripeUpdates: user, createdNewCustomerInStripe: false };
      }

      const customerData = {
        email: user.email,
        name: `${user.firstName} ${user.lastName}`,
        metadata: {
          wealthyhoodId: user.id
        }
      };
      const { id } = await StripeService.Instance.createCustomer(customerData);

      return {
        userAfterStripeUpdates: await User.findByIdAndUpdate(user.id, {
          "providers.stripe": { id }
        }),
        createdNewCustomerInStripe: true
      };
    } catch (err) {
      setUser({ email: user.email });
      captureException(err);
      logger.error(`Creating a Stripe entry failed for user ${user.id}`, {
        module: "UserService",
        method: "_createStripeEntrySafely",
        data: { user: user.id, error: JSON.stringify(err) }
      });
    }
  }

  private static async _createSaltedgeEntrySafely(
    user: UserDocument
  ): Promise<{ userAfterSaltedgeUpdates: UserDocument; createdNewLeadInSaltedge: boolean }> {
    try {
      if (user.providers?.saltedge?.id) {
        // This user already has a Saltedge lead created and our database is synced!
        return { userAfterSaltedgeUpdates: user, createdNewLeadInSaltedge: false };
      } else if (!user.activeProviders.includes(ProviderEnum.SALTEDGE)) {
        return { userAfterSaltedgeUpdates: user, createdNewLeadInSaltedge: false };
      }

      const leadData = {
        fullName: user.fullName,
        email: user.email,
        identifier: user.id
      };
      const { data } = await SaltedgeService.Instance.createLead(leadData);

      return {
        userAfterSaltedgeUpdates: await User.findByIdAndUpdate(user.id, {
          "providers.saltedge": { id: data.customer_id }
        }),
        createdNewLeadInSaltedge: true
      };
    } catch (err) {
      setUser({ email: user.email });
      captureException(err);
      logger.error(`Creating a Saltedge entry failed for user ${user.id}`, {
        module: "UserService",
        method: "_createSaltedgeEntrySafely",
        data: { user: user.id, error: JSON.stringify(err) }
      });
    }
  }

  private static async _createWkW8BenFormEntrySafely(user: UserDocument): Promise<UserDocument> {
    try {
      if (user.w8BenForm?.providers?.wealthkernel?.id) {
        // This user already has a W_8BEN form created!
        return user;
      } else if (!user.providers.wealthkernel.id) {
        // if the user does not have a wealthkernel party, we shouldn't create a w8ben form yet
        return user;
      }

      const { id } = await ProviderService.getBrokerageService(user.companyEntity).createW8BenForm({
        partyId: user.providers.wealthkernel.id
      });

      return User.findByIdAndUpdate(
        user.id,
        {
          "w8BenForm.providers.wealthkernel": { id, status: "Pending" }
        },
        { new: true }
      );
    } catch (err) {
      setUser({ email: user.email });
      captureException(err);
      logger.error(`Creating a W-8BEN form entry failed for user ${user.id}`, {
        module: "UserService",
        method: "_createWkW8BenFormEntrySafely",
        data: { user: user.id, error: JSON.stringify(err) }
      });
    }
  }

  private static async _completeWkW8BenFormEntrySafely(
    user: UserDocument
  ): Promise<{ userAfterW8BenFormUpdated: UserDocument; w8BenFormCompleted: boolean }> {
    try {
      if (user.w8BenForm?.providers?.wealthkernel?.status === "Completed") {
        // This user already has a W_8BEN form completed!
        return { userAfterW8BenFormUpdated: user, w8BenFormCompleted: true };
      }

      await ProviderService.getBrokerageService(user.companyEntity).completeW8BenForm(
        user.w8BenForm.providers.wealthkernel.id,
        {
          completedAt: user.w8BenForm.completedAt.toISOString()
        }
      );

      return {
        userAfterW8BenFormUpdated: await User.findByIdAndUpdate(
          user.id,
          {
            "w8BenForm.providers.wealthkernel.status": "Completed"
          },
          { new: true }
        ),
        w8BenFormCompleted: true
      };
    } catch (err) {
      setUser({ email: user.email });
      captureException(err);
      logger.error(`Completing a W-8BEN form entry failed for user ${user.id}`, {
        module: "UserService",
        method: "_completeWkW8BenFormEntrySafely",
        data: { user: user.id, error: JSON.stringify(err) }
      });
    }
  }

  private static _isEmailDisposable(email: string): boolean {
    const [, domain] = email.split("@");
    return disposableDomains.includes(domain);
  }

  private static async _handleUserSignup(
    userData: CreateUserPartial,
    referral?: CreateParticipantData
  ): Promise<UserDocument> {
    const emailDisposable = UserService._isEmailDisposable(userData.email);

    const provider = AUTH_PROVIDERS_MAPPING[userData.auth0.id.split("|")[0]];
    const newAuth0 = { id: userData.auth0.id, [provider]: userData.auth0.id };

    // if the user has already a gift we don't want him to see the referral page. So we mark it as seen.
    let viewedReferralCodeScreen = false;
    const userExistingGifts = (await GiftService.getGifts({
      targetUserEmail: userData.email
    })) as { data: GiftDocument[] };

    if (userExistingGifts.data?.length > 0) {
      viewedReferralCodeScreen = true;
    }

    const userDocument = await new User({
      ...userData,
      auth0: newAuth0,
      emailDisposable,
      viewedReferralCodeScreen
    }).save();

    await AccountService.createGeneralInvestmentAccount(userDocument);
    await PortfolioService.createGeneralInvestmentPortfolio(userDocument);
    await NotificationSettingsService.createDefaultNotificationSettingsForUser(userDocument);

    let updatedUser: UserDocument = userDocument;
    try {
      // Create a participant, if needed, with the required referral information
      updatedUser = await UserService._setParticipant(userDocument, referral);

      // Create a one-time referral code during sign up
      await ReferralCodeService.generateExpiringCode(updatedUser);
    } catch (err) {
      setUser({ email: userData.email });
      captureException(err);
      logger.error("Error in referral triggering API request", {
        module: "UserService",
        method: "_handleUserSignup",
        userEmail: userData.email,
        data: {
          error: err,
          referralData: referral
        }
      });
    }

    await updatedUser.populate(FULL_USER_POPULATIONS);

    const gift = ((await GiftService.getGifts({ targetUserEmail: updatedUser.email })) as { data: GiftDocument[] })
      .data[0];

    // There was no document for the user, so they just signed up
    // NOTE: the signup event should be sent after the participant has been
    // created and any attribution params have been set, in order to track
    // those params correctly in third party services
    eventEmitter.emit(events.user.signUp.eventId, updatedUser, {
      referredStatus: UserService._getReferredStatus(
        (updatedUser.participant as ParticipantDocument)?.referrer as ParticipantDocument
      ),
      gift
    });
    eventEmitter.emit(events.user.whAccountStatusUpdate.eventId, updatedUser, {
      accountStatus: MixpanelAccountStatusEnum.Pending
    });

    return updatedUser;
  }

  /**
   * Method called when an existing user just logged in from our Auth0 login page/mobile screen.
   * @param existingUser
   * @param userData
   * @param referral
   * @private
   */
  private static async _handleExistingUserLogin(
    existingUser: UserDocument,
    userData: CreateUserPartial,
    referral?: CreateParticipantData
  ): Promise<UserDocument> {
    let updatedAuth0 = {};
    const [provider, id] = userData.auth0.id.split("|");
    const isNewAuthId = !Object.entries(existingUser.auth0)
      .filter(([, id]) => id !== undefined && id !== null)
      .map(([provider]) => provider)
      .includes(AUTH_PROVIDERS_MAPPING[provider]);

    if (isNewAuthId) {
      try {
        logger.info("User just logged in with a new authentication mechanism, trying to link...", {
          module: "UserService",
          method: "_handleExistingUserLogin",
          userEmail: userData.email,
          data: {
            existingAuth0Ids: existingUser.auth0,
            newAuthId: userData.auth0.id
          }
        });
        await auth0ManagementClient.users.link(
          { id: existingUser.auth0.id },
          { user_id: id, provider: provider as PostIdentitiesRequestProviderEnum }
        );

        updatedAuth0 = { [AUTH_PROVIDERS_MAPPING[provider]]: userData.auth0.id };
      } catch (err) {
        logger.error("Error in linking Auth0 users", {
          module: "UserService",
          method: "_handleExistingUserLogin",
          userEmail: userData.email,
          data: {
            error: err
          }
        });
      }
    }

    const updatedUser = await User.findByIdAndUpdate(
      existingUser.id,
      {
        auth0: { ...existingUser.auth0, ...updatedAuth0 },
        lastLogin: userData.lastLogin,
        role: userData.role,
        lastLoginPlatform: userData.lastLoginPlatform
      },
      {
        new: true,
        runValidators: true
      }
    ).populate(FULL_USER_POPULATIONS);

    let justDownloadedApp = false;
    if (!updatedUser.participant) {
      // handle the scenario where the user does not have a participant
      // that can happen if a user signed up through an old flow on the web and later downloaded the app
      logger.warn(`User '${updatedUser.email} just logged in but participant does not exist, creating one...`, {
        module: "UserService",
        method: "_handleExistingUserLogin",
        userEmail: updatedUser.email
      });

      const participantData: any = {
        appInstallInfo: {
          createdAt: new Date(),
          platform: userData.lastLoginPlatform
        },
        email: updatedUser.email,
        anonymousId: referral?.anonymousId,
        appsflyerId: referral?.appsflyerId,
        gaClientId: referral?.gaClientId,
        participantRole: "BASIC" as ParticipantRoleType
      };
      await ParticipantService.createParticipant(participantData);

      await updatedUser.populate("participant");
    } else if (!updatedUser.participant.appsflyerId && referral?.appsflyerId) {
      justDownloadedApp = true;
      // the user has a participant but is missing some app info (appsflyerId)
      // because they logged in through the app for the first time
      await ParticipantService.updateParticipant(updatedUser.participant, {
        appsflyerId: referral.appsflyerId,
        appInstallInfo: referral.appInstallInfo
      });
      updatedUser.depopulate("participant");
      await updatedUser.populate("participant");
    }

    eventEmitter.emit(events.user.logIn.eventId, updatedUser, { justDownloadedApp });

    return updatedUser;
  }

  private static _createDbFilter(filter: UsersFilter): {
    role?: { $in: UserTypeEnum[] };
    portfolioConversionStatus?: { $in: PortfolioConversionStatusType[] };
    createdAt?: { $gte: Date };
  } {
    const actualFilter: {
      role?: { $in: UserTypeEnum[] };
      email?: string | { $regex: string };
      portfolioConversionStatus?: { $in: PortfolioConversionStatusType[] };
      createdAt?: { $gte?: Date };
      kycFailedAt?: { $gte?: Date };
      kycStatus?: KycStatusType;
      isPotentiallyDuplicateAccount?: boolean;
      referredByEmail?: { $exists: boolean };
      submittedRequiredInfoAt?: { $exists: boolean };
      hasAcceptedTerms?: boolean;
    } = {
      role: null,
      email: null,
      portfolioConversionStatus: null,
      kycStatus: null,
      createdAt: null,
      isPotentiallyDuplicateAccount: null,
      referredByEmail: null
    };

    if (filter.roles) {
      actualFilter.role = { $in: filter.roles };
    }

    if (filter.potentiallyDuplicateOnly) {
      actualFilter.isPotentiallyDuplicateAccount = true;
    }

    if (filter.referredOnly) {
      actualFilter["referredByEmail"] = { $exists: true };
    }

    if (filter.portfolioConversionStatus) {
      actualFilter.portfolioConversionStatus = { $in: [filter.portfolioConversionStatus] };
    } else if (filter.converted) {
      actualFilter.portfolioConversionStatus = { $in: ["completed"] };
    } else if (filter.converted === false) {
      actualFilter.portfolioConversionStatus = { $in: ["inProgress", "notStarted"] };
    }

    if (filter.createdAfter) {
      actualFilter.createdAt = { $gte: filter.createdAfter };
    }

    if (filter.kycFailedAfter) {
      actualFilter.kycFailedAt = { $gte: filter.kycFailedAfter };
    }

    if (filter.hasRequestedDeletion === true) {
      actualFilter.email = { $regex: "^deleted_.*" };
    } else if (filter.hasRequestedDeletion === false) {
      actualFilter.email = { $regex: "^(?!deleted_)" };
    }

    if (filter.hasAcceptedTerms === true) {
      actualFilter.hasAcceptedTerms = true;
    } else if (filter.hasAcceptedTerms === false) {
      actualFilter.hasAcceptedTerms = false;
    }

    if (filter.hasSubmittedRequiredInfo === true) {
      actualFilter.submittedRequiredInfoAt = { $exists: true };
    } else if (filter.hasSubmittedRequiredInfo === false) {
      actualFilter.submittedRequiredInfoAt = { $exists: false };
    }

    Object.keys(actualFilter).forEach((key) => {
      if ((filter as any)[key] != null) {
        (actualFilter as any)[key] = (filter as any)[key];
      }
    });

    return Object.fromEntries(Object.entries(actualFilter).filter(([, value]) => value != null));
  }

  private static async _getUsers(
    filter: UsersFilter = {},
    populate?: {
      portfolios?: boolean;
      kycOperation?: boolean;
      accounts?: boolean;
    },
    sort?: string
  ): Promise<ApiResponse<UserDocument>> {
    const actualFilter = UserService._createDbFilter(filter);

    const options: QueryOptions = { populate: [] };
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    if (populate?.portfolios) {
      options.populate = [
        {
          path: "portfolios",
          populate: {
            path: "currentTicker"
          }
        }
      ];
    }
    if (populate?.kycOperation) {
      options.populate = [
        ...((options.populate ?? []) as PopulateOptions[]),
        {
          path: "kycOperation"
        }
      ];
    }
    if (populate?.accounts) {
      options.populate = [
        ...((options.populate ?? []) as PopulateOptions[]),
        {
          path: "accounts"
        }
      ];
    }

    const users: UserDocument[] = await User.find(actualFilter, null, options);

    return { data: users };
  }

  private static async _getUsersPaginated(
    filter: UsersFilter = {},
    pageConfig: { page: number; pageSize: number },
    populate?: {
      portfolios?: boolean;
      kycOperation?: boolean;
      accounts?: boolean;
    },
    sort?: string
  ): Promise<PaginatedUsersResponse> {
    const actualFilter = UserService._createDbFilter(filter);
    const count = await User.countDocuments(actualFilter);
    const pageConfigToUse = PaginationUtil.getPaginationParametersFor(count, pageConfig);

    const options: QueryOptions = {};
    if (sort) {
      options.sort = DbUtil.determineSorting(sort);
    }

    if (populate?.portfolios) {
      options.populate = [
        {
          path: "portfolios",
          populate: {
            path: "currentTicker"
          }
        }
      ];
    }
    if (populate?.kycOperation) {
      options.populate = [
        ...((options.populate ?? []) as PopulateOptions[]),
        {
          path: "kycOperation"
        }
      ];
    }
    if (populate?.accounts) {
      options.populate = [
        ...((options.populate ?? []) as PopulateOptions[]),
        {
          path: "accounts"
        }
      ];
    }

    const users: UserDocument[] = await User.find(actualFilter, null, options)
      .skip((pageConfigToUse.page - 1) * pageConfigToUse.pageSize)
      .limit(pageConfigToUse.pageSize);

    return { pagination: pageConfigToUse, users };
  }

  /**
   *
   * @param userToCheck
   * @param amount is in £ *not c*
   * @returns
   */
  private static async _userHasTotalInvestmentAmount(userToCheck: UserDocument, amount: number) {
    const userInvestments = await AssetTransaction.find({
      owner: userToCheck.id,
      portfolioTransactionCategory: { $in: ["buy", "update"] },
      status: "Settled"
    }).populate("orders");

    const totalBuyAmount = userInvestments
      .filter((transaction) => {
        const isPortfolioBuy = transaction.portfolioTransactionCategory === "buy";
        const isAssetBuy =
          transaction.portfolioTransactionCategory === "update" &&
          transaction.orders.length === 1 &&
          transaction.orders[0].side === "Buy";

        return isPortfolioBuy || isAssetBuy;
      })
      .map((transaction) => {
        return transaction.portfolioTransactionCategory === "buy"
          ? (transaction.originalInvestmentAmount ?? transaction.consideration.amount)
          : (transaction.orders[0].consideration.originalAmount ??
              transaction.orders[0].consideration.amountSubmitted ??
              transaction.orders[0].consideration.amount);
      })
      .reduce((sum, quantity) => sum.plus(quantity), new Decimal(0))
      .div(100); // totalBuyAmount is in cents and needs to be converted to £ to compare with input amount

    return totalBuyAmount.greaterThanOrEqualTo(amount);
  }

  private static async _userHasTotalDepositAmount(userToCheck: UserDocument, amount: number): Promise<boolean> {
    const deposits = await DepositCashTransaction.find({
      owner: userToCheck.id,
      status: { $in: ["Settled", "Pending"] }
    });

    const eligibleDeposits = deposits.filter((deposit) => {
      if (deposit.status === "Settled") {
        return true;
      }

      switch (deposit.depositMethod) {
        case DepositMethodEnum.BANK_TRANSFER: {
          const devengoStatus =
            deposit.transferWithIntermediary?.acquisition?.incomingPayment?.providers?.devengo?.status;
          return devengoStatus === "confirmed";
        }
        case DepositMethodEnum.DIRECT_DEBIT:
        case DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER: {
          return deposit.isDirectDebitPaymentCollected === true;
        }
        case DepositMethodEnum.OPEN_BANKING: {
          return deposit.isPaymentAuthorised === true;
        }
        default:
          return false;
      }
    });

    const totalDepositAmount = eligibleDeposits
      .reduce((sum, deposit) => sum.plus(deposit.consideration.amount), new Decimal(0))
      .div(100);

    return totalDepositAmount.greaterThanOrEqualTo(amount);
  }

  private static async _getModalPrompts(user: UserDocument): Promise<ModalPromptType[]> {
    const [portfolio, settledRewards, gifts, wealthyhoodDividends, appRatingPrompt] = (await Promise.all([
      PortfolioService.getGeneralInvestmentPortfolio(user, false),
      RewardService.getRewards(
        {
          targetUser: user.id,
          status: "Settled",
          hasViewedAppModal: false
        },
        null,
        { portfolio: false }
      ),
      GiftService.getGifts(
        {
          targetUserEmail: user.email,
          hasViewedAppModal: false
        },
        null,
        { gifter: true, linkedAssetTransaction: false }
      ),
      WealthyhoodDividendTransaction.find({
        owner: user.id,
        "deposit.providers.wealthkernel.status": "Settled",
        hasViewedAppModal: false
      }),
      AppRatingService.getPrompt(user)
    ])) as [
      PortfolioDocument,
      { data: RewardDocument[] },
      { data: GiftDocument[] },
      WealthyhoodDividendTransactionDocument[],
      AppRatingPromptType
    ];

    const modalPrompts: ModalPromptType[] = [];
    let currentOrder = 0;

    if (appRatingPrompt) {
      modalPrompts.push({
        order: currentOrder,
        modalType: "AppRatingPrompt",
        data: {
          appRatingId: appRatingPrompt.appRatingId
        }
      });
      currentOrder++;
    }

    // Show settled rewards (received notification)
    if (settledRewards.data.length > 0) {
      modalPrompts.push({
        order: currentOrder,
        modalType: "RewardSettled",
        data: { rewards: settledRewards.data }
      });
      currentOrder++;
    }
    if (gifts.data.length > 0 && (portfolio.isTargetAllocationSetup || portfolio.hasHoldings)) {
      modalPrompts.push({
        order: currentOrder,
        modalType: "Gift",
        data: { gifts: gifts.data }
      });
      currentOrder++;
    }
    if (wealthyhoodDividends.length > 0) {
      modalPrompts.push({
        order: currentOrder,
        modalType: "WealthyhoodDividend",
        data: { wealthyhoodDividends }
      });
    }

    return modalPrompts;
  }

  private static _getAnchorDateTime(): DateTime {
    return DateTime.fromMillis(0);
  }

  private static _getBannerSampleOfRotationalCategory(
    category: bannersConfig.CategoryEnum,
    bannersOfCategory: bannersConfig.BannerConfigType[]
  ): bannersConfig.BannerConfigType[] {
    const categoryConfig = CATEGORY_CONFIG[category];

    if (categoryConfig.type !== "rotational" || !categoryConfig.options) {
      throw new Error("Rotational banner categories must have options populated");
    }

    // Number of banner samples for given category
    const noOfSamples = Decimal.div(bannersOfCategory.length, categoryConfig.options.sampleSize).ceil();

    // Rotation length -> Number of Days needed to show all banner samples for given category once
    const rotationLength = Decimal.mul(noOfSamples, categoryConfig.options.sampleDuration);

    const elapsedDaysSinceAnchorDate = DateTime.utc().diff(UserService._getAnchorDateTime()).as("days");

    // Elapsed days in current rotation - Min: 0 - Max: rotationLength
    const elapsedDaysInCurrentRotation = Decimal.mod(elapsedDaysSinceAnchorDate, rotationLength);

    // Banner Pair Index - Min: 0 - Max: noOfSamples
    const bannerSampleIndex = Decimal.div(elapsedDaysInCurrentRotation, categoryConfig.options.sampleDuration)
      .floor()
      .toNumber();

    const bannerSample: bannersConfig.BannerConfigType[] = [];
    for (let sampleIt = 0; sampleIt < categoryConfig.options.sampleSize; sampleIt++) {
      let bannerIndex = bannerSampleIndex * categoryConfig.options.sampleSize + sampleIt;
      /**
       * Edge case for learning guide banner sample:
       * If banner sample is single -because bannersOfCategory length is odd number- show first banner as the second one
       * */
      if (bannerIndex > bannersOfCategory.length - 1) {
        bannerIndex = 0;
      }

      bannerSample.push(bannersOfCategory[bannerIndex]);
    }

    return bannerSample;
  }

  private static _getBanners(user: UserDocument): bannersConfig.BannerConfigType[] {
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);
    return getBanners(user.currency, locale, user.companyEntity);
  }

  private static _getBannerById(user: UserDocument, id: bannersConfig.BannerEnum): bannersConfig.BannerConfigType {
    const banners = UserService._getBanners(user);

    return banners.find((banner) => banner.id === id);
  }

  private static _mergeAndSortBannersByCategory(banners: bannersConfig.BannerConfigType[]): BannerPromptType[] {
    const sortedBannerPrompts: BannerPromptType[] = [];

    const sortedCategories = Object.values(CATEGORY_CONFIG).sort(
      (categoryConfigA, categoryConfigB) => categoryConfigA.order - categoryConfigB.order
    );

    let categoryOffset = 0;
    sortedCategories.forEach((categoryConfig) => {
      // Get banners for this category
      const categoryBanners = banners.filter((banner) => banner.category === categoryConfig.keyName);

      // Sort banners based on their index inside category
      const sortedCategoryBanners = categoryBanners.sort(
        (bannerA, bannerB) => bannerA.indexInCategory - bannerB.indexInCategory
      );

      // Create banner prompts
      // Add the cateogoryOffset in order to have a global order at the end
      const bannerPrompts: BannerPromptType[] = sortedCategoryBanners.map((banner, index) => {
        return {
          bannerId: banner.id,
          order: categoryOffset + index
        };
      });

      sortedBannerPrompts.push(...bannerPrompts);

      // Update categoryOffset of the next iteration
      categoryOffset = sortedBannerPrompts.length;
    });

    return sortedBannerPrompts;
  }

  private static async _getBannerPrompts(user: UserDocument): Promise<BannerPromptType[]> {
    const [
      portfolio,
      rewards,
      gifts,
      subscription,
      activeAutomations,
      latestAnalysis,
      latestQuickTake,
      latestWeeklyReview,
      userHasSavingsHoldingsOrPendingTopups,
      savingsProductFeeDetails,
      userCanUnlockFreeShare
    ]: BannerPrerequisitesType = await UserService._getBannerPrerequisites(user);

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    /** Logic for 'STATIC' banner category */
    const showRedeemGiftBanner =
      gifts.data.length > 0 && (portfolio.isTargetAllocationSetup || portfolio.hasHoldings);
    const showSendGiftBanner = user.canSendGiftUntil && user.canSendGiftUntil > new Date(Date.now());
    const showSweatcoinDealBanner =
      user.isSweatcoinReferred && PRICE_CONFIG[subscription?.price]?.recurrence !== "lifetime";

    const showRepeatingInvestmentBanner = activeAutomations.data.length === 0 && portfolio.isTargetAllocationSetup;
    const bannerConfigArray: bannersConfig.BannerConfigType[] = [];

    const sweatcoinBanner = UserService._getBannerById(user, BannerEnum.SweatcoinLifetimeGoldDeal);
    if (showSweatcoinDealBanner && sweatcoinBanner.active) {
      bannerConfigArray.push(sweatcoinBanner);
    }

    const redeemGiftBanner = UserService._getBannerById(user, BannerEnum.RedeemGift);
    if (showRedeemGiftBanner && redeemGiftBanner.active) {
      bannerConfigArray.push(redeemGiftBanner);
    }

    const sendGiftBanner = UserService._getBannerById(user, BannerEnum.SendGift);
    if (showSendGiftBanner && sendGiftBanner.active) {
      bannerConfigArray.push(sendGiftBanner);
    }

    const unlockFreeShareBanner = UserService._getBannerById(user, BannerEnum.UnlockFreeShare);
    if (userCanUnlockFreeShare && unlockFreeShareBanner.active) {
      bannerConfigArray.push(unlockFreeShareBanner);
    }

    const earnFreeSharesBanner = UserService._getBannerById(user, BannerEnum.EarnFreeShares);
    if (earnFreeSharesBanner && earnFreeSharesBanner.active) {
      bannerConfigArray.push(earnFreeSharesBanner);
    }

    const planPromotionBanners = UserService._getBanners(user).filter(
      (banner) =>
        banner.category === CategoryEnum.PLAN_PROMOTION &&
        banner?.options?.planRequired === subscription.plan &&
        banner.active
    );
    if (
      planPromotionBanners?.length > 0 &&
      !usersConfig.APPLE_DEMO_EMAILS.includes(user.email) &&
      !bannerConfigArray.includes(UserService._getBannerById(user, BannerEnum.SweatcoinLifetimeGoldDeal))
    ) {
      bannerConfigArray.push(
        ...UserService._getBannerSampleOfRotationalCategory(CategoryEnum.PLAN_PROMOTION, planPromotionBanners)
      );
    }

    const learningGuidePromotionBanners = UserService._getBanners(user).filter(
      (banner) => banner.category === CategoryEnum.LEARNING_GUIDE_PROMOTION && banner.active
    );
    bannerConfigArray.push(
      ...UserService._getBannerSampleOfRotationalCategory(
        CategoryEnum.LEARNING_GUIDE_PROMOTION,
        learningGuidePromotionBanners
      )
    );

    // Always show this banner
    const latestAnalysisBanner = UserService._getBannerById(user, BannerEnum.LatestAnalysis);
    if (latestAnalysisBanner.active && latestAnalysis) {
      bannerConfigArray.push(latestAnalysisBanner);
    }

    const latestQuickTakeBanner = UserService._getBannerById(user, BannerEnum.LatestQuickTake);
    if (latestQuickTakeBanner.active && latestQuickTake) {
      bannerConfigArray.push(latestQuickTakeBanner);
    }

    const latestWeeklyReviewBanner = UserService._getBannerById(user, BannerEnum.LatestWeeklyReview);
    if (latestWeeklyReviewBanner.active && latestWeeklyReview) {
      bannerConfigArray.push(latestWeeklyReviewBanner);
    }

    const investmentPromotionBanners = this._getInvestmentPromotionBanners(user, showRepeatingInvestmentBanner);
    bannerConfigArray.push(...investmentPromotionBanners);

    const savingsPromotionBanners = this._getSavingsPromotionBanners(user, userHasSavingsHoldingsOrPendingTopups);
    bannerConfigArray.push(...savingsPromotionBanners);

    const bannerPrompts = UserService._mergeAndSortBannersByCategory(bannerConfigArray);

    return UserService._populateBannerData(bannerPrompts, user, {
      latestAnalysis,
      latestQuickTake,
      latestWeeklyReview,
      gifts: gifts.data,
      savingsProductFeeDetails
    });
  }

  /**
   * @description
   * Overrides banner `data` block with the provided `overrideData` if the banner with `bannerId` exists in the `bannerPrompts`.
   */
  private static _overrideBannerDataIfExists(
    bannerPrompts: BannerPromptType[],
    bannerId: bannersConfig.BannerEnum,
    overrideData: Partial<BannerPromptDataType>
  ): BannerPromptType[] {
    return bannerPrompts.map((bannerPrompt) => {
      if (bannerPrompt.bannerId === bannerId) {
        return {
          ...bannerPrompt,
          data: {
            ...bannerPrompt.data,
            ...overrideData
          }
        };
      }
      return bannerPrompt;
    });
  }

  private static _populateBannerData(
    bannerPrompts: BannerPromptType[],
    user: UserDocument,
    prerequisiteData: {
      latestAnalysis: AnalystInsightType;
      latestQuickTake: AnalystInsightType;
      latestWeeklyReview: AnalystInsightType;
      gifts: GiftDocument[];
      savingsProductFeeDetails: SavingsProductFeeDetailsType;
    }
  ): BannerPromptType[] {
    const { latestAnalysis, latestQuickTake, latestWeeklyReview, gifts, savingsProductFeeDetails } =
      prerequisiteData;

    bannerPrompts = bannerPrompts.map((bannerPrompt) => {
      return {
        ...bannerPrompt,
        data: UserService._getBannerById(user, bannerPrompt.bannerId).data
      };
    });

    bannerPrompts = UserService._overrideBannerDataIfExists(bannerPrompts, BannerEnum.RedeemGift, {
      gifts: gifts
    });

    if (latestAnalysis) {
      bannerPrompts = UserService._overrideBannerDataIfExists(bannerPrompts, BannerEnum.LatestAnalysis, {
        analystInsightId: latestAnalysis.id,
        title: latestAnalysis.title,
        imageURL: latestAnalysis.bannerImageURL
      });
    }

    if (latestQuickTake) {
      bannerPrompts = UserService._overrideBannerDataIfExists(bannerPrompts, BannerEnum.LatestQuickTake, {
        analystInsightId: latestQuickTake.id,
        title: latestQuickTake.title,
        imageURL: latestQuickTake.bannerImageURL
      });
    }

    if (latestWeeklyReview) {
      bannerPrompts = UserService._overrideBannerDataIfExists(bannerPrompts, BannerEnum.LatestWeeklyReview, {
        analystInsightId: latestWeeklyReview.id,
        title: latestWeeklyReview.title,
        imageURL: latestWeeklyReview.bannerImageURL
      });
    }

    const interestRatePerPlan = Object.fromEntries(
      savingsProductFeeDetails.feeDetails.map((feeDetail) => [
        feeDetail.plan,
        Decimal.div(feeDetail.netInterestRateValue, 100).toNumber()
      ])
    ) as Record<plansConfig.PlanType, number>;
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);
    const freeInterestRate = formatPercentage(interestRatePerPlan["free"], locale);
    const paidLowInterestRate = formatPercentage(interestRatePerPlan["paid_low"], locale);
    const paidMidInterestRate = formatPercentage(interestRatePerPlan["paid_mid"], locale);
    bannerPrompts = UserService._overrideBannerDataIfExists(bannerPrompts, BannerEnum.EarnInterestOnYourMoney, {
      title: `Earn up to ${paidMidInterestRate} interest on your money!`,
      modalTitle: `Earn up to ${paidMidInterestRate} interest on your money!`,
      modalContent: [
        `You can now earn up to ${paidMidInterestRate} interest on your spare cash through Money Market Funds! 💰`,
        "MMFs provide an excellent alternative to traditional savings accounts, because they typically offer **higher interest rates**, allowing your money to grow faster – all while keeping it on the safe side!",
        "Depending on your plan, today's interest rate is:",
        `• Basic plan: **${freeInterestRate}**`,
        `• Plus plan: **${paidLowInterestRate}**`,
        `• Gold plan: **${paidMidInterestRate}**`,
        "The net interest rate is annualised and updated daily."
      ],
      modalButtonText: "Get started"
    });

    return bannerPrompts;
  }

  private static async _getBannerPrerequisites(user: UserDocument): Promise<BannerPrerequisitesType> {
    return (await Promise.all([
      PortfolioService.getGeneralInvestmentPortfolio(user, false),
      RewardService.getRewards(
        {
          targetUser: user.id,
          status: "Pending"
        },
        null,
        { portfolio: false }
      ),
      GiftService.getGifts(
        {
          targetUserEmail: user.email,
          used: false
        },
        null,
        { gifter: true, linkedAssetTransaction: true }
      ),
      SubscriptionService.getSubscription(user.id),
      AutomationService.getAutomations({ owner: user.id, categories: ["TopUpAutomation"], activeOnly: true }),
      WealthyhubService.getLatestAnalystInsight(ContentEntryContentTypeEnum.ANALYSIS),
      WealthyhubService.getLatestAnalystInsight(ContentEntryContentTypeEnum.QUICK_TAKE),
      WealthyhubService.getLatestAnalystInsight(ContentEntryContentTypeEnum.WEEKLY_REVIEW),
      UserService._userHasSavingsHoldingsOrPendingSavingsTopups(user),
      // Chained promise to avoid unnecessary calls
      SavingsProductService.getSavingsProduct(ConfigUtil.getPromotionalSavingsProduct(user.companyEntity), {
        currentTicker: true
      }).then((promotionalSavingsProduct) =>
        SavingsProductService.getSavingsProductFeeDetails(
          promotionalSavingsProduct,
          ConfigUtil.getDefaultUserLocale(user.residencyCountry)
        )
      ),
      UserService._showUnlockFreeShareBanner(user)
    ])) as BannerPrerequisitesType;
  }

  private static _getSavingsPromotionBanners(
    user: UserDocument,
    userHasSavings: boolean
  ): bannersConfig.BannerConfigType[] {
    if (userHasSavings || usersConfig.APPLE_DEMO_EMAILS.includes(user.email)) return [];

    /**
     * For now we only have one banner for savings promotion,
     * and for that we skip the rotational logic and just return the banner every other week.
     *
     * If more savings promotional banners are added, we can use the rotational logic instead.
     */
    const earnInterestOnYourMoneyBanner = UserService._getBannerById(user, BannerEnum.EarnInterestOnYourMoney);
    if (earnInterestOnYourMoneyBanner.active && DateTime.fromMillis(Date.now()).weekNumber % 2 === 0) {
      return [earnInterestOnYourMoneyBanner];
    }

    return [];
  }

  private static _getInvestmentPromotionBanners(
    user: UserDocument,
    showRepeatingInvestmentBanner: boolean
  ): bannersConfig.BannerConfigType[] {
    const repeatingInvestmentBanners = UserService._getBanners(user).filter(
      (banner) => banner.category === CategoryEnum.INVESTMENT_PROMOTION && banner.active
    );

    let filteredBanners = UserService._getBannerSampleOfRotationalCategory(
      CategoryEnum.INVESTMENT_PROMOTION,
      repeatingInvestmentBanners
    );

    // if user has repeating automation, we filter out repeating investment related banners
    if (!showRepeatingInvestmentBanner) {
      filteredBanners = filteredBanners.filter(
        (banner) => banner.id !== BannerEnum.WhySetUpMonthlyInvestment && banner.id !== BannerEnum.WhatIsDCA
      );
    }
    return filteredBanners;
  }

  private static _getFallbackBannerPrompts(user: UserDocument): BannerPromptType[] {
    const earnFreeSharesBanner = UserService._getBannerById(user, BannerEnum.EarnFreeShares);
    const newToInvestingBanner = UserService._getBannerById(user, BannerEnum.NewToInvesting);
    const investingInEtfsBanner = UserService._getBannerById(user, BannerEnum.InvestingInETFs);

    return [
      {
        order: 0,
        bannerId: earnFreeSharesBanner.id,
        data: earnFreeSharesBanner.data
      },
      {
        order: 1,
        bannerId: newToInvestingBanner.id,
        data: newToInvestingBanner.data
      },
      {
        order: 2,
        bannerId: investingInEtfsBanner.id,
        data: investingInEtfsBanner.data
      }
    ];
  }

  /**
   * Passport verification checks as part of KYC check two things:
   * 1. That the user has entered a full name that matches the one on their provided document.
   * 2. That, if the user is Greek, are verifying with the EU entity and have provided an national ID
   * card, that ID card is of the new format.
   *
   * @param user
   * @private
   */
  private static async _runKycProviderPassportVerificationCheck(
    user: UserDocument
  ): Promise<{ checkWasProcessed: boolean; updatedUser: UserDocument }> {
    await user.populate([UserPopulationFieldsEnum.KYC_OPERATION, UserPopulationFieldsEnum.ADDRESSES]);

    /**
     * If user hasn't submitted all required info, we cannot run the passport check.
     * For the passport check to matter the passport details in our DB must be final, meaning the user cannot edit them anymore.
     * That's why depend on submittedRequiredInfo.
     */
    if (!user.submittedRequiredInfo) {
      logger.warn("Cannot run KYC passport check because user hasn't submitted all required info yet.", {
        module: "UserService",
        method: "_runKycProviderPassportVerificationCheck"
      });
      return {
        checkWasProcessed: false,
        updatedUser: user
      };
    }

    /**
     * Edge case: Probably unrealistic but if a user at this point does not have a kyc operation
     * we want to make him KYC failed, and check this manually
     */
    const kycOperation = user.kycOperation;
    if (!kycOperation) {
      logger.warn(`Could not retrieve KYC operation for ${user.id} user.`, {
        module: "UserService",
        method: "_runKycProviderPassportVerificationCheck"
      });
      await UserService.setFailedKycStatus(user);
      const updatedUser = await User.findByIdAndUpdate(
        user.id,
        { isPassportVerified: false },
        { runValidators: true, new: true }
      );
      return {
        checkWasProcessed: true,
        updatedUser
      };
    }

    const { passportDetails } = await ProviderService.getKycService(user.companyEntity).retrieveApplicant(user.id);

    const firstNameMatchesExtractedData =
      user.firstName && user.firstName.toLowerCase() === passportDetails.firstName?.toLowerCase();
    const lastNameMatchesExtractedData =
      user.lastName && user.lastName.toLowerCase() === passportDetails.lastName?.toLowerCase();
    const dateOfBirthMatchesExtractedData =
      user.dateOfBirth &&
      passportDetails.dateOfBirth &&
      DateUtil.datesAreEqual(user.dateOfBirth, passportDetails.dateOfBirth);

    const isPassportMatchingKycProvider =
      firstNameMatchesExtractedData && lastNameMatchesExtractedData && dateOfBirthMatchesExtractedData;
    const userUsedOldGreekId = UserService._didUserUseOldGreekId(user, passportDetails);

    // If this is the first time we're checking the passport, and the user used an old Greek ID, emit the event
    if (user.isPassportVerified === undefined && userUsedOldGreekId) {
      eventEmitter.emit(events.user.oldGreekIdDetected.eventId, user, {
        documentNumber: passportDetails.idDocs?.[0]?.number
      });
    }

    const isPassportVerified =
      isPassportMatchingKycProvider ||
      EXEMPT_EMAIL_PREFIXES_FROM_PASSPORT_CHECK.some((prefix) => user.email.startsWith(prefix));
    if (!isPassportVerified) {
      await UserService.setFailedKycStatus(user);
    }

    const updatedUser = await User.findByIdAndUpdate(
      user.id,
      { isPassportVerified },
      { runValidators: true, new: true }
    );

    return {
      checkWasProcessed: true,
      updatedUser
    };
  }

  private static _didUserUseOldGreekId(user: UserDocument, passportDetails: PassportDetails) {
    return (
      user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE &&
      passportDetails?.idDocs?.[0]?.country === "GR" &&
      passportDetails?.idDocs?.[0]?.idDocType === IdDocTypeEnum.ID_CARD &&
      !passportDetails?.idDocs?.[0]?.mrzLine1
    );
  }

  /**
   * @summary
   * This method returns true if user has savings holdings or has a pending savings topup
   */
  private static async _userHasSavingsHoldingsOrPendingSavingsTopups(user: UserDocument): Promise<boolean> {
    const [pendingSavingsTopups] = await Promise.all([
      Transaction.find({
        owner: user.id,
        status: { $in: ["Pending", "PendingDeposit"] },
        category: "SavingsTopupTransaction"
      }),
      DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.PORTFOLIOS)
    ]);

    const userHoldsSavings = Array.from(user.portfolios?.[0]?.savings ?? []).some(
      ([, holding]) => holding.amount > 0
    );

    return userHoldsSavings || pendingSavingsTopups.length > 0;
  }

  private static async _getUserWithNoSnapshotsDailySummariesView(user: UserDocument): Promise<DailySummariesType> {
    const now = new Date(Date.now());
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    const [latestSundownDigest, indexPrices, portfolio] = await Promise.all([
      SundownDigestService.getLatestSundownDigest(),
      IndexPriceUtil.getCachedCurrentIndexPrices(),
      PortfolioService.getGeneralInvestmentPortfolio(user, true)
    ]);

    const { aggregatedSavingsAmount } = await SavingsProductService.getAggregatedSavingsAmount(
      portfolio,
      DEFAULT_SAVINGS_PRODUCT_CONFIG[user.companyEntity]
    );

    const cashValue = portfolio.cash[user.currency].available;
    const savingsValue = Decimal.div(aggregatedSavingsAmount, 100).toNumber();
    const holdingsValue = portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset));
    const totalValue = new Decimal(cashValue).add(savingsValue).add(holdingsValue).toNumber();

    // Check if user has no activity (no cash, savings, holdings, or investments in progress)
    const hasNoActivity =
      cashValue === 0 &&
      savingsValue === 0 &&
      holdingsValue === 0 &&
      user.portfolioConversionStatus === "notStarted";

    const data = [
      {
        ...(hasNoActivity && { isUninvested: true }),
        timestamp: now.getTime(),
        shortDateLabel: UserService._getDailySummaryShortDateLabel(now),
        // In the uninvested summaries, the full date label is only used by sundown digest full page. Hence we use
        // the sundown digest date to construct the date label.
        fullDateLabel: UserService._getDailySummaryFullDateLabel(latestSundownDigest?.date ?? now),
        chartLabel: DateUtil.formatDateToDAYDD(now, { separatorCharacter: "\n" }),
        portfolio: {
          cash: {
            key: "cash",
            value: cashValue,
            chartValue: cashValue,
            displayValue: CurrencyUtil.formatCurrency(cashValue, user.currency, locale)
          },
          savings: {
            key: "savings",
            value: savingsValue,
            chartValue: savingsValue,
            displayValue: CurrencyUtil.formatCurrency(savingsValue, user.currency, locale)
          },
          holdings: {
            key: "holdings",
            value: holdingsValue,
            chartValue: holdingsValue,
            displayValue: CurrencyUtil.formatCurrency(holdingsValue, user.currency, locale)
          },
          total: {
            key: "total",
            value: totalValue,
            chartValue: totalValue,
            displayValue: CurrencyUtil.formatCurrency(totalValue, user.currency, locale)
          }
        },
        sundownDigest: latestSundownDigest?.content,
        marketSummary: UserService._parseSundownDigestToMarketSummary(latestSundownDigest, locale),
        todayMarkets: UserService._formatTodayMarkets(
          Object.entries(indexPrices).map(([key, { dailyReturnPercentage }]) => {
            return {
              label: INDEX_CONFIG_GLOBAL[key as indexesConfig.IndexType].simpleName,
              returns: UserService._getReturnsBasedOnDailyReturnPercentage(dailyReturnPercentage, locale)
            };
          }),
          locale,
          { defaultToZeroReturns: true }
        )
      } as DailySummaryType
    ];

    // In the beginning and end of the data points, we include data points that will only be displayed as chart
    // labels without the ability to be viewed/selected.
    const { startChartLabelOnlyPoints, endChartLabelOnlyPoints } = UserService._getChartLabelOnlyDataPoints(
      new Date(Date.now())
    );

    return {
      data: hasNoActivity
        ? data
        : (startChartLabelOnlyPoints as DailySummaryType[]).concat(data).concat(endChartLabelOnlyPoints),
      maxValueDifferences: DEFAULT_MAX_VALUE_DIFFERENCES
    };
  }

  /**
   * Returns the daily summary for users with snapshots (i.e. any activity). This includes:
   * 1. Invested users
   * 2. Users with cash
   * 3. Users with savings
   * 4. Fully withdrawn users
   *
   * @param user
   * @param snapshots
   * @private
   */
  private static async _getUserWithSnapshotsDailySummariesView(user: UserDocument): Promise<DailySummariesType> {
    const startDate = DateUtil.getDateOfDaysAgo(new Date(Date.now()), DAYS_TO_INCLUDE_IN_DAILY_SUMMARIES);
    const { end: endDate } = DateUtil.getStartAndEndOfDay(DateUtil.getYesterday());

    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.PORTFOLIOS);
    const commonIdToSavingsProductDict = await SavingsProductService.getSavingsProductsDict("commonId", false);

    const [snapshots, sundownDigests, indexPrices, yesterdayDailySummary, todayDailySummary, savingsTickers] =
      await Promise.all([
        DailySummarySnapshotService.getDailySummarySnapshots(
          { owner: user.id, date: { startDate, endDate } },
          "+date"
        ),
        SundownDigestService.getSundownDigests({ date: { startDate, endDate } }),
        IndexPriceService.getIndexPrices({ date: { startDate, endDate } }, "-date"),
        UserService._getDailySummaryForYesterday(user, commonIdToSavingsProductDict),
        UserService._getDailySummaryForToday(user, commonIdToSavingsProductDict),
        DailyTickerService.getPortfolioSavingsTickers(
          user.portfolios[0],
          commonIdToSavingsProductDict[DEFAULT_SAVINGS_PRODUCT_CONFIG[user.companyEntity]],
          { startDate, endDate }
        )
      ]);

    // Create maps of date strings to other components for quick lookup.
    const sundownDigestMap: Map<string, SundownDigestDocument> = new Map(
      sundownDigests.map((digest) => [DateUtil.formatDateToYYYYMMDD(digest.date), digest])
    );
    const savingsTickersMap: Map<string, DailyPortfolioSavingsTickerDocument> = new Map(
      savingsTickers.map((ticker) => [DateUtil.formatDateToYYYYMMDD(ticker.date), ticker])
    );
    const indexPriceMap: Map<string, TodayMarketsType> = UserService._groupIndexPricesByDateAndId(
      indexPrices,
      locale
    );

    const historicalDataPoints = snapshots
      .filter((snapshot) => !!snapshot) // Filter out null snapshots
      .map((snapshot) => {
        const snapshotDate = DateUtil.formatDateToYYYYMMDD(snapshot.date);
        const shouldShowSavingsDetails = DateUtil.isSameOrPastDate(
          DATE_TO_START_SHOWING_SUMMARY_SAVINGS_DETAILS,
          snapshot.date
        );

        const { cash, holdings, savings, total } = snapshot.portfolio;
        const sundownDigest = sundownDigestMap.get(snapshotDate);

        const performers = UserService._formatSnapshotInvestmentsPerformance(holdings, user, locale);

        return {
          timestamp: snapshot.date.getTime(),
          shortDateLabel: UserService._getDailySummaryShortDateLabel(snapshot.date),
          fullDateLabel: UserService._getDailySummaryFullDateLabel(snapshot.date),
          chartLabel: DateUtil.formatDateToDAYDD(snapshot.date, { separatorCharacter: "\n" }),
          seeAllTopPerformersEnabled: !snapshot.isBackfilled,
          portfolio: {
            cash: {
              key: "cash",
              value: cash.value.amount,
              chartValue: cash.value.amount,
              displayValue: CurrencyUtil.formatCurrency(cash.value.amount, cash.value.currency, locale)
            },
            savings: {
              key: "savings",
              value: savings.value.amount,
              chartValue: savings.value.amount,
              displayValue: CurrencyUtil.formatCurrency(savings.value.amount, savings.value.currency, locale),
              ...(shouldShowSavingsDetails && {
                unrealisedMonthlyInterest: CurrencyUtil.formatCurrency(
                  savings.unrealisedInterest?.amount ?? 0,
                  savings.unrealisedInterest?.currency ?? user.currency,
                  locale
                ),
                dailyInterest: UserService._formatDailySummarySavingsRate(
                  new Decimal(savingsTickersMap.get(snapshotDate)?.dailyAccrual ?? 0)
                    .div(100)
                    .toDecimalPlaces(2)
                    .toNumber(),
                  savingsTickersMap.get(snapshotDate)?.currency ?? user.currency,
                  locale
                )
              })
            },
            holdings: {
              key: "holdings",
              value: holdings.value.amount,
              chartValue: holdings.value.amount,
              displayValue: CurrencyUtil.formatCurrency(holdings.value.amount, holdings.value.currency, locale),
              ...UserService._getFormattedReturnsAndUpBy(
                holdings.dailyReturnPercentage,
                holdings.dailyUpBy,
                user.currency,
                locale
              )
            },
            total: {
              key: "total",
              value: total.value.amount,
              chartValue: total.value.amount,
              displayValue: CurrencyUtil.formatCurrency(total.value.amount, total.value.currency, locale)
            }
          },
          sundownDigest: sundownDigest?.content,
          marketSummary: UserService._parseSundownDigestToMarketSummary(sundownDigest, locale),
          todayMarkets: UserService._formatTodayMarkets(indexPriceMap.get(snapshotDate), locale, {
            defaultToZeroReturns: false
          }),
          sentimentScore:
            snapshot.sentimentScore?.total && UserService._formatSentimentScore(snapshot.sentimentScore),
          performers: performers
        } as DailySummaryType;
      });

    // If there is no snapshot for yesterday, add the created one
    if (!DateUtil.isYesterday(snapshots.at(-1).date) && yesterdayDailySummary) {
      // Fill in the missing fields for yesterday's summary.
      const snapshotDate = DateUtil.formatDateToYYYYMMDD(new Date(yesterdayDailySummary.timestamp));
      yesterdayDailySummary.sundownDigest = sundownDigestMap.get(snapshotDate)?.content;
      yesterdayDailySummary.marketSummary = UserService._parseSundownDigestToMarketSummary(
        sundownDigestMap.get(snapshotDate),
        locale
      );
      yesterdayDailySummary.todayMarkets = UserService._formatTodayMarkets(
        indexPriceMap.get(snapshotDate),
        locale,
        { defaultToZeroReturns: false }
      );

      historicalDataPoints.push(yesterdayDailySummary);
    }

    if (todayDailySummary) {
      historicalDataPoints.push(todayDailySummary);
    }

    // In the beginning and end of the data points, we include data points that will only be displayed as chart
    // labels without the ability to be viewed/selected.
    const { startChartLabelOnlyPoints, endChartLabelOnlyPoints } = UserService._getChartLabelOnlyDataPoints(
      new Date(historicalDataPoints?.[0].timestamp)
    );

    const allDataPoints = (startChartLabelOnlyPoints as DailySummaryType[])
      .concat(historicalDataPoints)
      .concat(endChartLabelOnlyPoints);

    const maxValueDifferences = UserService._calculateMaxValueDifferences(
      historicalDataPoints as DailySummaryWithDataType[]
    );

    return {
      data: allDataPoints,
      maxValueDifferences
    };
  }

  /**
   * Returns the daily summary for Yesterday using the portfolio's CURRENT ticker

   * @param user
   * @param savingsProductDict
   * @private
   */
  private static async _getDailySummaryForYesterday(
    user: UserDocument,
    savingsProductDict: { [key: string]: SavingsProductDocument }
  ): Promise<DailySummaryWithDataType> {
    const { start, end } = DateUtil.getStartAndEndOfYesterday();
    if (DateUtil.isWeekend(start)) {
      return null;
    }

    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);
    const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(user, true);

    const [indexPrices, userSavings, sentimentScore, dividendsForReturns, rewardsForReturns] = await Promise.all([
      IndexPriceUtil.getCachedCurrentIndexPrices(),
      SavingsProductService.getUserSavings(user.id, start),
      DailySummarySnapshotService.calculatePortfolioSentimentScore(portfolio),
      TransactionService.getDividendTransactionsForReturnsUpBy(user.id, start),
      RewardService.getSettledRewards(user.id)
    ]);

    const transactions = {
      dividendTransactions: dividendsForReturns,
      rewards: rewardsForReturns.filter((reward) => reward.updatedAt <= end)
    };

    // We only calculate MWRR and up-by values if user has holdings.
    let mwrr, upBy: Record<TenorEnum.TODAY, number>;
    if (portfolio.holdings?.length) {
      [mwrr, upBy] = await Promise.all([
        PortfolioService.getPortfolioMWRR(portfolio, transactions, TenorEnum.TODAY, start),
        PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY, start)
      ]);
    }

    const cashValue = portfolio.cash[user.currency]?.available ?? 0;
    const savingsValue = userSavings?.[0].savingsAmount ?? 0;
    const holdingsValue = portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset));
    const totalValue = new Decimal(cashValue).add(savingsValue).add(holdingsValue).toNumber();

    const [yesterdayPortfolioSavingsTickers, estimatedTodayDailySavings] = await Promise.all([
      DailyTickerService.getPortfolioSavingsTickers(
        portfolio,
        savingsProductDict[userSavings?.[0]?.savingsProductId],
        {
          startDate: DateUtil.getStartAndEndOfYesterday().start,
          endDate: DateUtil.getStartAndEndOfYesterday().end
        }
      ),
      DailyTickerService.estimateTodayPortfolioSavings(
        user,
        {
          amount: Decimal.mul(savingsValue, 100).toNumber(),
          currency: userSavings?.[0]?.currency
        },
        savingsProductDict[userSavings?.[0]?.savingsProductId]
      )
    ]);

    const estimatedDailyAccrual =
      estimatedTodayDailySavings &&
      Decimal.div(estimatedTodayDailySavings.dailyAccrual, 100).toDecimalPlaces(2).toNumber();
    let estimatedUnrealizedInterest = userSavings?.[0]?.unrealisedInterest ?? 0;

    if (estimatedDailyAccrual) {
      const hasYesterdayTicker = yesterdayPortfolioSavingsTickers?.length > 0;

      if (!hasYesterdayTicker) {
        // For yesterday: only add accrual if we don't have yesterday's ticker
        estimatedUnrealizedInterest = Decimal.add(estimatedUnrealizedInterest, estimatedDailyAccrual).toNumber();
      }
    }

    const assetAllocation = PortfolioUtil.mapHoldingsToAllocationFormat(user.currency, portfolio.holdings).assets;

    const performers = UserService._formatInvestmentsPerformanceForDate(
      portfolio,
      user,
      locale,
      assetAllocation,
      start
    );

    return {
      timestamp: DateUtil.getDateOfDaysAgo(new Date(Date.now()), 1).getTime(),
      chartLabel: DateUtil.formatDateToDAYDD(start, { separatorCharacter: "\n" }),
      fullDateLabel: "Yesterday",
      shortDateLabel: "Yesterday",
      seeAllTopPerformersEnabled: true,
      portfolio: {
        cash: {
          key: "cash",
          value: cashValue,
          chartValue: cashValue,
          displayValue: CurrencyUtil.formatCurrency(cashValue, user.currency, locale)
        },
        savings: {
          key: "savings",
          value: savingsValue,
          chartValue: savingsValue,
          displayValue: CurrencyUtil.formatCurrency(savingsValue, user.currency, locale),
          estimated: true,
          ...(estimatedDailyAccrual && {
            unrealisedMonthlyInterest: CurrencyUtil.formatCurrency(
              estimatedUnrealizedInterest,
              user.currency,
              locale
            ),
            dailyInterest: UserService._formatDailySummarySavingsRate(estimatedDailyAccrual, user.currency, locale)
          })
        },
        holdings: {
          key: "holdings",
          value: holdingsValue,
          chartValue: holdingsValue,
          displayValue: CurrencyUtil.formatCurrency(holdingsValue, user.currency, locale),
          ...UserService._getFormattedReturnsAndUpBy(
            mwrr?.[TenorEnum.TODAY],
            upBy?.[TenorEnum.TODAY],
            user.currency,
            locale
          )
        },
        total: {
          key: "total",
          value: totalValue,
          chartValue: totalValue,
          displayValue: CurrencyUtil.formatCurrency(totalValue, user.currency, locale)
        }
      },
      sentimentScore: sentimentScore && UserService._formatSentimentScore(sentimentScore),
      todayMarkets: UserService._formatTodayMarkets(
        Object.entries(indexPrices).map(([key, { dailyReturnPercentage }]) => {
          return {
            label: INDEX_CONFIG_GLOBAL[key as indexesConfig.IndexType].simpleName,
            returns: UserService._getReturnsBasedOnDailyReturnPercentage(dailyReturnPercentage, locale)
          };
        }),
        locale,
        { defaultToZeroReturns: true }
      ),
      performers: performers
    };
  }

  /**
   * Returns the daily summary for Today using the portfolio's CURRENT ticker
   * @param user
   * @param savingsProductDict
   * @private
   */
  public static async _getDailySummaryForToday(
    user: UserDocument,
    savingsProductDict: { [key: string]: SavingsProductDocument }
  ): Promise<DailySummaryWithDataType> {
    const { start, end } = DateUtil.getStartAndEndOfToday();
    if (DateUtil.isWeekend(start)) {
      return null;
    }

    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);
    const portfolio = await PortfolioService.getGeneralInvestmentPortfolio(user, true);

    const [indexPrices, userSavings, sentimentScore, dividendsForReturns, rewardsForReturns] = await Promise.all([
      IndexPriceUtil.getCachedCurrentIndexPrices(),
      SavingsProductService.getUserSavings(user.id, start),
      DailySummarySnapshotService.calculatePortfolioSentimentScore(portfolio),
      TransactionService.getDividendTransactionsForReturnsUpBy(user.id, start),
      RewardService.getSettledRewards(user.id)
    ]);

    const transactions = {
      dividendTransactions: dividendsForReturns,
      rewards: rewardsForReturns.filter((reward) => reward.updatedAt <= end)
    };

    // We only calculate MWRR and up-by values if user has holdings.
    let mwrr, upBy: Record<TenorEnum.TODAY, number>;
    if (portfolio.holdings?.length) {
      [mwrr, upBy] = await Promise.all([
        PortfolioService.getPortfolioMWRR(portfolio, transactions, TenorEnum.TODAY, start),
        PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY, start)
      ]);
    }

    const cashValue = portfolio.cash[user.currency]?.available ?? 0;
    const savingsValue = userSavings?.[0].savingsAmount ?? 0;
    const holdingsValue = portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset));
    const totalValue = new Decimal(cashValue).add(savingsValue).add(holdingsValue).toNumber();

    const [yesterdayPortfolioSavingsTickers, estimatedTodayDailySavings] = await Promise.all([
      DailyTickerService.getPortfolioSavingsTickers(
        portfolio,
        savingsProductDict[userSavings?.[0]?.savingsProductId],
        {
          startDate: DateUtil.getStartAndEndOfYesterday().start,
          endDate: DateUtil.getStartAndEndOfYesterday().end
        }
      ),
      DailyTickerService.estimateTodayPortfolioSavings(
        user,
        {
          amount: Decimal.mul(savingsValue, 100).toNumber(),
          currency: userSavings?.[0]?.currency
        },
        savingsProductDict[userSavings?.[0]?.savingsProductId]
      )
    ]);

    const estimatedDailyAccrual =
      estimatedTodayDailySavings &&
      Decimal.div(estimatedTodayDailySavings.dailyAccrual, 100).toDecimalPlaces(2).toNumber();
    let estimatedUnrealizedInterest = userSavings?.[0]?.unrealisedInterest ?? 0;

    if (estimatedDailyAccrual) {
      const hasYesterdayTicker = yesterdayPortfolioSavingsTickers?.length > 0;

      // For today: add 1 day's accrual, or 2 if we don't have yesterday's data
      const multiplier = hasYesterdayTicker ? 1 : 2;
      estimatedUnrealizedInterest = Decimal.add(
        estimatedUnrealizedInterest,
        estimatedDailyAccrual * multiplier
      ).toNumber();
    }

    const assetAllocation = PortfolioUtil.mapHoldingsToAllocationFormat(user.currency, portfolio.holdings).assets;

    const performers = UserService._formatInvestmentsPerformanceForDate(
      portfolio,
      user,
      locale,
      assetAllocation,
      start
    );

    return {
      timestamp: new Date(Date.now()).getTime(),
      chartLabel: DateUtil.formatDateToDAYDD(start, { separatorCharacter: "\n" }),
      fullDateLabel: "Today",
      shortDateLabel: "Today",
      seeAllTopPerformersEnabled: true,
      portfolio: {
        cash: {
          key: "cash",
          value: cashValue,
          chartValue: cashValue,
          displayValue: CurrencyUtil.formatCurrency(cashValue, user.currency, locale)
        },
        savings: {
          key: "savings",
          value: savingsValue,
          chartValue: savingsValue,
          displayValue: CurrencyUtil.formatCurrency(savingsValue, user.currency, locale),
          estimated: true,
          ...(estimatedDailyAccrual && {
            unrealisedMonthlyInterest: CurrencyUtil.formatCurrency(
              estimatedUnrealizedInterest,
              user.currency,
              locale
            ),
            dailyInterest: UserService._formatDailySummarySavingsRate(estimatedDailyAccrual, user.currency, locale)
          })
        },
        holdings: {
          key: "holdings",
          value: holdingsValue,
          chartValue: holdingsValue,
          displayValue: CurrencyUtil.formatCurrency(holdingsValue, user.currency, locale),
          ...UserService._getFormattedReturnsAndUpBy(
            mwrr?.[TenorEnum.TODAY],
            upBy?.[TenorEnum.TODAY],
            user.currency,
            locale
          )
        },
        total: {
          key: "total",
          value: totalValue,
          chartValue: totalValue,
          displayValue: CurrencyUtil.formatCurrency(totalValue, user.currency, locale)
        }
      },
      sentimentScore: sentimentScore && UserService._formatSentimentScore(sentimentScore),
      todayMarkets: UserService._formatTodayMarkets(
        Object.entries(indexPrices).map(([key, { dailyReturnPercentage }]) => {
          return {
            label: INDEX_CONFIG_GLOBAL[key as indexesConfig.IndexType].simpleName,
            returns: UserService._getReturnsBasedOnDailyReturnPercentage(dailyReturnPercentage, locale)
          };
        }),
        locale,
        { defaultToZeroReturns: true }
      ),
      performers: performers
    };
  }

  private static _formatInvestmentsPerformanceForDate(
    portfolio: PortfolioDocument,
    user: UserDocument,
    locale: localeConfig.LocaleType,
    assetAllocation: Record<string, number>,
    date: Date
  ): PerformersType {
    const allInvestments = portfolio.holdings
      .map((holding) => {
        return {
          holding,
          todayTicker:
            DateUtil.datesAreEqual(holding.asset.currentTicker.timestamp, date) && holding.asset.currentTicker
        };
      })
      .sort(
        (a, b) =>
          (b.todayTicker.dailyReturnPercentage ?? Number.NEGATIVE_INFINITY) -
          (a.todayTicker.dailyReturnPercentage ?? Number.NEGATIVE_INFINITY)
      )
      .map(({ holding, todayTicker }) => {
        return {
          assetId: holding.assetCommonId,
          value: CurrencyUtil.formatCurrency(
            Decimal.mul(holding.asset.currentTicker.getPrice(user.currency), holding.quantity).toNumber(),
            user.currency,
            locale
          ),
          weight: formatPercentage(Decimal.div(assetAllocation[holding.assetCommonId], 100).toNumber(), locale),
          ...(todayTicker &&
            UserService._getReturnsBasedOnDailyReturnPercentage(todayTicker.dailyReturnPercentage, locale))
        };
      });

    const topPerformers = allInvestments
      .filter((investment) => "upBy" in investment && investment.upBy !== "0.00%")
      .splice(0, 3);
    const worstPerformers = allInvestments
      .filter((investment) => "downBy" in investment)
      .reverse()
      .splice(0, 3);

    return {
      all: allInvestments,
      best: topPerformers,
      worst: worstPerformers
    };
  }

  private static _formatSnapshotInvestmentsPerformance(
    holdings: HoldingsSnapshotPortfolioComponentType,
    user: UserDocument,
    locale: localeConfig.LocaleType
  ): PerformersType {
    const allInvestments = holdings.assets
      .sort((a, b) => b.dailyReturnPercentage - a.dailyReturnPercentage)
      .map((asset) => {
        return {
          assetId: asset.assetId,
          value:
            asset.latestPrice?.amount &&
            CurrencyUtil.formatCurrency(
              Decimal.mul(asset.latestPrice.amount, asset.quantity).toNumber(),
              user.currency,
              locale
            ),
          weight: asset.holdingWeightPercentage && formatPercentage(asset.holdingWeightPercentage, locale),
          ...UserService._getReturnsBasedOnDailyReturnPercentage(asset.dailyReturnPercentage, locale)
        };
      });

    const topPerformers = allInvestments
      .filter((investment) => "upBy" in investment && investment.upBy !== "0.00%")
      .slice(0, 3);
    const worstPerformers = allInvestments
      .filter((investment) => "downBy" in investment)
      .reverse()
      .splice(0, 3);

    return {
      all: allInvestments,
      best: topPerformers,
      worst: worstPerformers
    };
  }

  private static _formatSentimentScore(sentimentScore: SentimentScoreType): SentimentScoreWithLabelsType {
    return Object.fromEntries(
      Object.entries(sentimentScore)
        .filter(([, score]) => ![undefined, null].includes(score))
        .map(([key, score]) => {
          return [
            key,
            {
              score: Decimal.mul(score, 100).floor().toNumber(),
              label: UserService._getScoreLabel(score)
            }
          ];
        })
    ) as SentimentScoreWithLabelsType;
  }

  private static _getScoreLabel(score: number): SentimentLabelEnum {
    if (score >= 0.667) {
      return SentimentLabelEnum.OPTIMAL;
    } else if (score >= 0.333) {
      return SentimentLabelEnum.SUBOPTIMAL;
    }
    return SentimentLabelEnum.UNDERPERFORMING;
  }

  private static _getChartLabelOnlyDataPoints(startDate: Date): {
    startChartLabelOnlyPoints: ChartLabelOnlyType[];
    endChartLabelOnlyPoints: ChartLabelOnlyType[];
  } {
    return {
      startChartLabelOnlyPoints: DateUtil.getWeekDaysBetween(
        DateUtil.getDateNWorkDaysAgo(startDate, CHART_LABEL_ONLY_POINTS_TO_INCLUDE),
        startDate,
        { includingStart: true, includingEnd: false }
      )
        .map((date) => {
          return {
            timestamp: date.getTime(),
            isOnlyForChartLabel: true,
            chartLabel: DateUtil.formatDateToDAYDD(date, { separatorCharacter: "\n" })
          };
        })
        // Get the last elements of the array in case the above has returned more dates
        .slice(-CHART_LABEL_ONLY_POINTS_TO_INCLUDE),
      endChartLabelOnlyPoints: DateUtil.getWeekDaysBetween(
        new Date(Date.now()),
        DateUtil.getDateAfterNthUKWorkDays(new Date(Date.now()), CHART_LABEL_ONLY_POINTS_TO_INCLUDE),
        { includingStart: false, includingEnd: true }
      )
        .map((date) => {
          return {
            timestamp: date.getTime(),
            isOnlyForChartLabel: true,
            chartLabel: DateUtil.formatDateToDAYDD(date, { separatorCharacter: "\n" })
          };
        })
        // Get the first elements of the array in case the above has returned more dates
        .slice(0, CHART_LABEL_ONLY_POINTS_TO_INCLUDE)
    };
  }

  private static _getReturnsBasedOnDailyReturnPercentage(
    dailyReturnPercentage: number,
    locale: localeConfig.LocaleType
  ): ReturnsType {
    return dailyReturnPercentage >= 0
      ? { upBy: formatPercentage(dailyReturnPercentage, locale, 2, 2, "never") }
      : { downBy: formatPercentage(dailyReturnPercentage, locale, 2, 2, "never") };
  }

  private static _getFormattedReturnsAndUpBy(
    dailyReturnPercentage: number,
    upBy: number,
    currency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): ReturnsType {
    if (!dailyReturnPercentage && !upBy) {
      return;
    }

    if (!dailyReturnPercentage && upBy) {
      return upBy > 0
        ? {
            upBy: `${CurrencyUtil.formatCurrency(upBy, currency, locale)}`
          }
        : {
            downBy: `${CurrencyUtil.formatCurrency(upBy, currency, locale, true, "never")}`
          };
    }

    return dailyReturnPercentage > 0
      ? {
          upBy: `${CurrencyUtil.formatCurrency(upBy, currency, locale)} · ${formatPercentage(dailyReturnPercentage, locale)}`
        }
      : {
          downBy: `${CurrencyUtil.formatCurrency(upBy, currency, locale, true, "never")} · ${formatPercentage(dailyReturnPercentage, locale, 2, 2, "never")}`
        };
  }

  private static _getDailySummaryShortDateLabel(date: Date): string {
    if (DateUtil.isToday(date)) {
      return "Today";
    } else if (DateUtil.isYesterday(date)) {
      return "Yesterday";
    } else return DateUtil.formatDateToDAYDDMON(date);
  }

  private static _getDailySummaryFullDateLabel(date: Date): string {
    if (DateUtil.isToday(date)) {
      return "Today";
    } else if (DateUtil.isYesterday(date)) {
      return "Yesterday";
    } else return DateUtil.formatDateToDAYDDMONYYYY(date);
  }

  private static _groupIndexPricesByDateAndId(
    sortedIndexPrices: IndexPriceDocument[],
    locale: localeConfig.LocaleType
  ): Map<string, TodayMarketsType> {
    const indexPricesByDate: Record<string, TodayMarketsType> = {};

    sortedIndexPrices
      .filter((indexPrice) => !!INDEX_CONFIG_GLOBAL[indexPrice.index]) // Only keep indexes we currently support
      .forEach((indexPrice) => {
        const { index, date } = indexPrice;
        const formattedDate = DateUtil.formatDateToYYYYMMDD(date);

        const simpleName = INDEX_CONFIG_GLOBAL[index].simpleName;

        if (!indexPricesByDate[formattedDate]) {
          indexPricesByDate[formattedDate] = [];
        }

        if (!indexPricesByDate[formattedDate].find((entry) => entry.label === simpleName)) {
          indexPricesByDate[formattedDate].push({
            label: simpleName,
            returns: UserService._getReturnsBasedOnDailyReturnPercentage(indexPrice.dailyReturnPercentage, locale)
          });
        }
      });

    return new Map(Object.entries(indexPricesByDate));
  }

  /**
   * This method updates the today's markets in two ways:
   * 1. For any indexes that have not been added in today's markets, we still include them with 0-returns if defaultToZeroReturns is true.
   * 2. We sort the list of markets based on the INDEXES_CONFIG.
   */
  private static _formatTodayMarkets(
    todayMarkets: TodayMarketsType,
    locale: localeConfig.LocaleType,
    options: { defaultToZeroReturns: boolean }
  ): TodayMarketsType {
    return IndexArrayConst.map((index) => INDEX_CONFIG_GLOBAL[index])
      .sort((a, b) => a.order - b.order)
      .map(({ simpleName }) => {
        const existingMarket = todayMarkets?.find((market) => market.label === simpleName);

        if (!existingMarket && options?.defaultToZeroReturns) {
          return {
            label: simpleName,
            returns: { upBy: formatPercentage(0, locale) }
          };
        }

        return existingMarket;
      })
      .filter((todayMarket) => !!todayMarket);
  }

  /**
   * Calculates the maximum difference between adjacent data points (up to N points apart).
   *
   * If all the data points have the same value (e.g. all values are 0) or there are not enough points, we return a default value.
   *
   * @param values Array of numeric values
   * @param adjacencyRange Maximum number of points to consider adjacent (default: 3)
   * @returns Maximum difference found
   */
  private static _calculateMaxDifference(values: number[], adjacencyRange: number = 3): number {
    if (!values || values.length < 2) {
      return DEFAULT_MAX_VALUE_DIFFERENCE;
    }

    let maxDiff = 0;

    for (let i = 0; i < values.length; i++) {
      for (let j = 1; j <= adjacencyRange && i + j < values.length; j++) {
        const diff = Math.abs(values[i + j] - values[i]);
        if (diff > maxDiff) {
          maxDiff = diff;
        }
      }
    }

    if (maxDiff === 0) {
      return DEFAULT_MAX_VALUE_DIFFERENCE;
    }

    return maxDiff;
  }

  /**
   * Calculates maxValueDifferences across all portfolio components
   * @param dailySummaries Array of daily summaries with portfolio data
   * @returns Record with maximum differences for each component
   */
  private static _calculateMaxValueDifferences(
    dailySummaries: DailySummaryType[]
  ): Record<keyof DailySummaryPortfolioType, number> {
    const filteredSummaries = dailySummaries.filter(
      (summary) => !(summary as ChartLabelOnlyType).isOnlyForChartLabel
    ) as DailySummaryWithDataType[];

    // Extract values for each component
    const cashValues = filteredSummaries.map((summary) => summary.portfolio.cash.value);
    const savingsValues = filteredSummaries.map((summary) => summary.portfolio.savings.value);
    const holdingsValues = filteredSummaries.map((summary) => summary.portfolio.holdings.value);
    const totalValues = filteredSummaries.map((summary) => summary.portfolio.total.value);

    // Calculate max differences
    return {
      cash: UserService._calculateMaxDifference(cashValues),
      savings: UserService._calculateMaxDifference(savingsValues),
      holdings: UserService._calculateMaxDifference(holdingsValues),
      total: UserService._calculateMaxDifference(totalValues)
    };
  }

  private static _formatDailySummarySavingsRate(
    amount: number,
    currency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): string {
    const formattedAmount = new Intl.NumberFormat(locale, {
      style: "currency",
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(Math.abs(amount));

    return `+${formattedAmount} / day`;
  }

  /**
   * Parses the sundown digest formatted content into the market summary format
   * @param sundownDigest The sundown digest data
   * @param locale User locale for formatting
   * @returns Formatted market summary or null if no data available
   */
  private static _parseSundownDigestToMarketSummary(
    sundownDigest: SundownDigestDocument,
    locale: localeConfig.LocaleType
  ): MarketSummaryType {
    if (!sundownDigest?.formattedContent) {
      return null;
    }

    const { overview, sections } = sundownDigest.formattedContent;

    return {
      overview,
      sections: sections?.map((section) => {
        let assetReturns = undefined;
        if (section.assetReturnPercentage !== undefined) {
          assetReturns = UserService._getReturnsBasedOnDailyReturnPercentage(
            section.assetReturnPercentage,
            locale
          );
        }

        return {
          title: section.title,
          tickerSymbol: UserService._formatTickerSymbol(section.assetId, section.companyTicker),
          assetReturns,
          content: section.content,
          assetId: section.assetId,
          tag: section.tag
        };
      })
    };
  }

  /**
   * Formats a ticker symbol by prepending the appropriate currency symbol based on the asset's traded currency
   * @param assetId The public asset type ID used to determine the traded currency
   * @param ticker The ticker symbol to format
   * @returns The formatted ticker symbol with currency prefix, or empty string if no ticker provided
   */
  private static _formatTickerSymbol(
    assetId?: publicInvestmentUniverseConfig.PublicAssetType,
    ticker?: string
  ): string {
    if (!ticker) {
      return "";
    }

    if (!assetId) {
      return ticker;
    }

    const tradedCurrency = PUBLIC_ASSET_CONFIG[assetId].tradedCurrency;
    return `${CURRENCY_SYMBOLS[tradedCurrency]}${ticker}`;
  }

  /**
   * Handles free share unlocking logic for WEALTHYHOOD_EUROPE users.
   *
   * Returns true if user:
   * 1. Is not EU whitelisted (whitelisted users have their own reward flow)
   * 2. Has not already received a reward
   * 3. DOES NOT have total deposits >= MIN_DEPOSIT_AMOUNT_FOR_REWARD_ELIGIBILITY (€100)
   *
   * @param user - The WEALTHYHOOD_EUROPE user document to check
   * @returns Promise<boolean> - True if user can unlock free share
   */
  private static async _canUnlockFreeShareEurope(user: UserDocument): Promise<boolean> {
    // Whitelist users get their own reward flow
    if (user.usedEuWhitelistCodeOrEmail) {
      return false;
    }

    const [hasMinimumDepositsToUnlockReward, doesNotAlreadyHaveReward] = await Promise.all([
      UserService._userHasTotalDepositAmount(user, MIN_DEPOSIT_AMOUNT_FOR_REWARD_ELIGIBILITY),
      RewardService.getRewards({ targetUser: user.id }, null, null, null).then(
        (rewards: { data: RewardDocument[] }) => rewards.data.length === 0
      )
    ]);

    return doesNotAlreadyHaveReward && !hasMinimumDepositsToUnlockReward;
  }

  /**
   * Handles free share unlocking logic for WEALTHYHOOD_UK users.
   *
   * Returns true if user:
   * 1. Has signed up in the last 10 days
   * 2. Has not already received a reward
   * 3. DOES NOT have total investments >= MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY
   *
   * @param user - The WEALTHYHOOD_UK user document to check
   * @returns Promise<boolean> - True if user can unlock free share
   */
  private static async _canUnlockFreeShareUK(user: UserDocument): Promise<boolean> {
    const hasSignedUpInTheLast10Days =
      user.createdAt >
      DateUtil.getDateOfDaysAgo(new Date(Date.now()), DAYS_AGO_TO_HAVE_SIGNED_UP_FOR_REWARD_ELIGIBILITY);

    // Return early if user signed up more than 10 days ago
    if (!hasSignedUpInTheLast10Days) {
      return false;
    }

    const [hasMinimumInvestmentsToUnlockReward, doesNotAlreadyHaveReward] = await Promise.all([
      UserService._userHasTotalInvestmentAmount(user, MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY),
      RewardService.getRewards({ targetUser: user.id }, null, null, null).then(
        (rewards: { data: RewardDocument[] }) => rewards.data.length === 0
      )
    ]);

    return doesNotAlreadyHaveReward && !hasMinimumInvestmentsToUnlockReward;
  }

  /**
   * Determines if the free share unlock banner should be shown to the user.
   *
   * Returns true if user:
   * 1. Can unlock a free share (meets all eligibility criteria)
   * 2. Has an investment portfolio with holdings OR has set up target allocation
   *
   * This method combines free share eligibility with portfolio readiness to determine
   * when to display the unlock banner in the UI.
   *
   * @param user - The user document to check
   * @returns Promise<boolean> - True if the unlock banner should be shown
   */
  private static async _showUnlockFreeShareBanner(user: UserDocument): Promise<boolean> {
    const [canUnlockFreeShare, portfolio] = await Promise.all([
      UserService.canUnlockFreeShare(user),
      PortfolioService.getGeneralInvestmentPortfolio(user)
    ]);

    return canUnlockFreeShare && (portfolio?.hasHoldings || portfolio?.isTargetAllocationSetup);
  }
}
