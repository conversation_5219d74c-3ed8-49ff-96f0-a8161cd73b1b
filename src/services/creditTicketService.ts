import { CreditTicket, CreditTicketDocument, CreditTicketPopulationFieldsEnum } from "../models/CreditTicket";
import DbUtil from "../utils/dbUtil";
import { UserDocument } from "../models/User";
import ProviderService from "./providerService";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";
import { InternalTransferStatusType, WealthkernelService } from "../external-services/wealthkernelService";
import mongoose from "mongoose";
import PortfolioService from "./portfolioService";
import { PortfolioDocument } from "../models/Portfolio";
import Decimal from "decimal.js";
import { DepositCashTransactionDocument } from "../models/Transaction";
import { CreditTicketRepository } from "../repositories/creditTicketRepository";
import { MainCurrencyToWealthkernelCurrency } from "../configs/currenciesConfig";
import { TransactionService } from "./transactionService";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import { DepositActionEnum } from "../configs/depositsConfig";

const CREDIT_TICKET_AMOUNT_LIMIT = 10000 * 100; // 10,000 EUR
const FUNDING_ACCOUNT_REMAINING_BALANCE_LIMIT = 0; // We allow our funding balance to go to down to 0.

export default class CreditTicketService {
  /**
   * PUBLIC METHODS
   */

  /**
   * @description Creates a CreditTicket for a deposit if one doesn't already exist.
   *
   * If the deposit has already left our Devengo acquisition account, it is too late to start the instant money flow.
   * This is a safety measure in case something goes wrong in credit ticket creation and the deposit start the non-instant money flow.
   *
   * @param deposit The deposit to create a CreditTicket for
   * @param options
   */
  public static async createCreditTicketForDeposit(
    deposit: DepositCashTransactionDocument,
    options: { session?: mongoose.ClientSession; requestInstantly?: boolean } = {}
  ): Promise<void> {
    if (deposit.linkedCreditTicket || deposit.hasDevengoAcquisitionStageCompleted) {
      return;
    }

    const creditTicket = await new CreditTicket({
      owner: deposit.owner,
      portfolio: deposit.portfolio,
      consideration: {
        currency: deposit.consideration.currency,
        amount: deposit.consideration.amount
      },
      status: "Pending",
      deposit: {
        activeProviders: deposit.activeProviders
      }
    }).save({ session: options?.session });

    await deposit.updateOne({ linkedCreditTicket: creditTicket.id }, { session: options?.session });

    logger.info(`Created credit ticket for deposit ${deposit.id}`, {
      module: "CreditTicketService",
      method: "createCreditTicketForDeposit",
      data: { depositId: deposit.id, creditTicketId: creditTicket.id }
    });

    if (options?.requestInstantly) {
      await CreditTicketService.createInternalTransferForCreditTicketSafely(creditTicket, {
        session: options?.session
      });
    }
  }

  /**
   * @description This method is used for bulk operations triggered usually from cron jobs.
   * It adds error handling in order to just report the error but not stop the execution of bulk operation.
   * @param creditTicket
   */
  public static async syncCreditTicketWithPendingInternalTransferSafely(
    creditTicket: CreditTicketDocument
  ): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(creditTicket, CreditTicketPopulationFieldsEnum.OWNER);
    const user = creditTicket.owner as UserDocument;

    try {
      const wkDeposit = await ProviderService.getBrokerageService(user.companyEntity).retrieveInternalTransfer(
        creditTicket.deposit.providers.wealthkernel.id
      );

      await CreditTicketService.updateCreditTicketWealthkernelStatus(creditTicket, wkDeposit.status);
    } catch (err) {
      captureException(err);
      logger.error(`Syncing with wealthkernel failed for deposit ${creditTicket.id}`, {
        module: "CreditTicketService",
        method: "syncCreditTicketWithPendingInternalTransferSafely",
        data: { creditTicketId: creditTicket.id, userId: user.id, error: err }
      });
    }
  }

  public static async createInternalTransferForCreditTicketSafely(
    creditTicket: CreditTicketDocument,
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<void> {
    try {
      await DbUtil.populateIfNotAlreadyPopulated(creditTicket, CreditTicketPopulationFieldsEnum.PORTFOLIO);
      const portfolio = creditTicket.portfolio as PortfolioDocument;

      if (creditTicket.consideration.amount > CREDIT_TICKET_AMOUNT_LIMIT) {
        await CreditTicketRepository.rejectCreditTicket(creditTicket.id, { session: options?.session });
        return;
      }

      const brokerageService = ProviderService.getBrokerageService(
        entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      );

      const currentBalance = await brokerageService.retrieveWealthyhoodFundingCashBalance();
      const remainingBalance = Decimal.sub(currentBalance, creditTicket.consideration.amount).toNumber();

      if (remainingBalance < FUNDING_ACCOUNT_REMAINING_BALANCE_LIMIT) {
        await CreditTicketRepository.rejectCreditTicket(creditTicket.id, { session: options?.session });
        return;
      }

      // Create internal transfer from Wealthyhood portfolio to user's portfolio
      const internalTransfer = await WealthkernelService.EUInstance.createInternalTransfer(
        {
          fromPortfolioId: process.env.WEALTHKERNEL_WH_PORTFOLIO_ID_FUNDING_EU,
          toPortfolioId: portfolio.providers.wealthkernel.id,
          consideration: {
            currency: MainCurrencyToWealthkernelCurrency[creditTicket.consideration.currency],
            amount: Decimal.div(creditTicket.consideration.amount, 100).toNumber()
          },
          clientReference: creditTicket.id
        },
        creditTicket.id
      );

      logger.info(
        `Created credit ticket internal transfer for credit ticket ${creditTicket.id} with WK id ${internalTransfer.id}`,
        {
          module: "CreditTicketCronService",
          method: "createCreditTicketDeposits"
        }
      );

      await CreditTicket.findByIdAndUpdate(
        creditTicket.id,
        {
          "deposit.providers.wealthkernel": {
            status: "Requested",
            id: internalTransfer.id,
            submittedAt: new Date(Date.now())
          }
        },
        { session: options?.session }
      );
    } catch (err) {
      captureException(err);
      logger.error(`Failed to create credit ticket internal transfer ${creditTicket.id}`, {
        module: "CreditTicketCronService",
        method: "createCreditTicketDeposits",
        data: {
          error: err
        }
      });
    }
  }

  public static async updateCreditTicketWealthkernelStatus(
    creditTicket: CreditTicketDocument,
    newWkStatus: InternalTransferStatusType
  ): Promise<void> {
    if (
      creditTicket?.deposit?.providers?.wealthkernel?.status === "Completed" ||
      creditTicket.status !== "Pending"
    ) {
      logger.warn(`Cannot update WK deposit for credit ticket ${creditTicket.id} as it is not pending!`, {
        module: "CreditTicketService",
        method: "updateCreditTicketWealthkernelStatus"
      });
      return;
    }

    logger.info(`Syncing credit ticket ${creditTicket.id}`, {
      module: "CreditTicketService",
      method: "updateCreditTicketWealthkernelStatus"
    });

    if (newWkStatus === "Completed") {
      await Promise.all([
        DbUtil.populateIfNotAlreadyPopulated(creditTicket, CreditTicketPopulationFieldsEnum.OWNER),
        DbUtil.populateIfNotAlreadyPopulated(creditTicket, CreditTicketPopulationFieldsEnum.PORTFOLIO),
        DbUtil.populateIfNotAlreadyPopulated(creditTicket, CreditTicketPopulationFieldsEnum.DEPOSIT)
      ]);

      const user = creditTicket.owner as UserDocument;
      const portfolio = creditTicket.portfolio as PortfolioDocument;
      const deposit = creditTicket.linkedDeposit as DepositCashTransactionDocument;

      const isBankAccountNameChecked = await TransactionService.isTransactionBankAccountNameChecked(deposit);
      if (!isBankAccountNameChecked) {
        return;
      }

      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        await CreditTicket.findByIdAndUpdate(
          creditTicket.id,
          {
            "deposit.providers.wealthkernel.status": "Completed",
            status: "Credited",
            creditedAt: new Date(Date.now())
          },
          { session }
        );

        if (deposit.depositAction === DepositActionEnum.JUST_PAY) {
          await PortfolioService.updateCashAvailability(
            portfolio.id,
            user.currency,
            Decimal.div(creditTicket.consideration.amount, 100).toNumber(),
            {
              available: true,
              settled: true,
              session
            }
          );

          const eventData = {
            amount: Decimal.div(creditTicket.consideration.amount, 100).toNumber(),
            currency: creditTicket.consideration.currency,
            noAssetTransactionPending: true
          };

          logger.info("eventEmitter.emit - depositAvailable", {
            module: "CreditTicketService",
            method: "updateCreditTicketWealthkernelStatus",
            data: { ...eventData, depositId: deposit.id }
          });
          eventEmitter.emit(events.transaction.depositAvailable.eventId, user, eventData);
        }
      });
    } else {
      await CreditTicket.findByIdAndUpdate(creditTicket.id, {
        "deposit.providers.wealthkernel.status": newWkStatus
      });
    }
  }
}
