import { dates } from "@wealthyhood/shared-configs";
import { DateTime } from "luxon";
import Decimal from "decimal.js";
import { marketHoursConfig } from "@wealthyhood/shared-configs";

const DAY_IN_MILLISECONDS = 24 * 60 * 60 * 1000;
const DAY_IN_SECONDS = 24 * 60 * 60;
const MINUTE_IN_SECONDS = 60;
const MINUTE_IN_MILLISECONDS = MINUTE_IN_SECONDS * 1000;
const AVG_MONTH_DURATION_IN_DAYS = 30.44;
const AVG_YEAR_DURATION_IN_DAYS = 365.25;

export enum DateOrderingEnum {
  MOST_RECENT_FIRST = "mostRecentFirst",
  LEAST_RECENT_FIRST = "leastRecentFirst"
}

export default class DateUtil {
  // Uk bank holidays retrieved from "https://www.gov.uk/bank-holidays.json"
  public static UK_BANK_HOLIDAYS = dates.ukBankHolidays;
  public static US_BANK_HOLIDAYS = dates.usBankHolidays;

  public static convertDaysToSeconds(days: number): number {
    return days * DAY_IN_SECONDS;
  }

  public static convertMinutesToMilliseconds(minutes: number): number {
    return minutes * MINUTE_IN_MILLISECONDS;
  }

  public static convertMinutesToSeconds(minutes: number): number {
    return minutes * MINUTE_IN_SECONDS;
  }

  public static getDateOfMinutesAgo(minutes: number): Date {
    const now = new Date(Date.now());
    return new Date(now.setMinutes(now.getMinutes() - minutes));
  }

  public static getDateOfMinutesFromNow(minutes: number): Date {
    const now = new Date(Date.now());
    return new Date(now.setMinutes(now.getMinutes() + minutes));
  }

  public static getDateOfHoursAgo(date: Date, hours: number): Date {
    return new Date(date.getTime() - hours * 60 * 60 * 1000);
  }

  public static getFirstDayOfMonthWithOffset(monthsOffset: number, now: Date = new Date(Date.now())): Date {
    const firstDayOfMonthsAgo = new Date(now.getFullYear(), now.getMonth() + monthsOffset, 1);
    firstDayOfMonthsAgo.setHours(0, 0, 0, 0);
    return firstDayOfMonthsAgo;
  }

  public static getFirstDayOfLastMonth(now: Date = new Date(Date.now())): Date {
    return DateUtil.getFirstDayOfMonthWithOffset(-1, now);
  }

  public static getFirstDayOfThisMonth(now: Date = new Date(Date.now())): Date {
    return DateUtil.getFirstDayOfMonthWithOffset(0, now);
  }

  public static getFirstDayOfNextMonth(now: Date = new Date(Date.now())): Date {
    return DateUtil.getFirstDayOfMonthWithOffset(1, now);
  }

  public static getFirstWorkDayOfNextMonth(now: Date = new Date(Date.now())): Date {
    return DateUtil.calculateNextUKWorkDay(DateUtil.getFirstDayOfNextMonth(now));
  }

  public static isFirstDayOfThisMonth(now: Date = new Date(Date.now())): boolean {
    return DateUtil.datesAreEqual(now, DateUtil.getFirstDayOfMonthWithOffset(0, now));
  }

  public static getLastDayOfLastMonth(now: Date = new Date(Date.now())): Date {
    /**
     * Setting day parameter to 0 means one day less than first day of the month which is last day of the previous month.
     */
    return new Date(now.getFullYear(), now.getMonth(), 0);
  }

  public static isLastUKWorkDayOfThisMonth(): boolean {
    const now = new Date(Date.now());
    const lastWorkDayOfThisMonth = DateUtil._getLastUKWorkDayOfThisMonth(now);

    return DateUtil.datesAreEqual(now, lastWorkDayOfThisMonth);
  }

  public static getDateOfDaysAgo(date: Date, days: number): Date {
    return new Date(date.getTime() - days * DAY_IN_MILLISECONDS);
  }

  public static getDateOfYearsAgo(date: Date, years: number): Date {
    const lastYearSameMonthSameDay = new Date(date);
    lastYearSameMonthSameDay.setFullYear(lastYearSameMonthSameDay.getFullYear() - years);
    return lastYearSameMonthSameDay;
  }

  public static getDateOfMonthsAgo(date: Date, months: number): Date {
    const lastMonthSameDay = new Date(date);
    lastMonthSameDay.setMonth(lastMonthSameDay.getMonth() - months);
    return lastMonthSameDay;
  }

  public static getDateForTime(date: Date, { atHours, atMinutes }: { atHours: number; atMinutes: number }): Date {
    date.setHours(atHours);
    date.setMinutes(atMinutes);
    return date;
  }

  public static getDateAfterNMonths(date: Date, months: number): Date {
    const dateAfterNMonths = new Date(date);
    dateAfterNMonths.setMonth(dateAfterNMonths.getMonth() + months);
    return dateAfterNMonths;
  }

  public static getDateAfterNdays(date: Date, days: number): Date {
    const dateAfterNdays = new Date(date);
    dateAfterNdays.setDate(date.getDate() + days);
    dateAfterNdays.setHours(0, 0, 0, 0);

    return dateAfterNdays;
  }

  public static getDateAfterNHours(date: Date, hours: number): Date {
    const dateAfterNHours = new Date(date);
    dateAfterNHours.setHours(dateAfterNHours.getHours() + hours);

    return dateAfterNHours;
  }

  public static isWeekend(date = new Date(Date.now())): boolean {
    const dayOfWeek = date.getDay();
    return dayOfWeek === 6 || dayOfWeek === 0; // 6 = Saturday, 0 = Sunday
  }

  public static isUKBankHoliday(dateToCheck = new Date(Date.now())): boolean {
    return this.UK_BANK_HOLIDAYS.some(({ date }) => DateUtil.datesAreEqual(date, dateToCheck));
  }

  public static isUSBankHoliday(dateToCheck = new Date(Date.now())): boolean {
    return this.US_BANK_HOLIDAYS.some(({ date }) => DateUtil.datesAreEqual(date, dateToCheck));
  }

  public static isInLastMonth(dateToCheck = new Date(Date.now())): boolean {
    const lastMonth = DateUtil.getFirstDayOfLastMonth();

    return dateToCheck.getMonth() === lastMonth.getMonth();
  }

  /**
   * Returns month and year in the format YYYY-MM
   * @param date
   */
  public static getYearAndMonth(date: Date): string {
    const [year, month] = date.toISOString().split("T")[0].split("-");

    return `${year}-${month}`;
  }

  /**
   * Returns day, month and year in the format YYYY-MM-DD
   * @param date
   */
  public static getYearAndMonthAndDay(date: Date): string {
    const [year, month, day] = date.toISOString().split("T")[0].split("-");
    return `${year}-${month}-${day}`;
  }

  /**
   * Check if given date is a work day.
   *
   * @param date
   * @private
   */
  public static isUKWorkDay(date: Date) {
    return !DateUtil.isWeekend(date) && !DateUtil.isUKBankHoliday(date);
  }

  public static isUSWorkDay(date: Date) {
    return !DateUtil.isWeekend(date) && !DateUtil.isUSBankHoliday(date);
  }

  /**
   * @description
   * Calculates next work day for the provided date,
   * considering weekends and UK bank holidays.
   *
   * @param date
   */
  public static calculateNextUKWorkDay(date: Date): Date {
    const oneDayAfterDate = DateUtil.getDateAfterNdays(date, 1);

    if (!DateUtil.isUKWorkDay(oneDayAfterDate)) {
      return DateUtil.calculateNextUKWorkDay(oneDayAfterDate);
    } else {
      return oneDayAfterDate;
    }
  }

  /**
   * @description
   * Calculates next work day for the provided date,
   * considering weekends and US bank holidays.
   *
   * @param date
   */
  public static calculateNextUSWorkDay(date: Date): Date {
    const oneDayAfterDate = DateUtil.getDateAfterNdays(date, 1);

    if (!DateUtil.isUSWorkDay(oneDayAfterDate)) {
      return DateUtil.calculateNextUSWorkDay(oneDayAfterDate);
    } else {
      return oneDayAfterDate;
    }
  }

  /**
   * @description
   * Calculates previous work day for the provided date,
   * considering weekends and bank holidays.
   *
   * @param date
   */
  public static calculatePreviousWorkDay(date: Date): Date {
    const oneDayBefore = DateUtil.getDateOfDaysAgo(date, 1);

    if (!DateUtil.isUKWorkDay(oneDayBefore)) {
      return DateUtil.calculatePreviousWorkDay(oneDayBefore);
    } else {
      return oneDayBefore;
    }
  }

  /**
   * @description
   * Calculates the closest previous work day for the provided date,
   * considering weekends and UK bank holidays.
   *
   * @param date
   */
  public static calculateMostRecentUKWorkDay(date: Date): Date {
    const oneDayBeforeDate = DateUtil.getDateAfterNdays(date, -1);

    if (DateUtil.isWeekend(oneDayBeforeDate) || DateUtil.isUKBankHoliday(oneDayBeforeDate)) {
      return DateUtil.calculateMostRecentUKWorkDay(oneDayBeforeDate);
    } else {
      return oneDayBeforeDate;
    }
  }

  public static datesAreEqual(date: Date, anotherDate: Date) {
    return (
      date.getDate() == anotherDate.getDate() &&
      date.getMonth() == anotherDate.getMonth() &&
      date.getFullYear() == anotherDate.getFullYear()
    );
  }

  /**
   * @returns True if the potentially future date is indeed in the future comparing to
   * the start date.
   */
  public static isFutureDate(potentiallyFutureDate: Date, startDate: Date) {
    return !DateUtil.datesAreEqual(potentiallyFutureDate, startDate) && potentiallyFutureDate > startDate;
  }

  /**
   * @returns True if the potentially past date is indeed in the past comparing to
   * the start date.
   */
  public static isPastDate(potentiallyPastDate: Date, startDate: Date) {
    return !DateUtil.datesAreEqual(potentiallyPastDate, startDate) && potentiallyPastDate < startDate;
  }

  /**
   * @returns True if the potentially past date is same or past date given the reference date.
   * the start date.
   */
  public static isSameOrPastDate(potentiallyPastDate: Date, startDate: Date) {
    return (
      DateUtil.datesAreEqual(potentiallyPastDate, startDate) || DateUtil.isPastDate(potentiallyPastDate, startDate)
    );
  }

  /**
   * @returns True if the candidate date is in the same date (same day - month - year) or is in the future compared
   * to the reference date.
   */
  public static isSameOrFutureDate(candidateDate: Date, referenceDate: Date) {
    return (
      DateUtil.datesAreEqual(candidateDate, referenceDate) || DateUtil.isFutureDate(candidateDate, referenceDate)
    );
  }

  public static isToday(potentiallyTodayDate: Date) {
    return DateUtil.datesAreEqual(potentiallyTodayDate, new Date(Date.now()));
  }

  public static isYesterday(potentiallyYesterdayDate: Date) {
    return DateUtil.datesAreEqual(potentiallyYesterdayDate, DateUtil.getYesterday());
  }

  public static convertToTimeZone(date: Date | DateTime, timeZone: string): Date {
    if (date instanceof Date) {
      return DateTime.fromJSDate(date).setZone(timeZone).toJSDate();
    } else if (date instanceof DateTime) {
      return date.setZone(timeZone).toJSDate();
    }
  }

  public static getNumberOfWeekDaysBetween(
    firstDate: Date,
    secondDate: Date,
    options: { includingStart: boolean } = { includingStart: true }
  ): number {
    return DateUtil.getWeekDaysBetween(firstDate, secondDate, {
      includingStart: options?.includingStart,
      includingEnd: true
    }).length;
  }

  /**
   * @description Method to calculate days between two dates excluding weekends
   * It does not take into account holidays.
   * @param firstDate
   * @param secondDate
   * @param options
   */
  public static getWeekDaysBetween(
    firstDate: Date,
    secondDate: Date,
    options: { includingStart: boolean; includingEnd: boolean } = { includingStart: true, includingEnd: true }
  ): Date[] {
    const start = new Date(firstDate);
    const end = new Date(options?.includingEnd ? secondDate : DateUtil.getDateOfDaysAgo(secondDate, 1));

    const workDays = options.includingStart ? [firstDate] : [];

    start.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);

    const current = new Date(start);
    current.setDate(current.getDate() + 1);
    while (current <= end) {
      if (!DateUtil.isWeekend(current)) {
        workDays.push(new Date(current));
      }
      current.setDate(current.getDate() + 1);
    }

    return workDays;
  }

  /**
   * @description
   * Returns difference between two dates in years.
   * Input dates are being normalized to utc.
   *
   * @param firstDate
   * @param secondDate
   */
  public static dateDiffInYears(firstDate: Date, secondDate: Date): number {
    // we divide by 365.25 instead of 365, to take into account leap years
    return Math.floor(DateUtil.dateDiffInWholeDays(firstDate, secondDate) / AVG_YEAR_DURATION_IN_DAYS);
  }

  /**
   * @description
   * Returns difference between two dates in months.
   * Input dates are being normalized to utc.
   *
   * @param firstDate
   * @param secondDate
   */
  public static dateDiffInMonths(firstDate: Date, secondDate: Date): number {
    return Math.floor(DateUtil.dateDiffInWholeDays(firstDate, secondDate) / AVG_MONTH_DURATION_IN_DAYS);
  }

  /**
   * @description
   * Returns difference between two dates in (whole) days.
   * Input dates are being normalized to utc and the result is **floored**.
   *
   * @param firstDate
   * @param secondDate
   */
  public static dateDiffInWholeDays(firstDate: Date, secondDate: Date): number {
    const exactDays = DateUtil.dateDiffInExactDays(firstDate, secondDate);

    return Math.floor(exactDays);
  }

  /**
   * @description
   * Returns difference between two dates in days.
   * Input dates are being normalized to utc. Result can be decimal (e.g. 1.235 days)
   *
   * @param firstDate
   * @param secondDate
   */
  public static dateDiffInExactDays(firstDate: Date, secondDate: Date): number {
    const dayInMilliSeconds = 1000 * 60 * 60 * 24;

    // Discard the time and time-zone information.
    const utc1 = Date.UTC(
      firstDate.getFullYear(),
      firstDate.getMonth(),
      firstDate.getDate(),
      firstDate.getHours(),
      firstDate.getMinutes(),
      firstDate.getSeconds()
    );
    const utc2 = Date.UTC(
      secondDate.getFullYear(),
      secondDate.getMonth(),
      secondDate.getDate(),
      secondDate.getHours(),
      secondDate.getMinutes(),
      secondDate.getSeconds()
    );

    return Decimal.sub(utc2, utc1).div(dayInMilliSeconds).toNumber();
  }

  public static getSecondsSinceEpoch(now: Date = new Date(Date.now())): number {
    return Decimal.div(now.getTime(), 1000).floor().toNumber();
  }

  /**
   * @description
   * Returns the difference between two dates in whole minutes.
   * Input dates are being normalized to UTC and the result is **floored**.
   *
   * @param firstDate
   * @param secondDate
   */
  public static dateDiffInWholeMinutes(firstDate: Date, secondDate: Date): number {
    const exactMinutes = DateUtil._dateDiffInExactMinutes(firstDate, secondDate);
    return Math.floor(exactMinutes);
  }

  /**
   * @description
   * Returns the exact difference between two dates in hours.
   * Input dates are being normalized to UTC. Result can be decimal (e.g. 2.345 hours)
   *
   * @param firstDate
   * @param secondDate
   */
  public static dateDiffInExactHours(firstDate: Date, secondDate: Date): number {
    const hourInMilliseconds = 1000 * 60 * 60;

    // Discard the time and time-zone information.
    const utc1 = Date.UTC(
      firstDate.getFullYear(),
      firstDate.getMonth(),
      firstDate.getDate(),
      firstDate.getHours()
    );
    const utc2 = Date.UTC(
      secondDate.getFullYear(),
      secondDate.getMonth(),
      secondDate.getDate(),
      secondDate.getHours()
    );

    return new Decimal(utc2 - utc1).div(hourInMilliseconds).toNumber();
  }

  /**
   * @description
   * Returns the difference between two dates in whole hours.
   * Input dates are being normalized to UTC and the result is **floored**.
   *
   * @param firstDate
   * @param secondDate
   */
  public static dateDiffInWholeHours(firstDate: Date, secondDate: Date): number {
    const exactHours = DateUtil.dateDiffInExactHours(firstDate, secondDate);
    return Math.floor(exactHours);
  }

  /**
   * Returns the date after N work days from the given date. The returned date will always be a work day.
   * Example: For input (7-1-2023-Saturday, 1), will return Monday.
   * For input (5-1-2023-Thursday, 2), will return Monday.
   * @private
   */
  public static getDateAfterNthUKWorkDays(from: Date, workDays: number) {
    let finalDate = from;
    for (let i = 0; i < workDays; i++) {
      finalDate = DateUtil.calculateNextUKWorkDay(finalDate);
    }
    return finalDate;
  }

  public static getDateAfterNthUSWorkDays(from: Date, workDays: number) {
    let finalDate = from;
    for (let i = 0; i < workDays; i++) {
      finalDate = DateUtil.calculateNextUSWorkDay(finalDate);
    }
    return finalDate;
  }

  /**
   * Returns the date N work days before the given date. The returned date will always be a work day.
   * @private
   */
  public static getDateNWorkDaysAgo(from: Date, workDays: number) {
    let finalDate = from;
    for (let i = 0; i < workDays; i++) {
      finalDate = DateUtil.calculatePreviousWorkDay(finalDate);
    }
    return finalDate;
  }

  /**
   * Returns an array of all dates between a start and an end date.
   * @private
   */
  public static getAllDatesBetweenTwoDates(
    from: Date,
    until: Date,
    options: { order: DateOrderingEnum } = { order: DateOrderingEnum.LEAST_RECENT_FIRST }
  ) {
    const allDates = [];

    for (let date = new Date(from); date <= new Date(until); date.setDate(date.getDate() + 1)) {
      if (options.order === DateOrderingEnum.LEAST_RECENT_FIRST) {
        allDates.push(new Date(date)); // Adds element in the end of the array
      } else if (options.order === DateOrderingEnum.MOST_RECENT_FIRST) {
        allDates.unshift(new Date(date)); // Adds element in the beginning of the array
      }
    }

    return allDates;
  }

  /**
   * Converts into a day of month, 1-28 or -1 if date is 29, 30 or 31.
   * @param date
   */
  public static convertIntoRecurrenceDate(date: Date): number {
    return date.getDate() <= 28 ? date.getDate() : -1;
  }

  public static isFirstMondayOfTheMonthOrLater(now: Date = new Date(Date.now())): boolean {
    const firstMondayOfTheMonth = DateUtil._getFirstMondayOfTheMonth(now);

    return now.getDate() >= firstMondayOfTheMonth.getDate();
  }

  public static getStartAndEndOfDay(day: Date = new Date(Date.now())): { start: Date; end: Date } {
    return { start: DateUtil.getStartOfDay(day), end: DateUtil.getEndOfDay(day) };
  }

  public static getStartOfDay(day: Date = new Date(Date.now())): Date {
    const start = new Date(day);
    start.setHours(0, 0, 0, 0);

    return start;
  }

  public static getEndOfDay(day: Date = new Date(Date.now())): Date {
    const end = new Date(day);
    end.setHours(23, 59, 59, 999);

    return end;
  }

  public static getStartAndEndOfToday(): { start: Date; end: Date } {
    const start = new Date(Date.now());
    start.setHours(0, 0, 0, 0);
    const end = new Date(Date.now());
    end.setHours(23, 59, 59, 999);

    return { start, end };
  }

  public static getStartAndEndOfYesterday(): { start: Date; end: Date } {
    const yesterday = DateUtil.getYesterday();
    const start = new Date(yesterday);
    start.setHours(0, 0, 0, 0);
    const end = new Date(yesterday);
    end.setHours(23, 59, 59, 999);

    return { start, end };
  }

  public static getStartAndEndOfLastMonth(now: Date = new Date(Date.now())): { start: Date; end: Date } {
    const start = DateUtil.getFirstDayOfLastMonth(now);
    const end = DateUtil.getLastDayOfLastMonth(now);
    end.setHours(23, 59, 59, 999);

    return { start, end };
  }

  public static getYesterday(now: Date = new Date(Date.now())): Date {
    const date = new Date(now);
    date.setDate(date.getDate() - 1);
    return date;
  }

  public static getLastNDaysRange(startingFrom = new Date(Date.now()), n: number): { start: Date; end: Date } {
    const end = DateTime.fromJSDate(startingFrom).set({ hour: 23, minute: 59 });
    const start = end.minus({ days: n }).set({ hour: 0, minute: 0 });

    return {
      start: start.toJSDate(),
      end: end.toJSDate()
    };
  }

  public static formatDateToDDMONTHYYYY(date: Date): string {
    return new Date(date).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "long",
      year: "numeric"
    });
  }

  public static formatDateToDDMONYYYY(date: Date, options?: { separatorCharacter: string }): string {
    const DEFAULT_SEPARATOR_CHARACTER = " ";

    const formattedDate = new Date(date).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric"
    });

    if (options?.separatorCharacter && options?.separatorCharacter !== DEFAULT_SEPARATOR_CHARACTER) {
      return formattedDate.replace(/ /g, options?.separatorCharacter);
    }

    return formattedDate;
  }

  /**
   * Formats a date to "Day DD MON YYYY" -> e.g. "Monday 28 Oct 2024"
   * @param date
   */
  public static formatDateToDAYDDMONYYYY(date: Date): string {
    return new Date(date).toLocaleDateString("en-GB", {
      weekday: "long",
      day: "2-digit",
      month: "short",
      year: "numeric"
    });
  }

  /**
   * Formats a date to "Day DD" -> e.g. "Mon 28"
   * @param date
   * @param options
   */
  public static formatDateToDAYDD(date: Date, options?: { separatorCharacter: string }): string {
    const formattedDate = date.toLocaleDateString("en-GB", {
      weekday: "short",
      day: "2-digit"
    });

    if (options?.separatorCharacter) {
      return formattedDate.replace(" ", options.separatorCharacter);
    }

    return formattedDate;
  }

  /**
   * Formats a date to format Day, DD MON -> e.g. "Fri, 18 Oct"
   * @param date
   */
  public static formatDateToDAYDDMON(date: Date): string {
    return new Date(date).toLocaleDateString("en-GB", {
      weekday: "short",
      day: "2-digit",
      month: "short"
    });
  }

  public static formatDateToMONYYYY(date: Date): string {
    return new Date(date).toLocaleDateString("en-GB", {
      month: "short",
      year: "numeric" // full numeric year
    });
  }

  /**
   * @description Converts a given date string to a formatted string representing the quarter of the year and the last two digits of the year.
   * @param {string} dateStr - The date string to be converted.
   * @returns {string} A string in the format "Qx • yy", where 'Qx' represents the quarter and 'yy' represents the last two digits of the year.
   */
  public static getQuarterlyDisplayDate(dateStr: string): string {
    const date = new Date(dateStr);
    const year = date.getFullYear().toString().slice(-2);
    const month = date.getMonth();
    const quarter = ["Q1", "Q2", "Q3", "Q4"][Math.floor(month / 3)];

    return `${quarter} • ${year}`;
  }

  /**
   * @description Converts a given date string to a string representing the full year.
   * @param {string} dateStr - The date string to be converted.
   * @returns {string} A string representing the full year extracted from the given date.
   */
  public static getAnnualDisplayDate(dateStr: string): string {
    const date = new Date(dateStr);
    return date.getFullYear().toString();
  }

  // example format: 2024-01-31
  public static formatDateToYYYYMMDD(date: Date): string {
    return new Date(date).toLocaleDateString("en-CA", { year: "numeric", month: "2-digit", day: "2-digit" });
  }

  // example format: 31/01/2024 (Greek date format)
  public static formatDateToGreek(dateStr: string): string {
    const [year, month, day] = dateStr.split("-");
    return `${day}/${month}/${year}`;
  }

  //example format: 23 Apr 24, 14:23
  public static formatDateToDDMONYYYYHHMM(date: Date): string {
    const dateString = new Date(date).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric"
    });
    const timeString = new Date(date).toLocaleTimeString("en-GB", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false
    });

    // Combine the date and time strings
    return `${dateString}, ${timeString}`;
  }

  // example format: 23 Apr 24, 14:23:01 (UTC)
  public static formatDateToDDMONYYYYHHMMSSUTC(date: Date): string {
    const dateString = new Date(date).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric"
    });
    const timeString = new Date(date).toLocaleTimeString("en-GB", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    });

    // Combine the date and time strings
    return `${dateString}, ${timeString} (UTC)`;
  }

  /**
   * @description Given a date & an interval that can be in minutes or weeks,
   * it maps the input date to the closest date as specified by the interval.
   *
   * For minutes, rounds to the nearest interval (e.g. 10 min intervals: 3:57 => 4:00)
   * For weeks, rounds to the nearest Friday of that week
   *
   * @param date The date to map
   * @param interval The interval size
   * @param unit The unit of the interval ('minutes' | 'weeks')
   */
  public static mapDateToClosestTimeMark(date: Date, interval: number, unit: "minutes" | "weeks"): Date {
    if (unit === "minutes") {
      return DateUtil._mapDateToClosestTimeMarkForMinutesInterval(date, interval);
    } else if (unit === "weeks") {
      return DateUtil._mapDateToClosestTimeMarkForWeeksInterval(date);
    }
  }

  /**
   * Returns a number from 0 to 1 representing the completion percentage from the start date
   * to the end date.
   * @param startDate
   * @param endDate
   */
  public static getCompletionPercentage(startDate: Date, endDate: Date): number {
    const today = new Date(Date.now());

    // If today's date is after the end date, we return 1 representing 100%.
    if (DateUtil.isFutureDate(today, endDate)) {
      return 1;
    }

    const millisecondsFromStartToEnd = endDate.valueOf() - startDate.valueOf();
    const millisecondsFromTodayToEnd = endDate.valueOf() - today.valueOf();

    return Decimal.sub(millisecondsFromStartToEnd, millisecondsFromTodayToEnd)
      .div(millisecondsFromStartToEnd)
      .toDecimalPlaces(2)
      .toNumber();
  }

  /**
   * Returns the current year as a number
   * @param date Optional date to get year from, defaults to current date
   */
  public static getCurrentYear(date: Date = new Date(Date.now())): number {
    return date.getFullYear();
  }

  /**
   * @description
   * Returns difference between two dates in minutes.
   * Input dates are being normalized to UTC. Result can be decimal (e.g. 1.235 minutes)
   *
   * @param firstDate
   * @param secondDate
   */
  private static _dateDiffInExactMinutes(firstDate: Date, secondDate: Date): number {
    const minuteInMilliseconds = 1000 * 60;

    // Discard the time and time-zone information.
    const utc1 = Date.UTC(
      firstDate.getFullYear(),
      firstDate.getMonth(),
      firstDate.getDate(),
      firstDate.getHours(),
      firstDate.getMinutes()
    );
    const utc2 = Date.UTC(
      secondDate.getFullYear(),
      secondDate.getMonth(),
      secondDate.getDate(),
      secondDate.getHours(),
      secondDate.getMinutes()
    );

    return Decimal.sub(utc2, utc1).div(minuteInMilliseconds).toNumber();
  }

  private static _getFirstMondayOfTheMonth(now: Date = new Date(Date.now())): Date {
    // Iterate through days starting with first day of the month until we find the first Monday
    const currentDay = DateUtil.getFirstDayOfThisMonth(now);
    while (currentDay.getDay() !== 1) {
      currentDay.setDate(currentDay.getDate() + 1);
    }

    return currentDay;
  }

  /**
   * Returns the last work day of the current month, by first finding the first day of next month,
   * and then going backwards from there, finding the first date that is a work day.
   * @private
   */
  private static _getLastUKWorkDayOfThisMonth(now: Date = new Date(Date.now())): Date {
    const firstDayOfNextMonth = DateUtil.getFirstDayOfNextMonth(now);

    return DateUtil.calculateMostRecentUKWorkDay(firstDayOfNextMonth);
  }

  /**
   * @description Given a date & an interval in minutes (for example 10, for 10 minutes),
   * it maps the input date to the closest date as specified by the interval.
   *
   * For example:
   * 2024-09-01T03:57:00 => 2024-09-01T04:00:00
   *
   */
  private static _mapDateToClosestTimeMarkForMinutesInterval(date: Date, interval: number): Date {
    const minutes = date.getMinutes();
    const roundedMinutes = Math.round(minutes / interval) * interval;
    const resultDate = new Date(date);
    resultDate.setMinutes(roundedMinutes);
    resultDate.setSeconds(0);
    resultDate.setMilliseconds(0);
    return resultDate;
  }

  /**
   * @description Given a date, maps it to the closest Friday.
   * If the date is closer to the previous Friday (Mon or earlier), it will map to that.
   * If the date is closer to the next Friday (Tue or later), it will map to that.
   * Time is reset to start of day (00:00:00).
   *
   * For example:
   * Sunday => previous 2 days (Friday)
   * Monday => previous 3 days (Friday)
   * Tuesday => next 3 days (Friday)
   * Wednesday => next 2 days (Friday)
   * Thursday => next 1 day (Friday)
   * Friday => same day
   * Saturday => previous 1 day (Friday)
   */
  private static _mapDateToClosestTimeMarkForWeeksInterval(date: Date): Date {
    const resultDate = new Date(date);
    const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, ..., 5 = Friday, 6 = Saturday

    // Calculate days to closest Friday (day 5)
    let daysToAdd;
    if (dayOfWeek === 5) {
      // Friday
      daysToAdd = 0;
    } else if (dayOfWeek === 6) {
      // Saturday
      daysToAdd = -1;
    } else if (dayOfWeek === 0) {
      // Sunday
      daysToAdd = -2;
    } else {
      // Monday through Thursday
      // If we're closer to previous Friday (Mon), go back; otherwise go forward to next Friday
      daysToAdd = dayOfWeek <= 1 ? -(dayOfWeek + 2) : 5 - dayOfWeek;
    }

    // Adjust to closest Friday
    resultDate.setDate(date.getDate() + daysToAdd);

    // Reset time to start of day
    resultDate.setHours(0, 0, 0, 0);

    return resultDate;
  }

  /**
   * Compute earliest opening and latest closing across all exchanges for a given calendar date.
   * The function converts each market's hours in the market's own time zone, then returns UTC
   * Date objects.
   *
   * @param date
   */

  public static getEarliestOpenAndLatestCloseMarketHours(date: Date = new Date(Date.now())): {
    earliestOpen: { HOUR: number; MINUTES: number };
    latestClose: { HOUR: number; MINUTES: number };
  } {
    const opensUtc: DateTime[] = [];
    const closesUtc: DateTime[] = [];

    for (const exchangeConfig of Object.values(marketHoursConfig.MARKET_TRADING_HOURS)) {
      const openLocal = DateTime.fromJSDate(date).setZone(exchangeConfig.timeZone).set({
        hour: exchangeConfig.start.HOUR,
        minute: exchangeConfig.start.MINUTES,
        second: 0,
        millisecond: 0
      });

      const closeLocal = DateTime.fromJSDate(date).setZone(exchangeConfig.timeZone).set({
        hour: exchangeConfig.end.HOUR,
        minute: exchangeConfig.end.MINUTES,
        second: 0,
        millisecond: 0
      });

      opensUtc.push(openLocal.toUTC());
      closesUtc.push(closeLocal.toUTC());
    }

    const earliestOpenUtc = opensUtc.reduce((min, p) => (p < min ? p : min));
    const latestCloseUtc = closesUtc.reduce((max, p) => (p > max ? p : max));

    return {
      earliestOpen: {
        HOUR: earliestOpenUtc.hour,
        MINUTES: earliestOpenUtc.minute
      },
      latestClose: {
        HOUR: latestCloseUtc.hour,
        MINUTES: latestCloseUtc.minute
      }
    };
  }

  public static isDateWithinTimeBounds(
    date: Date,
    min: { HOUR: number; MINUTES: number },
    max: { HOUR: number; MINUTES: number }
  ): boolean {
    const dt = DateTime.fromJSDate(date, { zone: "utc" });

    const minUtc = dt.startOf("day").set({ hour: min.HOUR, minute: min.MINUTES, second: 0, millisecond: 0 });
    const maxUtc = dt.startOf("day").set({ hour: max.HOUR, minute: max.MINUTES, second: 0, millisecond: 0 });

    return dt >= minUtc && dt <= maxUtc;
  }

  public static setDateToLatestCloseMarketHours(date: Date = new Date(Date.now())): Date {
    const { latestClose } = DateUtil.getEarliestOpenAndLatestCloseMarketHours(date);
    return DateUtil.getDateForTime(date, { atHours: latestClose.HOUR, atMinutes: latestClose.MINUTES });
  }
}
