import {
  investmentUniverseConfig,
  marketHoursConfig,
  publicInvestmentUniverseConfig
} from "@wealthyhood/shared-configs";
import DateUtil from "./dateUtil";
import { RedisClientService } from "../loaders/redis";
import { PartialRecord } from "types/utils";
import { AssetPriceDataPointType, PortfolioPriceDataPointType } from "tickers";
import { StockSplitCorporateEventDocument } from "../models/CorporateEvent";
import Decimal from "decimal.js";
import { DateTime } from "luxon";
import { TenorEnum } from "../configs/durationConfig";
import { DURATIONS_MAP } from "../configs/durationConfig";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { PUBLIC_ASSET_CONFIG } = publicInvestmentUniverseConfig;
const { MARKET_TRADING_HOURS } = marketHoursConfig;

export const getCachedTodaysTickers = async (): Promise<
  PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
> => {
  // fetch cached data
  const data =
    await RedisClientService.Instance.get<
      PartialRecord<investmentUniverseConfig.AssetType, { close: number; timestamp: number }>
    >("fmp:today:latest_tickers");
  const latestTickers = data ?? {};

  // filter out any empty prices & prices that are not today's
  const { start } = DateUtil.getStartAndEndOfToday();
  const filteredLatestTickers = Object.fromEntries(
    Object.entries(latestTickers).filter(
      ([, entry]) => entry && entry.close > 0 && entry.timestamp > new Date(start).getTime()
    )
  );

  return filteredLatestTickers;
};

/**
 * @description Given a ticker data point & an interval in minutes (for example 10, for 10 minutes),
 * it maps the input ticker to one with timestamp the closest date as specified by the interval.
 *
 * For example:
 * { close: 10, timestamp: new Date("2024-09-01T03:57:00" }
 *
 * returns
 * { close: 10, timestamp: new Date("2024-09-01T04:00:00" }
 *
 */
export const mapTickerToClosestTimeMark = <T extends AssetPriceDataPointType | PortfolioPriceDataPointType>(
  ticker: T,
  interval: number,
  unit: "minutes" | "weeks" = "minutes"
): T => {
  const updatedTicker = { ...ticker };
  updatedTicker.timestamp = DateUtil.mapDateToClosestTimeMark(
    new Date(ticker.timestamp),
    interval,
    unit
  ).getTime();
  return updatedTicker;
};

/**
 * @description Takes an array of tickers (timestamp & closing price) and an interval and
 * returns an array of tickers sampled by the interval defined and mapped to the closest timemark as
 * specified by the interval.
 *
 * Steps:
 * 1. For each ticker create a key value mapping where key is the timestamp of the closest time mark
 * 2. Iterate on all tickers and assign them to the closest time mark key if the entry is empty or
 *    if the diff is smaller
 * 3. Convert the dict to an array of tickers sorted by timestamp
 *
 * @param tickers Array of ticker data points
 * @param interval the sampling frequency in minutes or weeks (for example 10 for 10 minute frequency or 1 for 1 week frequency)
 * @param unit The unit of the interval ('minutes' | 'weeks')
 * @returns an array of tickers sampled by the interval defined and mapped to the closest timemark as specified by the interval.
 *
 * For example, for 10 minute interval and input
 * [
 *   { timestamp: new Date("2024-08-01T04:02:00").getTime(), close: 9 },
 *   { timestamp: new Date("2024-09-01T03:57:00").getTime(), close: 8 },
 *   { timestamp: new Date("2024-09-01T04:02:00").getTime(), close: 10 },
 *   { timestamp: new Date("2024-09-01T04:08:00").getTime(), close: 20 },
 *   { timestamp: new Date("2024-09-01T04:09:00").getTime(), close: 30 },
 *   { timestamp: new Date("2024-09-01T05:18:00").getTime(), close: 40 },
 *   { timestamp: new Date("2024-09-01T05:35:00").getTime(), close: 5 },
 *   { timestamp: new Date("2024-09-01T05:47:00").getTime(), close: 1 }
 * ]
 *
 * the output is
 * [
 *   { timestamp: new Date("2024-08-01T04:00:00").getTime(), close: 9 }, // 04:02 => 04:00
 *   { timestamp: new Date("2024-09-01T04:00:00").getTime(), close: 10 }, // 04:02 => 04:00
 *   { timestamp: new Date("2024-09-01T04:10:00").getTime(), close: 30 }, // 04:09 => 04:10
 *   { timestamp: new Date("2024-09-01T05:20:00").getTime(), close: 40 }, // 05:18 => 05:20
 *   { timestamp: new Date("2024-09-01T05:40:00").getTime(), close: 5 }, // 05:35 => 05:40
 *   { timestamp: new Date("2024-09-01T05:50:00").getTime(), close: 1 } // 05:47 => 05:50
 * ]
 *
 */
export const sampleTickers = <T extends AssetPriceDataPointType | PortfolioPriceDataPointType>(
  tickers: T[],
  interval: number,
  unit: "minutes" | "weeks" = "minutes"
): T[] => {
  const tickersClosestTimeMarkMapping: Map<number, T> = new Map();

  tickers.forEach((ticker) => {
    const tickerTimeMark = DateUtil.mapDateToClosestTimeMark(new Date(ticker.timestamp), interval, unit).getTime();

    if (!tickersClosestTimeMarkMapping.has(tickerTimeMark)) {
      tickersClosestTimeMarkMapping.set(tickerTimeMark, { ...ticker });
    } else if (
      Math.abs(tickerTimeMark - ticker.timestamp) <
      Math.abs(tickerTimeMark - tickersClosestTimeMarkMapping.get(tickerTimeMark).timestamp)
    ) {
      tickersClosestTimeMarkMapping.set(tickerTimeMark, { ...ticker });
    }
  });

  return Array.from(tickersClosestTimeMarkMapping.entries())
    .map(([timeMark, ticker]) => {
      return {
        ...ticker,
        timestamp: timeMark
      };
    })
    .sort((tickerA, tickerB) => tickerA.timestamp - tickerB.timestamp) as T[];
};

export const adjustHoldingForSplit = (quantity: number, stockSplit: StockSplitCorporateEventDocument): number => {
  if (!stockSplit) {
    return quantity;
  }

  return Decimal.div(quantity, stockSplit.multiplier)
    .mul(stockSplit.divider)
    .toDecimalPlaces(4, Decimal.ROUND_DOWN)
    .toNumber();
};

export const adjustPriceForSplit = (close: number, stockSplit: StockSplitCorporateEventDocument): number => {
  if (!stockSplit) {
    return close;
  }

  return Decimal.mul(close, stockSplit.multiplier).div(stockSplit.divider).toNumber();
};

/**
 * Adjusts the price within the array of data points based on the given stock split.
 *
 * @param data
 * @param stockSplit
 */
export const adjustPricesForSplit = (
  data: AssetPriceDataPointType[],
  stockSplit: StockSplitCorporateEventDocument
): AssetPriceDataPointType[] => {
  return data.map((point) => {
    if (DateUtil.isSameOrFutureDate(new Date(point.timestamp), stockSplit.date)) {
      return point;
    }

    return {
      ...point,
      close: adjustPriceForSplit(point.close, stockSplit)
    };
  });
};

export const filterOlderThanNDaysOfPriceData = <T extends AssetPriceDataPointType | PortfolioPriceDataPointType>(
  data: T[],
  days: number
): T[] => {
  if (!data?.length) {
    return data;
  }

  const startOfToday = DateUtil.getStartOfDay(new Date(Date.now()));
  const hasDataFromToday = data.at(-1).timestamp >= startOfToday.getTime();

  // If we have data from today, request 1 less day
  const daysToFilter = hasDataFromToday ? days - 1 : days;
  const cutoffDate = DateUtil.getStartOfDay(DateUtil.getDateOfDaysAgo(new Date(Date.now()), daysToFilter));

  return data.filter((point) => point.timestamp >= cutoffDate.getTime());
};

/**
 * @description Sets a date to the market closing time for a specific asset.
 * The time will be set to the closing hour and minute of the asset's exchange in the exchange's timezone.
 *
 * @param assetCommonId The asset to get the market closing time for (can be AssetType or PublicAssetType)
 * @param date The date to set to market closing time (defaults to current date)
 * @returns A Date object set to the market closing time in the exchange's timezone
 *
 * @example
 * // For a US stock, sets the date to 16:00 America/New_York time
 * const closingTime = setDateToAssetMarketClosingTime('equities_apple');
 */
export const setDateToAssetMarketClosingTime = (
  assetCommonId: investmentUniverseConfig.AssetType | publicInvestmentUniverseConfig.PublicAssetType,
  date: Date = new Date(Date.now())
): Date => {
  const assetConfig =
    ASSET_CONFIG[assetCommonId as investmentUniverseConfig.AssetType] ||
    PUBLIC_ASSET_CONFIG[assetCommonId as publicInvestmentUniverseConfig.PublicAssetType];
  const { formalExchange } = assetConfig;

  const marketClosing = DateTime.fromJSDate(date)
    .setZone(MARKET_TRADING_HOURS[formalExchange].timeZone, { keepLocalTime: true })
    .set({
      hour: MARKET_TRADING_HOURS[formalExchange].end.HOUR,
      minute: MARKET_TRADING_HOURS[formalExchange].end.MINUTES,
      second: 0,
      millisecond: 0
    });

  return marketClosing.toJSDate();
};

/**
 * @description Adds the previous day's last price as a starting point to the filtered price data.
 * This method finds the last price from the day before the first data point in the filtered data
 * and prepends it to the array with the timestamp set to the asset's market closing time.
 *
 * @param assetCommonId The asset to get the market closing time for (can be AssetType or PublicAssetType)
 * @param data The current data
 * @param allPriceData All available price data to search for the previous day's close
 */
export const addPreviousDayCloseAsStartingPoint = (
  assetCommonId: investmentUniverseConfig.AssetType | publicInvestmentUniverseConfig.PublicAssetType,
  data: AssetPriceDataPointType[],
  allPriceData: AssetPriceDataPointType[]
): AssetPriceDataPointType[] => {
  const earliestDataTimestamp = data[0]?.timestamp;
  if (!earliestDataTimestamp) {
    return data;
  }

  const startOfDay = DateUtil.getStartOfDay(new Date(earliestDataTimestamp));
  const previousDayData = allPriceData
    .filter((point) => {
      const pointDay = DateUtil.getStartOfDay(new Date(point.timestamp));
      return pointDay.getTime() < startOfDay.getTime();
    })
    .sort((a, b) => b.timestamp - a.timestamp); // Sort descending to get the latest

  // Add the last price from the previous day as the starting point with timestamp set to market closing time
  if (previousDayData.length > 0) {
    const startingPoint = { ...previousDayData[0] };
    startingPoint.timestamp = setDateToAssetMarketClosingTime(
      assetCommonId,
      new Date(startingPoint.timestamp)
    ).getTime();
    return [startingPoint, ...data];
  }

  return data;
};

/**
 * @description Returns the date at the start of the tenor, adjusted for today's data if it exists.
 *
 * @param tenor
 * @param referenceDate
 * @param todayDataExist
 * @private
 */
export const getDateAtTenorStart = (tenor: TenorEnum, referenceDate: Date, todayDataExist: boolean): Date => {
  if (tenor === TenorEnum.TODAY) {
    return DateUtil.getStartOfDay(referenceDate);
  }

  const duration = todayDataExist ? DURATIONS_MAP[tenor] - 1 : DURATIONS_MAP[tenor];
  return DateUtil.getStartOfDay(DateUtil.getDateOfDaysAgo(referenceDate, duration));
};
