import { addBreadcrumb, captureException } from "@sentry/node";
import { countriesConfig } from "@wealthyhood/shared-configs";
import axios from "axios";
import { randomUUID } from "crypto";
import https from "https";
import qs from "qs";
import { PartialRecord } from "utils";
import { BadRequestError, ForbiddenError, InternalServerError } from "../models/ApiErrors";
import { getCachedDataWithFallback } from "../utils/cacheUtil";
import { hashSHA256utf8 } from "../utils/cryptoUtil";
import { delay } from "../utils/scriptUtil";
import logger from "./loggerService";

const API_VERSION = "2021-05-17";
export const BANK_ACCOUNT_NAME_CHAR_LIMIT = 50;

export enum CurrencyEnum {
  EUR = "EUR",
  GBP = "GBP",
  USD = "USD"
}

export enum WealthkernelAccountRegionEnum {
  UK = "UK",
  EU = "EU"
}

/**
 * DATA TYPES
 */
type AccountDataType = {
  type: PortfolioWrapperTypeEnum;
  clientReference?: string;
  name: string;
  productId: string;
  owner: string;
  currency: CurrencyEnum;
};

type BonusDataType = {
  destinationPortfolio: string;
  consideration: {
    currency: string;
    amount: number;
  };
  clientReference?: string;
};

type InternalTransferDataType = {
  fromPortfolioId: string;
  toPortfolioId: string;
  consideration: {
    currency: CurrencyEnum;
    amount: number;
  };
  clientReference?: string;
};

type MandateDataType = {
  bankAccountId: string;
  partyId: string;
};

type DirectDebitPaymentDataType = {
  amount: {
    currency: string;
    amount: number;
  };
  mandateId: string;
  portfolioId: string;
  collectionDate: string;
};

type AddressDataType = {
  partyId: string;
  clientReference?: string;
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  region?: string;
  countryCode: countriesConfig.CountryCodesType; // Must be ISO3166 two-letter country code.
  postalCode: string;
  startDate?: Date;
  endDate?: Date;
};

type BankAccountDataType = {
  partyId: string;
  clientReference?: string;
  name: string;
  accountNumber: string;
  sortCode: string;
  currency: CurrencyEnum; // Currently only 'GBP' is supported
  countryCode: countriesConfig.CountryCodesType; // Currently only 'GB' is supported
};

export const DocumentCodesArray = ["NINO"] as const;

export type DocumentCodesType = (typeof DocumentCodesArray)[number];

type IdentifierType = {
  issuer: countriesConfig.CountryCodesType;
  type: DocumentCodesType;
  value: string;
};

export enum PortfolioWrapperTypeEnum {
  GIA = "GIA",
  ISA = "ISA",
  JISA = "JISA",
  SIPP = "SIPP"
}
export const PortfolioWrapperArray = ["GIA", "ISA", "JISA", "SIPP"] as const;
type PortfolioWrapperType = (typeof PortfolioWrapperArray)[number];
const PortfolioMandateArray = ["ExecutionOnlyMandate"];
type PortfolioMandateType = (typeof PortfolioMandateArray)[number];
type PortfolioDataType = {
  accountId: string;
  clientReference?: string;
  name: string;
  currency: CurrencyEnum;
  mandate: {
    type: PortfolioMandateType;
  };
};
type CashValuationType = {
  currency: string;
  value: MoneyType;
  amount: MoneyType;
  fxRate: number;
};
type DepositDataType = {
  portfolioId: string; // wealthkernel portfolio id
  useDefaultAccount: boolean;
  bankAccountId?: string;
  consideration: {
    currency: string;
    amount: number;
  };
  reference: string; // bank reference for the deposit
};
export type HoldingValuationType = {
  isin: string;
  quantity: number;
  price: MoneyType;
  value: MoneyType;
  fxRate: number;
};
type MoneyType = {
  currency: CurrencyEnum;
  amount: number;
};
type OrderDataType = {
  portfolioId: string;
  isin: string;
  settlementCurrency: CurrencyEnum;
  side: OrderSideType;
  aggregate?: boolean;
  consideration?: {
    currency: CurrencyEnum;
    amount: number;
  };
  clientReference?: string;
  quantity?: number;
};

type PartyDataType = {
  clientReference?: string;
  type: "Person";
  title?: string;
  forename: string;
  middlename?: string;
  surname: string;
  previousSurname?: string;
  emailAddress: string;
  telephoneNumber?: string;
  dateOfBirth: string;
  taxResidencies: countriesConfig.CountryCodesType[];
  nationalities: countriesConfig.CountryCodesType[];
  identifiers: IdentifierType[];
} & EmploymentInfoType;

export const WithdrawalRequestArray = ["SpecifiedAmount", "Full"] as const;
export type WithdrawalRequestType = (typeof WithdrawalRequestArray)[number];

type WithdrawalDataType = {
  type: WithdrawalRequestType;
  portfolioId: string;
  bankAccountId?: string;
  consideration?: {
    currency: CurrencyEnum;
    amount: number;
  };
  reference: string;
};
export type WithdrawalRetrievalResponseType = {
  id: string;
  type: WithdrawalRequestType;
  portfolioId: string;
  bankAccountId: string;
  consideration?: {
    currency: CurrencyEnum;
    amount: number;
  };
  paidOut?: {
    currency: CurrencyEnum;
    amount: number;
  };
  reference: string;
  status: WithdrawalStatusType;
  requestedAt: Date;
};

type ChargeDataType = {
  clientReference?: string;
  portfolioId: string;
  consideration?: {
    currency: CurrencyEnum;
    amount: number;
  };
  narrative: string;
  reason?: string;
};

type W8BenFormDataType = {
  partyId: string;
};

type W8BenFormCompletedDataType = {
  completedAt: string; // Date in ISO-8601 format
};

/**
 * QUERY PARAM TYPES
 */
export type ListChargesParamsType = {
  portfolioId?: string;
  clientReference?: string;
};

export type ListInternalTransfersParamsType = {
  fromPortfolioId?: string;
};

type AccountQueryParamsType = {
  accountId?: string;
  after?: string;
  child?: string;
  clientReference?: string;
  limit?: number;
  //Owner party to filter by. Applies to ISAs and GIAs
  owner?: string;
  productId?: string;
  status?: AccountStatusType;
  type?: PortfolioWrapperType;
};

type AddressQueryParamsType = {
  after?: string;
  limit?: number;
  partyId?: string;
};

type BankAccountQueryParamsType = {
  accountNumber?: string;
  after?: string;
  clientReference?: string;
  limit?: number;
  partyId?: string;
  sortCode?: string;
};

type PartyQueryParamsType = {
  clientReference?: string;
  emailAddress?: string;
  partyId?: string;
  surname?: string;
  status?: string;
};

type PortfoliosQueryParamsType = {
  accountId?: string;
  after?: string;
  clientReference?: string;
  currency?: CurrencyEnum;
  limit?: number; // integer default 20
  status?: PortfolioStatusType;
};

type ValuationsQueryParamsType = {
  portfolioId?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
};

type TransactionsQueryParamsType = {
  portfolioId?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
  after?: string;
  type?: TransactionTypeType;
  status?: TransactionStatusType;
};

export enum WealthkernelEventEnum {
  BONUS_CREATED = "bonuses.bonus_created",
  BONUS_REJECTED = "bonuses.bonus_rejected",
  BONUS_SETTLED = "bonuses.bonus_settled",
  ORDER_MATCHED = "orders.order_matched",
  ORDER_REJECTED = "orders.order_rejected",
  ORDER_SETTLED = "orders.order_settled",
  ACCOUNT_SUSPENDED = "accounts.account_suspended",
  ACCOUNT_UNSUSPENDED = "accounts.account_unsuspended",
  ACCOUNT_ACTIVATED = "accounts.account_activated",
  ACCOUNT_CLOSED = "accounts.account_closed",
  DEPOSIT_CREATED = "deposits.deposit_created",
  DEPOSIT_REJECTED = "deposits.deposit_rejected",
  DEPOSIT_SETTLED = "deposits.deposit_settled",
  DEPOSIT_CANCELLED = "deposits.deposit_cancelled",
  INTERNAL_TRANSFER_ACCEPTED = "internal_transfers.internal_transfer_accepted",
  INTERNAL_TRANSFER_REJECTED = "internal_transfers.internal_transfer_rejected",
  INTERNAL_TRANSFER_COMPLETED = "internal_transfers.internal_transfer_completed",
  WITHDRAWAL_CREATED = "withdrawals.withdrawal_created",
  WITHDRAWAL_SETTLED = "withdrawals.withdrawal_settled",
  WITHDRAWAL_REJECTED = "withdrawals.withdrawal_rejected",
  WITHDRAWAL_CANCELLED = "withdrawals.withdrawal_cancelled",
  CHARGE_REQUESTED = "charges.charge_requested",
  CHARGE_BOOKED = "charges.charge_booked",
  CHARGE_REJECTED = "charges.charge_rejected",
  CHARGE_CANCELLED = "charges.charge_cancelled",
  BANK_ACCOUNT_ACTIVATED = "bank_accounts.bank_account_activated",
  BANK_ACCOUNT_SUSPENDED = "bank_accounts.bank_account_suspended"
}

const BonusEventToStatusDict: PartialRecord<string, BonusStatusType> = {
  [WealthkernelEventEnum.BONUS_CREATED]: "Created",
  [WealthkernelEventEnum.BONUS_REJECTED]: "Rejected",
  [WealthkernelEventEnum.BONUS_SETTLED]: "Settled"
};

const OrderEventToStatusDict: PartialRecord<string, WealthkernelOrderStatusWithSettledType> = {
  [WealthkernelEventEnum.ORDER_MATCHED]: "Matched",
  [WealthkernelEventEnum.ORDER_REJECTED]: "Rejected",
  [WealthkernelEventEnum.ORDER_SETTLED]: "Settled"
};

const AccountEventToStatusDict: PartialRecord<string, AccountStatusType> = {
  [WealthkernelEventEnum.ACCOUNT_ACTIVATED]: "Active",
  [WealthkernelEventEnum.ACCOUNT_SUSPENDED]: "Suspended",
  [WealthkernelEventEnum.ACCOUNT_UNSUSPENDED]: "Active",
  [WealthkernelEventEnum.ACCOUNT_CLOSED]: "Closed"
};

const DepositEventToStatusDict: PartialRecord<string, DepositStatusType> = {
  [WealthkernelEventEnum.DEPOSIT_CREATED]: "Created",
  [WealthkernelEventEnum.DEPOSIT_REJECTED]: "Rejected",
  [WealthkernelEventEnum.DEPOSIT_SETTLED]: "Settled",
  [WealthkernelEventEnum.DEPOSIT_CANCELLED]: "Cancelled"
};

const InternalTransferEventToStatusDict: PartialRecord<string, InternalTransferStatusType> = {
  [WealthkernelEventEnum.INTERNAL_TRANSFER_ACCEPTED]: "Accepted",
  [WealthkernelEventEnum.INTERNAL_TRANSFER_REJECTED]: "Rejected",
  [WealthkernelEventEnum.INTERNAL_TRANSFER_COMPLETED]: "Completed"
};

const WithdrawalEventToStatusDict: PartialRecord<string, WithdrawalStatusType> = {
  [WealthkernelEventEnum.WITHDRAWAL_CREATED]: "Created",
  [WealthkernelEventEnum.WITHDRAWAL_REJECTED]: "Rejected",
  [WealthkernelEventEnum.WITHDRAWAL_SETTLED]: "Settled",
  [WealthkernelEventEnum.WITHDRAWAL_CANCELLED]: "Cancelled"
};

const ChargeEventToStatusDict: PartialRecord<string, ChargeStatusType> = {
  [WealthkernelEventEnum.CHARGE_REQUESTED]: "Requested",
  [WealthkernelEventEnum.CHARGE_BOOKED]: "Booked",
  [WealthkernelEventEnum.CHARGE_REJECTED]: "Rejected",
  [WealthkernelEventEnum.CHARGE_CANCELLED]: "Cancelled"
};

const BankAccountEventToStatusDict: PartialRecord<string, BankAccountStatusType> = {
  [WealthkernelEventEnum.BANK_ACCOUNT_ACTIVATED]: "Active",
  [WealthkernelEventEnum.BANK_ACCOUNT_SUSPENDED]: "Suspended"
};

export type WealthkernelEventPayloadType = {
  eventId: string;
  eventType: {
    name: string;
    version: number;
  };
  occurredAt: string;
};

export type WealthkernelBonusEventPayloadType = WealthkernelEventPayloadType & {
  payload: {
    bonusId: string;
  };
};

export type WealthkernelOrderEventPayloadType = WealthkernelEventPayloadType & {
  payload: {
    orderId: string;
  };
};

export type WealthkernelAccountEventPayloadType = WealthkernelEventPayloadType & {
  payload: {
    accountId: string;
  };
};

export type WealthkernelDepositEventPayloadType = WealthkernelEventPayloadType & {
  payload: {
    depositId: string;
  };
};

export type WealthkernelInternalTransferEventPayloadType = WealthkernelEventPayloadType & {
  payload: {
    internalTransferId: string;
  };
};

export type WealthkernelWithdrawalEventPayloadType = WealthkernelEventPayloadType & {
  payload: {
    withdrawalId: string;
  };
};

export type WealthkernelChargeEventPayloadType = WealthkernelEventPayloadType & {
  payload: {
    chargeId: string;
  };
};

export type WealthkernelBankAccountEventPayloadType = WealthkernelEventPayloadType & {
  payload: {
    bankAccountId: string;
  };
};

/**
 * ENTITY TYPES
 */
export const AccountStatusArray = ["Pending", "Active", "Suspended", "Closing", "Closed"] as const;
export type AccountStatusType = (typeof AccountStatusArray)[number];
export type AccountType = AccountDataType & {
  status: AccountStatusType;
  addedAt: Date;
  id: string;
};
export type AddressType = AddressDataType & {
  id: string;
};
export type BalancesType = BalanceType[];
export type BalanceType = {
  value?: MoneyType;
  quantity?: number;
  isin?: string;
};

export const BankAccountStatusArray = ["Active", "Inactive", "Pending", "Suspended"] as const;
export type BankAccountStatusType = (typeof BankAccountStatusArray)[number];
export type BankAccountType = BankAccountDataType & {
  id: string;
  status: BankAccountStatusType;
  activatedAt: Date;
  deactivatedAt: Date;
};
export const DepositStatusArray = ["Created", "Settled", "Cancelled", "Rejected", "Cancelling"] as const;
export type DepositStatusType = (typeof DepositStatusArray)[number];
export type DepositType = {
  id: string;
  portfolioId: string;
  accountId: string;
  consideration: {
    currency: string;
    amount: number;
  };
  reference: string;
  status: DepositStatusType;
  createdAt: string;
};

export const DirectDebitPaymentStatusArray = [
  "Pending",
  "Collecting",
  "Collected",
  "Completed",
  "Cancelled",
  "Failed"
] as const;
export type DirectDebitPaymentStatusType = (typeof DirectDebitPaymentStatusArray)[number];
export type DirectDebitPaymentType = {
  id: string;
  status: DirectDebitPaymentStatusType;
  amount: {
    currency: string;
    amount: number;
  };
};

export const BonusStatusArray = ["Created", "Rejected", "Settled"] as const;
export type BonusStatusType = (typeof BonusStatusArray)[number];
export type BonusType = BonusDataType & {
  id: string;
  status: BonusStatusType;
};

export const InternalTransferStatusArray = ["Requested", "Accepted", "Rejected", "Completed"] as const;
export type InternalTransferStatusType = (typeof InternalTransferStatusArray)[number];

export type InternalTransferType = InternalTransferDataType & {
  id: string;
  status: InternalTransferStatusType;
};

export type ChargeType = ResponseType &
  ChargeDataType & {
    status: ChargeStatusType;
  };

export const WealthkernelMandateStatusArray = ["Pending", "Active", "Cancelled", "Failed"] as const;
export type WealthkernelMandateStatusType = (typeof WealthkernelMandateStatusArray)[number];
export type MandateType = MandateDataType & {
  status: WealthkernelMandateStatusType;
};

export type FillType = {
  transactionId: string;
  price: MoneyType;
  consideration: {
    currency: CurrencyEnum;
    amount: number;
  };
  quantity: number;
  status: "Matched" | "Cancelled";
  filledAt: Date;
  settlementDate: Date;
  // will always have a value (1.0 if no FX)
  exchangeRate: number;
  baseExchangeRate: number; // Base exchange rate from WealthKernel
};
export const OrderSideArray = ["Buy", "Sell"] as const;
export type OrderSideType = (typeof OrderSideArray)[number];

export const WealthkernelOrderStatusArray = [
  "Pending",
  "Open",
  "Matched",
  "Rejected",
  "Cancelling",
  "Cancelled"
] as const;
export type WealthkernelOrderStatusType = (typeof WealthkernelOrderStatusArray)[number];
export type WealthkernelOrderStatusWithSettledType = WealthkernelOrderStatusType | "Settled";
export type OrderType = OrderDataType & {
  fills: FillType[];
  reason: string;
  receivedAt: Date;
  id: string;
  status: WealthkernelOrderStatusType;
};

export type PartyType = PartyDataType & {
  addedAt: Date;
  id: string;
};

export const PortfolioStatusArray = ["Created", "Active", "Closing", "Closed"] as const;
export type PortfolioStatusType = (typeof PortfolioStatusArray)[number];
export type PortfolioType = PortfolioDataType & { createdAt: Date; id: string; status: PortfolioStatusType };
export const TransactionStatusArray = ["Matched", "Settled", "Cancelled"] as const;
export type TransactionStatusType = (typeof TransactionStatusArray)[number];
export const TransactionTypeArray = [
  "Adjustment",
  "Buy",
  "Sell",
  "Deposit",
  "Withdrawal",
  "Charge",
  "ConsolidationIn",
  "ConsolidationOut",
  "Dividend",
  "FxIn",
  "FxOut",
  "CashTransferIn",
  "CashTransferOut",
  "InternalCashTransferIn",
  "InternalCashTransferOut",
  "InternalTransferIn",
  "InternalTransferOut",
  "TransferIn",
  "TransferOut",
  "Redemption"
] as const;
type TransactionTypeType = (typeof TransactionTypeArray)[number];
export type TransactionType = {
  id: string;
  portfolioId: string;
  isin?: string;
  type: TransactionTypeType;
  status: TransactionStatusType;
  price?: MoneyType;
  quantity?: number;
  consideration: MoneyType;
  charges: MoneyType;
  date: string;
  timestamp: string;
  settledOn: string;
  updatedAt: string;
  bookCost?: MoneyType;
  narrative?: string;
};

export const WithdrawalStatusArray = ["Pending", "Active", "Settled", "Cancelling", "Cancelled", "Rejected"];
export type WithdrawalStatusType = (typeof WithdrawalStatusArray)[number];

export type ValuationType = {
  portfolioId: string;
  date: Date;
  value: MoneyType;
  cash: CashValuationType[];
  holdings: HoldingValuationType[];
  changedAt: Date;
};

export type ResponseType = { id: string };

export const ChargeStatusArray = ["Requested", "Booked", "Cancelled", "Rejected"] as const;
export type ChargeStatusType = (typeof ChargeStatusArray)[number];

export const IndustryArray = [
  "AgricultureForestryAndFishing",
  "ArtsSportAndCreative",
  "ConstructionAndEngineering",
  "CryptoIndustryAndCryptocurrencies",
  "CulturalArtefacts",
  "DatingOrAdultIndustry",
  "Education",
  "EnergyAndWaterSupply",
  "FinanceAndInsurance",
  "GamblingOrIGamingIndustry",
  "GovernmentPublicServiceAndDefence",
  "HealthAndSocialWork",
  "Hospitality",
  "ImportAndExport",
  "InformationAndCommunication",
  "LegalAndRegulatory",
  "Manufacturing",
  "Mining",
  "MoneyTransfer",
  "MotorTrades",
  "PreciousMetals",
  "Property",
  "Retail",
  "ScientificAndTechnical",
  "Tobacco",
  "TransportAndStorage",
  "Wholesale"
] as const;
export type IndustryType = (typeof IndustryArray)[number];

export const SourceOfWealthArray = [
  "Salary",
  "Inheritance",
  "Gift",
  "BusinessOwnership",
  "SaleOfProperty",
  "GamblingOrLottery",
  "PersonalSavings",
  "LegalSettlement",
  "SaleOfInvestments",
  "Dividend"
] as const;
export type SourceOfWealthType = (typeof SourceOfWealthArray)[number];

export const EmploymentStatusArray = [
  "FullTime",
  "PartTime",
  "SelfEmployed",
  "Unemployed",
  "Retired",
  "Student",
  "NotWorkingDueToIllnessOrDisability",
  "CarerOrParent"
] as const;
export type EmploymentStatusType = (typeof EmploymentStatusArray)[number];
export const EMPLOYMENT_STATUSES_REQUIRE_INDUSTRY: EmploymentStatusType[] = [
  "FullTime",
  "PartTime",
  "SelfEmployed"
];
export type EmploymentInfoType = {
  sourcesOfWealth: SourceOfWealthType[];
  industry?: IndustryType;
  annualIncome: MoneyType;
  employmentStatus: EmploymentStatusType;
};

/**
 * OTHER TYPES
 */
type RequestHeadersType = {
  Authorization?: string;
  "Accept-Version"?: string;
  "Request-Id"?: string;
  "Idempotency-Key"?: string;
};
type ResponseHeadersType = {
  "content-length"?: string;
  "content-type"?: string;
  "pagination-last"?: string;
  "x-correlation-id"?: string;
  "strict-transport-security"?: string;
  "x-cache"?: string;
  "x-azure-ref"?: string;
  date?: string;
  connection?: string;
};

/**
 * ENUMS
 */
enum WkEndpointEnum {
  ACCOUNTS = "accounts",
  ADDRESSES = "addresses",
  BALANCES = "balances",
  BANK_ACCOUNTS = "bank-accounts",
  DEPOSITS = "deposits",
  ORDERS = "orders",
  PARTIES = "parties",
  PORTFOLIOS = "portfolios",
  TRANSACTIONS = "transactions",
  BONUSES = "bonuses",
  MANDATES = "direct-debits/mandates",
  DIRECT_DEBIT_PAYMENTS = "direct-debits/payments",
  VALUATIONS = "valuations",
  WITHDRAWALS = "withdrawals",
  CHARGES = "charges",
  W_8BEN = "us-withholding-tax/w8bens",
  INTERNAL_TRANSFERS = "transfers/internal"
}

enum HttpMethodEnum {
  GET = "get",
  POST = "post"
}

const axiosInstance = axios.create({
  timeout: 60000,
  httpsAgent: new https.Agent({ keepAlive: true })
});

type AuthResponseType = {
  access_token: string;
  expires_in: number; // in seconds
  token_type: string;
  scope: string;
};

// In seconds
const TOKEN_CACHE_EXPIRATION_BUFFER = 10 * 60; // 10 minutes
const TOKEN_POLLING_INTERVAL = 5 * 60; // 5 minutes
const TOKEN_ERROR_INTERVAL = 60; // 1 minute

export class WealthkernelService {
  private static _authUrl = `${process.env.WEALTHKERNEL_AUTH_URL}/connect/token`;
  private static _serviceUrl = process.env.WEALTHKERNEL_SERVICE_URL;
  private static _instances: Record<WealthkernelAccountRegionEnum, WealthkernelService> = {
    [WealthkernelAccountRegionEnum.EU]: undefined,
    [WealthkernelAccountRegionEnum.UK]: undefined
  };

  private _clientId: string;
  private _clientSecret: string;
  private _region: WealthkernelAccountRegionEnum;
  private _accessToken: string;
  private _authPromise: Promise<AuthResponseType>;

  constructor(options: { region: WealthkernelAccountRegionEnum }) {
    this._clientId = process.env[`WEALTHKERNEL_CLIENT_ID_${options?.region}`];
    this._clientSecret = process.env[`WEALTHKERNEL_CLIENT_SECRET_${options?.region}`];
    this._region = options?.region;

    WealthkernelService._verifyEnvironmentVariablesExist();

    this._requestAuthToken();
  }

  // ====================
  // Statics - Utils
  // ====================
  public static get EUInstance(): WealthkernelService {
    return (
      this._instances[WealthkernelAccountRegionEnum.EU] ||
      (this._instances[WealthkernelAccountRegionEnum.EU] = new this({ region: WealthkernelAccountRegionEnum.EU }))
    );
  }

  public static get UKInstance(): WealthkernelService {
    return (
      this._instances[WealthkernelAccountRegionEnum.UK] ||
      (this._instances[WealthkernelAccountRegionEnum.UK] = new this({ region: WealthkernelAccountRegionEnum.UK }))
    );
  }

  public static getAllRegionInstances(): WealthkernelService[] {
    return [WealthkernelService.EUInstance, WealthkernelService.UKInstance];
  }

  public get region(): WealthkernelAccountRegionEnum {
    return this._region;
  }

  /**
   * @description Takes a date as an input and modifies it to the format <yyyy-mm-dd> expected by the WK API
   * @param date
   */
  public static formatDate(date: Date): string {
    const day = new Date(date).toLocaleDateString("en-GB", { day: "2-digit" });
    const month = new Date(date).toLocaleDateString("en-GB", { month: "2-digit" });
    const year = new Date(date).toLocaleDateString("en-GB", { year: "numeric" });
    return `${year}-${month}-${day}`;
  }

  /**
   * @description Returns that most recent date when a calculation took place by Wealthkernel. For Mondays and
   * Sundays it will return Fridays. For any other day it will return the date before - job runs the morning after.
   */
  public static getLatestCalculationDate(): string {
    let targetDate = new Date().getTime() - 1 * 24 * 60 * 60 * 1000;
    const longDay = new Date().toLocaleDateString("en-GB", { weekday: "long" });
    if (longDay === "Sunday") {
      targetDate = new Date().getTime() - 2 * 24 * 60 * 60 * 1000;
    } else if (longDay === "Monday") {
      targetDate = new Date().getTime() - 3 * 24 * 60 * 60 * 1000;
    }

    return WealthkernelService.formatDate(new Date(targetDate));
  }

  /**
   * Validates if wealthkernel signature is valid. If not it throws a ForbiddenError.
   *
   * @param signature
   * @param region
   * @param rawBodyStr
   */
  public static validateWebhookSignature(
    signature: string,
    region: WealthkernelAccountRegionEnum,
    rawBodyStr: string
  ) {
    if (!signature) {
      throw new BadRequestError("No signature found", "Invalid header");
    }

    const secretBase64 = process.env[`WEALTHKERNEL_WEBHOOK_SECRET_${region}`];
    const secretDecoded = Buffer.from(secretBase64, "base64");

    const signatureSplitByComma = signature.split(",");
    const timestamp = signatureSplitByComma[0].split("t=")[1];
    const signatureHmac = signatureSplitByComma[1].split("v1=")[1];
    const content = rawBodyStr + timestamp;
    const contentHmac = hashSHA256utf8(content, secretDecoded);

    if (signatureHmac != contentHmac) {
      throw new ForbiddenError("Received webhook from wealthkernel with invalid signature!");
    }
  }

  public static isBonusEvent(eventName: string): boolean {
    return [
      WealthkernelEventEnum.BONUS_CREATED,
      WealthkernelEventEnum.BONUS_SETTLED,
      WealthkernelEventEnum.BONUS_REJECTED
    ]
      .map((val) => val.toString())
      .includes(eventName);
  }

  public static isOrderEvent(eventName: string): boolean {
    return [
      WealthkernelEventEnum.ORDER_MATCHED,
      WealthkernelEventEnum.ORDER_REJECTED,
      WealthkernelEventEnum.ORDER_SETTLED
    ]
      .map((val) => val.toString())
      .includes(eventName);
  }

  public static isAccountEvent(eventName: string): boolean {
    return [
      WealthkernelEventEnum.ACCOUNT_SUSPENDED,
      WealthkernelEventEnum.ACCOUNT_UNSUSPENDED,
      WealthkernelEventEnum.ACCOUNT_ACTIVATED,
      WealthkernelEventEnum.ACCOUNT_CLOSED
    ]
      .map((val) => val.toString())
      .includes(eventName);
  }

  public static getStatusFromBonusEvent(eventName: string): BonusStatusType {
    if (!this.isBonusEvent(eventName.toString())) {
      throw new InternalServerError("Wealthkernel event must be related to bonuses!");
    }
    return BonusEventToStatusDict[eventName];
  }

  public static getStatusFromOrderEvent(eventName: string): WealthkernelOrderStatusWithSettledType {
    if (!this.isOrderEvent(eventName.toString())) {
      throw new InternalServerError("Wealthkernel event must be related to orders!");
    }
    return OrderEventToStatusDict[eventName];
  }

  public static getStatusFromAccountEvent(eventName: string): AccountStatusType {
    if (!this.isAccountEvent(eventName.toString())) {
      throw new InternalServerError("Wealthkernel event must be related to accounts!");
    }
    return AccountEventToStatusDict[eventName];
  }

  public static isDepositEvent(eventName: string): boolean {
    return [
      WealthkernelEventEnum.DEPOSIT_CREATED,
      WealthkernelEventEnum.DEPOSIT_REJECTED,
      WealthkernelEventEnum.DEPOSIT_SETTLED,
      WealthkernelEventEnum.DEPOSIT_CANCELLED
    ]
      .map((val) => val.toString())
      .includes(eventName);
  }

  public static isInternalTransferEvent(eventName: string): boolean {
    return [
      WealthkernelEventEnum.INTERNAL_TRANSFER_ACCEPTED,
      WealthkernelEventEnum.INTERNAL_TRANSFER_COMPLETED,
      WealthkernelEventEnum.INTERNAL_TRANSFER_REJECTED
    ]
      .map((val) => val.toString())
      .includes(eventName);
  }

  public static getStatusFromDepositEvent(eventName: string): DepositStatusType {
    if (!this.isDepositEvent(eventName)) {
      throw new InternalServerError("Wealthkernel event must be related to deposits!");
    }
    return DepositEventToStatusDict[eventName];
  }

  public static getStatusFromInternalTransferEvent(eventName: string): InternalTransferStatusType {
    if (!this.isInternalTransferEvent(eventName)) {
      throw new InternalServerError("Wealthkernel event must be related to internal transfers!");
    }
    return InternalTransferEventToStatusDict[eventName];
  }

  public static isWithdrawalEvent(eventName: string): boolean {
    return [
      WealthkernelEventEnum.WITHDRAWAL_CANCELLED,
      WealthkernelEventEnum.WITHDRAWAL_CREATED,
      WealthkernelEventEnum.WITHDRAWAL_REJECTED,
      WealthkernelEventEnum.WITHDRAWAL_SETTLED
    ]
      .map((val) => val.toString())
      .includes(eventName);
  }

  public static getStatusFromWithdrawalEvent(eventName: string): WithdrawalStatusType {
    if (!this.isWithdrawalEvent(eventName)) {
      throw new InternalServerError("Wealthkernel event must be related to widrawals!");
    }
    return WithdrawalEventToStatusDict[eventName];
  }

  public static isChargeEvent(eventName: string): boolean {
    return [
      WealthkernelEventEnum.CHARGE_REQUESTED,
      WealthkernelEventEnum.CHARGE_BOOKED,
      WealthkernelEventEnum.CHARGE_REJECTED,
      WealthkernelEventEnum.CHARGE_CANCELLED
    ]
      .map((val) => val.toString())
      .includes(eventName);
  }

  public static getStatusFromChargeEvent(eventName: string): ChargeStatusType {
    if (!this.isChargeEvent(eventName)) {
      throw new InternalServerError("Wealthkernel event must be related to charges!");
    }
    return ChargeEventToStatusDict[eventName];
  }

  public static isBankAccountEvent(eventName: string): boolean {
    return [WealthkernelEventEnum.BANK_ACCOUNT_ACTIVATED, WealthkernelEventEnum.BANK_ACCOUNT_SUSPENDED]
      .map((val) => val.toString())
      .includes(eventName);
  }

  public static getStatusFromBankAccountEvent(eventName: string): BankAccountStatusType {
    if (!this.isBankAccountEvent(eventName)) {
      throw new InternalServerError("Wealthkernel event must be related to bank accounts!");
    }
    return BankAccountEventToStatusDict[eventName];
  }

  // ====================
  // Accounts
  // ====================

  /**
   * @description Creates a new account.
   * @param accountData
   */
  public async createAccount(accountData: AccountDataType): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.ACCOUNTS, {}, accountData);
  }

  /**
   * @description Retrieves an account
   * @param accountId
   */
  public async retrieveAccount(accountId: string): Promise<AccountType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.ACCOUNTS}/${accountId}`);
  }

  /**
   * @description Creates a new account.
   * @param accountId
   */
  public async closeAccount(accountId: string): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, `${WkEndpointEnum.ACCOUNTS}/${accountId}/actions/close`);
  }

  /**
   * @description Retrieves a list of accounts.
   * @param accountQueryParams
   */
  public async retrieveAccounts(accountQueryParams: AccountQueryParamsType): Promise<AccountType[]> {
    const accounts = await this.fetch(HttpMethodEnum.GET, WkEndpointEnum.ACCOUNTS, accountQueryParams, {});
    return accounts || [];
  }

  // ====================
  // Addresses
  // ====================

  /**
   * @description Adds a new address to the party.
   * @param addressData
   */
  public async addAddress(addressData: AddressDataType): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.ADDRESSES, {}, addressData);
  }

  /**
   * @description Retrieves a list of addresses.
   * @param addressQueryParams
   */
  public async listAddresses(addressQueryParams: AddressQueryParamsType): Promise<AddressType[]> {
    const addresses = await this.fetch(HttpMethodEnum.GET, WkEndpointEnum.ADDRESSES, addressQueryParams, {});
    return addresses || [];
  }

  // ====================
  // Balances
  // ====================

  /**
   * @description Retrieves the cash balance of the given portfolioId
   * @param portfolioId
   */
  public async listCashBalances(portfolioId: string): Promise<BalancesType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.BALANCES}/cash`, { portfolioId });
  }

  /**
   * @description Retrieves the holdings balance of the given portfolioId
   * @param portfolioId
   * @param isin
   */
  public async listHoldingsBalances(portfolioId: string, isin: string): Promise<BalancesType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.BALANCES}/holdings`, { portfolioId, isin });
  }

  // ====================
  // Bank Accounts
  // ====================

  /**
   * @description Adds a new bank account to the party.
   * @param bankAccountData
   */
  public async addBankAccount(bankAccountData: BankAccountDataType): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.BANK_ACCOUNTS, {}, bankAccountData);
  }

  /**
   * @description Retrieves a bank account
   * @param bankAccountId
   */
  public async retrieveBankAccount(bankAccountId: string): Promise<BankAccountType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.BANK_ACCOUNTS}/${bankAccountId}`);
  }

  /**
   * @description Retrieves a list of bank accounts.
   * @param bankAccountQueryParams
   */
  public async retrieveBankAccounts(
    bankAccountQueryParams: BankAccountQueryParamsType
  ): Promise<BankAccountType[]> {
    const bankAccounts = await this.fetch(
      HttpMethodEnum.GET,
      WkEndpointEnum.BANK_ACCOUNTS,
      bankAccountQueryParams,
      {}
    );
    return bankAccounts || [];
  }

  // ====================
  // Deposits
  // ====================

  /**
   * @description Creates a new deposit.
   * @param depositData
   */
  public async createDeposit(depositData: DepositDataType): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.DEPOSITS, {}, depositData);
  }

  /**
   * @description Retrieves a deposit
   */
  public async retrieveDeposit(depositId: string): Promise<DepositType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.DEPOSITS}/${depositId}`);
  }

  // ====================
  // Orders
  // ====================

  /**
   * @description Creates an order.
   * @param orderData
   * @param idempotencyKey
   */
  public async createOrder(orderData: OrderDataType, idempotencyKey?: string): Promise<ResponseType> {
    const headerConfig = {
      "Idempotency-Key": idempotencyKey
    };

    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.ORDERS, {}, orderData, headerConfig);
  }

  public async retrieveOrder(orderId: string): Promise<OrderType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.ORDERS}/${orderId}`);
  }

  // ====================
  // Parties
  // ====================

  /**
   * @description Retrieves a list of parties along with associated accounts and addresses.
   * @param partyQueryParams
   */
  public async retrieveParties(partyQueryParams: PartyQueryParamsType): Promise<PartyType[]> {
    const parties = await this.fetch(HttpMethodEnum.GET, WkEndpointEnum.PARTIES, partyQueryParams, {});
    return parties || [];
  }

  /**
   * @description Creates a new party.
   * @param partyData
   */
  public async createParty(partyData: PartyDataType): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.PARTIES, {}, partyData);
  }

  // ====================
  // Portfolios
  // ====================

  /**
   * @description Creates a new portfolio.
   * @param portfolioData
   */
  public async createPortfolio(portfolioData: PortfolioDataType): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.PORTFOLIOS, {}, portfolioData);
  }

  /**
   * @description Retrieves an existing portfolio.
   * @param portfolioId
   */
  public async retrievePortfolio(portfolioId: string): Promise<PortfolioType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.PORTFOLIOS}/${portfolioId}`);
  }

  public async retrievePortfolios(params: PortfoliosQueryParamsType): Promise<PortfolioType[]> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.PORTFOLIOS}`, params);
  }

  /**
   * @description Closes an existing portfolio.
   * @param portfolioId
   */
  public async closePortfolio(portfolioId: string): Promise<PortfolioType> {
    return await this.fetch(HttpMethodEnum.POST, `${WkEndpointEnum.PORTFOLIOS}/${portfolioId}/actions/close`);
  }

  // ====================
  // Transactions
  // ====================

  /**
   * @description Retrieves a transaction.
   * @param transactionId
   */
  public async retrieveTransaction(transactionId: string): Promise<TransactionType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.TRANSACTIONS}/${transactionId}`);
  }

  /**
   * @description Lists transactions.
   * @param params
   * @param processorFn
   */
  public async listTransactions(
    params: TransactionsQueryParamsType = {},
    processorFn?: (transaction: TransactionType) => Promise<void>
  ): Promise<TransactionType[] | void> {
    if (processorFn) {
      await this._fetchPaginated(
        { method: HttpMethodEnum.GET, endpoint: WkEndpointEnum.TRANSACTIONS, params },
        processorFn
      );
      return;
    } else {
      return await this.fetch(HttpMethodEnum.GET, WkEndpointEnum.TRANSACTIONS, params);
    }
  }

  // ====================
  // Valuations
  // ====================

  /**
   * @description Retrieves the latest valuation of a portfolio. If the request is during the weekend, when
   * no valuations take place, it will return the last calculated valuation of Friday.
   * @param portfolioId
   */
  public async retrieveLatestValuationByPortfolio(portfolioId: string): Promise<ValuationType> {
    const startDate = WealthkernelService.getLatestCalculationDate();
    const endDate = startDate;

    const valuations = await this.retrieveValuations({ portfolioId, startDate, endDate });
    return valuations[0];
  }

  /**
   * @description Retrieves the valuations of all portfolios. Results can be filtered out by portfolio id,
   * start date or end date of valuation.
   * @param params
   */
  public async retrieveValuations(params: ValuationsQueryParamsType = {}): Promise<ValuationType[]> {
    return await this.fetch(HttpMethodEnum.GET, WkEndpointEnum.VALUATIONS, params);
  }

  /**
   * @description Retrieves the valuations of all portfolios. Results can be filtered out by portfolio id,
   * start date or end date of valuation.
   * @param params
   */
  public async retrieveAllValuations(params: ValuationsQueryParamsType = {}): Promise<ValuationType[]> {
    const allValuations: ValuationType[] = [];

    let paginationHeaderExists = true;
    let paginationHeader = "";

    while (paginationHeaderExists) {
      const paginationParams = { ...(params || {}), after: paginationHeader };

      const response = await this._fetchWithHeaders(
        HttpMethodEnum.GET,
        WkEndpointEnum.VALUATIONS,
        paginationParams
      );

      await delay(300); // Wait 250ms before requesting next page

      // Add the current page of valuations to our collection
      allValuations.push(...response.data);

      // Check if there are more pages
      paginationHeader = response.headers["pagination-last"];
      if (!paginationHeader) {
        paginationHeaderExists = false;
      }
    }

    return allValuations;
  }
  // ====================
  // Bonuses
  // ====================

  /**
   * @description Retrieves a bonus payment.
   * @param bonusId
   */
  public async retrieveBonus(bonusId: string): Promise<BonusType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.BONUSES}/${bonusId}`);
  }

  /**
   * @description Creates a bonus payment from the Wealthyhood portfolio to the given portfolio.
   * @param bonusData
   */
  public async createBonus(bonusData: BonusDataType): Promise<ResponseType> {
    const sourcePortfolio = process.env[`WEALTHKERNEL_WH_PORTFOLIO_ID_BONUS_${this.region}`];

    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.BONUSES, {}, { ...bonusData, sourcePortfolio });
  }

  // ====================
  // Mandates
  // ====================

  /**
   * Requests the creation a new mandate for the given party, authorizing payments from the given bank account.
   * @param mandateData
   * @param idempotencyKey
   */
  public async createMandate(mandateData: MandateDataType, idempotencyKey?: string): Promise<ResponseType> {
    const headerConfig = {
      "Idempotency-Key": idempotencyKey
    };

    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.MANDATES, {}, mandateData, headerConfig);
  }

  /**
   * @description Retrieves a mandate.
   * @param mandateId
   */
  public async retrieveMandate(mandateId: string): Promise<MandateType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.MANDATES}/${mandateId}`);
  }

  /**
   * Requests the cancellation of an existing mandate, cancelling all related cancellable subscriptions and payments.
   * @param mandateId
   */
  public async cancelMandate(mandateId: string): Promise<void> {
    await this.fetch(HttpMethodEnum.POST, `${WkEndpointEnum.MANDATES}/${mandateId}/actions/cancel`, {});
  }

  /**
   * @description Gets the next possible collection date for a payment given a mandate ID.
   * @param mandateId
   */
  public async retrieveMandateNextPossiblePaymentDate(mandateId: string): Promise<{ date: string }> {
    return await this.fetch(
      HttpMethodEnum.GET,
      `${WkEndpointEnum.MANDATES}/${mandateId}/next-possible-collection-date`
    );
  }

  // ====================
  // Direct Debit Payments
  // ====================

  /**
   * Requests the creation a new payment for a given direct debit mandate.
   * @param directDebitPaymentData
   */
  public async createDirectDebitPayment(
    directDebitPaymentData: DirectDebitPaymentDataType
  ): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.DIRECT_DEBIT_PAYMENTS, {}, directDebitPaymentData);
  }

  /**
   * Fetches the direct debit payment with the given ID.
   * @param paymentId
   */
  public async retrieveDirectDebitPayment(paymentId: string): Promise<DirectDebitPaymentType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.DIRECT_DEBIT_PAYMENTS}/${paymentId}`);
  }

  // ====================
  // Withdrawals
  // ====================

  /**
   * @description Creates a new withdrawal.
   * @param withdrawalData
   */
  public async requestWithdrawal(withdrawalData: WithdrawalDataType): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.WITHDRAWALS, {}, withdrawalData);
  }

  /**
   * @description Retrieves a withdrawal.
   * @param withdrawalId
   */
  public async retrieveWithdrawal(withdrawalId: string): Promise<WithdrawalRetrievalResponseType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.WITHDRAWALS}/${withdrawalId}`);
  }

  // ====================
  // CHARGES
  // ====================

  /**
   * @description List all charges matching the given params.
   * @param params
   */
  public async listCharges(params: ListChargesParamsType): Promise<ChargeType[]> {
    return await this.fetch(HttpMethodEnum.GET, WkEndpointEnum.CHARGES, params);
  }

  /**
   * @description Creates a new charge.
   * @param chargeData
   * @param idempotencyKey
   */
  public async createCharge(chargeData: ChargeDataType, idempotencyKey: string): Promise<ResponseType> {
    // TODO: remove string suffix once we retry failed charges
    const headerConfig = {
      "Idempotency-Key": `${idempotencyKey}-retry-1`
    };

    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.CHARGES, {}, chargeData, headerConfig);
  }

  /**
   * Fetches charge transaction with the given ID.
   * @param chargeId
   */
  public async retrieveCharge(chargeId: string): Promise<ChargeType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.CHARGES}/${chargeId}`);
  }

  // ====================
  // W-8BEN FORMS
  // ====================

  /**
   * @description Creates a new W-8BEN form.
   * @param w8BenFormData
   */
  public async createW8BenForm(w8BenFormData: W8BenFormDataType): Promise<ResponseType> {
    return await this.fetch(HttpMethodEnum.POST, WkEndpointEnum.W_8BEN, {}, { partyId: w8BenFormData.partyId });
  }

  /**
   * @description Completes a new W-8BEN form.
   * @param w8BenFormId
   * @param w8BenFormData
   */
  public async completeW8BenForm(
    w8BenFormId: string,
    w8BenFormData: W8BenFormCompletedDataType
  ): Promise<ResponseType> {
    return await this.fetch(
      HttpMethodEnum.POST,
      `${WkEndpointEnum.W_8BEN}/${w8BenFormId}/actions/complete`,
      {},
      { completedAt: w8BenFormData.completedAt }
    );
  }

  // ====================
  // INTERNAL TRANSFERS
  // ====================

  /**
   * @description Creates a new internal transfer between portfolios.
   * @param internalTransferData
   * @param idempotencyKey
   */
  public async createInternalTransfer(
    internalTransferData: InternalTransferDataType,
    idempotencyKey?: string
  ): Promise<ResponseType> {
    const headerConfig = {
      "Idempotency-Key": idempotencyKey
    };

    return await this.fetch(
      HttpMethodEnum.POST,
      WkEndpointEnum.INTERNAL_TRANSFERS,
      {},
      internalTransferData,
      headerConfig
    );
  }

  /**
   * @description Retrieves an internal transfer.
   * @param internalTransferId
   */
  public async retrieveInternalTransfer(internalTransferId: string): Promise<InternalTransferType> {
    return await this.fetch(HttpMethodEnum.GET, `${WkEndpointEnum.INTERNAL_TRANSFERS}/${internalTransferId}`);
  }

  /**
   * @description List all internal transfers matching the given params.
   * @param params
   */
  public async listInternalTransfers(params: ListInternalTransfersParamsType): Promise<InternalTransferType[]> {
    return await this.fetch(HttpMethodEnum.GET, WkEndpointEnum.INTERNAL_TRANSFERS, params);
  }

  /**
   * PRIVATE METHODS
   */

  /**
   * @description Returns a promise to indicate whether the API authentication is complete
   */
  private async _isReady(): Promise<boolean> {
    await this._authPromise;
    return Boolean(this._accessToken);
  }

  /**
   * @description Throws an error if any of env variables required to use the WK API, are not set.
   */
  private static _verifyEnvironmentVariablesExist(): void {
    Object.values(WealthkernelAccountRegionEnum).forEach((region) => {
      if (!process.env[`WEALTHKERNEL_CLIENT_ID_${region}`]) {
        throw new Error(`WEALTHKERNEL_CLIENT_ID_${region} is not set`);
      } else if (!process.env[`WEALTHKERNEL_CLIENT_SECRET_${region}`]) {
        throw new Error(`WEALTHKERNEL_CLIENT_SECRET_${region} is not set`);
      } else if (!process.env[`WEALTHKERNEL_WEBHOOK_SECRET_${region}`]) {
        throw new Error(`WEALTHKERNEL_WEBHOOK_SECRET_${region} is not set`);
      }
    });

    if (!WealthkernelService._authUrl) {
      throw new Error("WEALTHKERNEL_AUTH_URL env variable is not set");
    } else if (!WealthkernelService._serviceUrl) {
      throw new Error("WEALTHKERNEL_SERVICE_URL env variable is not set");
    }
  }

  /**
   * @description Method to fetch the access token that's used to authenticate WK API requests.
   *
   * 1. Retrieve the access token from the cache.
   * 1.5 If it doesn't exist, make a request to the WK API to get the access token.
   * 2. Poll every 5 minutes to get the latest access token.
   *
   * If the request fails, we retry after 1 minute.
   */
  private async _requestAuthToken(): Promise<void> {
    try {
      this._authPromise = getCachedDataWithFallback<AuthResponseType>(
        `wealthkernel:auth:${this._region.toLowerCase()}`,
        async () => {
          logger.info("Making http request", {
            module: "wealthkernelService",
            method: "_requestAuthToken",
            data: {
              clientId: this._clientId
            }
          });

          return (
            await axios.post(
              WealthkernelService._authUrl,
              qs.stringify({
                // eslint-disable-next-line camelcase
                grant_type: "client_credentials",
                // eslint-disable-next-line camelcase
                client_id: this._clientId,
                // eslint-disable-next-line camelcase
                client_secret: this._clientSecret,
                scope: "wk.gateway"
              })
            )
          ).data;
        },
        (response: AuthResponseType) => response.expires_in - TOKEN_CACHE_EXPIRATION_BUFFER
      );

      const authResponse = await this._authPromise;

      this._accessToken = authResponse.access_token;

      setTimeout(() => this._requestAuthToken(), TOKEN_POLLING_INTERVAL * 1000);
    } catch (err) {
      logger.error("Retrieval of wealthkernel auth token failed", {
        module: "wealthkernelService",
        method: "_requestAuthToken"
      });
      captureException(err);
      setTimeout(() => this._requestAuthToken(), TOKEN_ERROR_INTERVAL * 1000);
    }
  }

  /**
   * @description This is the method for making any requests to access the WK API, other than authentication.
   * It is configured to have the Bearer token in the header of the request.
   * @param method get or post
   * @param endpoint the WK endpoint that we want to access as defined in the endpoint enum
   * @param params query params of the request
   * @param data any data that may be posted with the request
   * @param headerConfig
   */
  private async fetch(
    method: HttpMethodEnum,
    endpoint: string,
    params: any = {},
    data?: any,
    headerConfig: RequestHeadersType = {}
  ): Promise<any> {
    const url = `${WealthkernelService._serviceUrl}/${endpoint}`;
    const requestId = randomUUID();

    try {
      await this._isReady();

      const headers: RequestHeadersType = {
        Authorization: `Bearer ${this._accessToken}`,
        "Accept-Version": API_VERSION,
        "Request-Id": requestId,
        ...headerConfig
      };

      if (!this._accessToken) {
        logger.warn("Access token missing", { module: "wealthkernelService", method: "fetch" });
      }
      logger.info("making http request", {
        module: "wealthkernelService",
        method: "fetch",
        data: {
          method,
          url,
          params,
          data,
          headers,
          clientId: this._clientId
        }
      });

      const response = await axiosInstance({
        method,
        url,
        headers,
        params,
        data
      });

      logger.info("http response", {
        module: "wealthkernelService",
        method: "fetch",
        data: response.data
      });

      return response.data;
    } catch (err) {
      logger.error("http request failed", {
        module: "wealthkernelService",
        method: "fetch",
        data: {
          requestId,
          clientId: this._clientId,
          reason: err.response ? JSON.stringify(err.response.data, null, 4) : "",
          error: err,
          requestData: {
            params,
            data: JSON.stringify(data, null, 4)
          }
        }
      });
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4),
          requestData: {
            params,
            data: JSON.stringify(data, null, 4)
          }
        }
      });
      captureException(err);
      throw new InternalServerError("Something went wrong with wealthkernel communication");
    }
  }

  /**
   * @description This is a method for fetching paginated requests.
   * - Makes request with and returns both data & headers
   * - Runs processor function on each document in the data array
   * - If header has pagination entry makes new request and repeats process
   *
   * @param options The fetch options that define method, endpoint, req params & req body
   * @param processorFn The function that is applied on each fetched document
   */
  private async _fetchPaginated(
    options: {
      method: HttpMethodEnum;
      endpoint: string;
      params: any;
      data?: any;
    },
    processorFn: (doc: any) => Promise<void>
  ): Promise<any> {
    let paginationHeaderExists = true;
    let paginationHeader = "";

    while (paginationHeaderExists) {
      const { method, endpoint, params, data } = options;

      const paginationParams = { ...(params || {}), after: paginationHeader };
      const response = (await this._fetchWithHeaders(method, endpoint, paginationParams, data)) as {
        data: any[];
        headers: ResponseHeadersType;
      };
      const documents = response.data;

      // For each fetched document apply processor function
      for (let i = 0; i < documents.length; i++) {
        const document = documents[i];
        await processorFn(document);
      }

      paginationHeader = response.headers["pagination-last"];
      if (!paginationHeader) {
        paginationHeaderExists = false;
      }
    }
  }

  private async _fetchWithHeaders(
    method: HttpMethodEnum,
    endpoint: string,
    params: any = {},
    data?: any
  ): Promise<{ data: any; headers: ResponseHeadersType }> {
    const url = `${WealthkernelService._serviceUrl}/${endpoint}`;
    const requestId = randomUUID();

    try {
      await this._isReady();

      const headers = {
        Authorization: `Bearer ${this._accessToken}`,
        "Accept-Version": API_VERSION,
        "Request-Id": requestId
      };

      if (!this._accessToken) {
        logger.warn("Access token missing", { module: "wealthkernelService", method: "fetch" });
      }
      logger.info("making http request", {
        module: "wealthkernelService",
        method: "_fetchWithHeaders",
        data: {
          method,
          url,
          params,
          data,
          headers
        }
      });

      const response = await axiosInstance({
        method,
        url,
        headers,
        params,
        data
      });

      logger.info("http response", {
        module: "wealthkernelService",
        method: "_fetchWithHeaders",
        data: response.data
      });

      return { data: response.data, headers: response.headers as ResponseHeadersType };
    } catch (err) {
      logger.error("http request failed", {
        module: "wealthkernelService",
        method: "_fetchWithHeaders",
        data: {
          requestId,
          reason: err.response ? JSON.stringify(err.response.data, null, 4) : "",
          error: err
        }
      });
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4)
        }
      });
      captureException(err);
      throw new InternalServerError("Something went wrong with wealthkernel communication");
    }
  }
}
