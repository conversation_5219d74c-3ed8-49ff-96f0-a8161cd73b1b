import request from "supertest";
import supertest from "supertest";
import { DateTime } from "luxon";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildBankAccount,
  buildCashbackTransaction,
  buildChargeTransaction,
  buildCreditTicket,
  buildDailyPortfolioTicker,
  buildDepositCashTransaction,
  buildGift,
  buildHoldingDTO,
  buildIntraDayAssetTicker,
  buildIntraDayPortfolioTicker,
  buildInvestmentProduct,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildReward,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildSubscription,
  buildTopUpAutomation,
  buildUser,
  buildValidObjectId
} from "../../tests/utils/generateModels";
import { KycStatusEnum, User, UserDocument } from "../../models/User";
import { GiftedHoldingType, Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import {
  entitiesConfig,
  giftsConfig,
  investmentsConfig,
  investmentUniverseConfig
} from "@wealthyhood/shared-configs";
import { faker } from "@faker-js/faker";
import app from "../../app";
import Decimal from "decimal.js";
import {
  AssetTransaction,
  AssetTransactionDocument,
  CashbackTransaction,
  DepositCashTransactionDocument,
  RebalanceTransaction,
  RebalanceTransactionDocument,
  SavingsTopupTransaction,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransaction,
  SavingsWithdrawalTransactionDocument,
  WithdrawalCashTransaction
} from "../../models/Transaction";
import { DepositMethodEnum } from "../../types/transactions";
import { Order, OrderDocument, OrderSubmissionIntentEnum } from "../../models/Order";
import {
  MIN_ALLOWED_ASSET_INVESTMENT,
  MIN_ALLOWED_ASSET_QUANTITY
} from "@wealthyhood/shared-configs/dist/investments";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { AssetArrayConst } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { SubscriptionDocument } from "../../models/Subscription";
import { ProviderEnum } from "../../configs/providersConfig";
import DateUtil from "../../utils/dateUtil";
import { RedisClientService } from "../../loaders/redis";
import { WealthkernelService } from "../../external-services/wealthkernelService";
import { DepositActionEnum } from "../../configs/depositsConfig";
import { FX_FEE_SPREADS_WH } from "../../configs/fxSpreadsConfig";

const { MIN_ALLOWED_INVESTMENT, MIN_ALLOWED_SAVINGS_INVESTMENT, MIN_ALLOWED_SAVINGS_WITHDRAWAL } =
  investmentsConfig;
const { RISK_CONFIG, ASSET_CONFIG } = investmentUniverseConfig;
const { RESTRICTED_HOLDING_PERIOD_DAYS } = giftsConfig;

describe("PortfolioRoutes", () => {
  beforeEach(() => jest.clearAllMocks());
  beforeAll(async () => await connectDb("PortfolioRoutes"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  describe("GET /portfolios", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    const PORTFOLIO_TICKER_PRICE = 200;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({
        holdings: [await buildHoldingDTO(true, "equities_us", 1, { price: PORTFOLIO_TICKER_PRICE })],
        owner: user.id,
        mode: PortfolioModeEnum.REAL
      });
      await buildIntraDayPortfolioTicker({
        portfolio: portfolio.id,
        pricePerCurrency: { GBP: PORTFOLIO_TICKER_PRICE }
      });
    });

    it("should return status 400 if mode is virtual", async () => {
      const response = await request(app)
        .get("/api/m2m/portfolios?mode=VIRTUAL")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    it("should return status 400 and for invalid mode", async () => {
      const response = await request(app)
        .get("/api/m2m/portfolios?mode=SDF#@$")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: expect.stringContaining("Param 'mode' has invalid value")
          }
        })
      );
    });

    it("should return status 200 and array of portfolios with filled prices when populateTicker is not set", async () => {
      const response = await request(app)
        .get("/api/m2m/portfolios")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const expectedData = (
        (await Portfolio.findById(portfolio.id).populate("owner")) as PortfolioDocument
      ).toJSON();

      expect(JSON.parse(response.text)).toEqual([
        expect.objectContaining({
          ...JSON.parse(JSON.stringify(expectedData)),
          currentTicker: expect.objectContaining({
            price: PORTFOLIO_TICKER_PRICE
          }),
          holdings: expect.arrayContaining([
            expect.objectContaining({
              asset: expect.objectContaining({
                currentTicker: expect.objectContaining({
                  price: PORTFOLIO_TICKER_PRICE
                })
              })
            })
          ])
        })
      ]);
    });

    it("should return status 200 and array of portfolios without filled prices when populateTicker is not set but portfolio has no current ticker", async () => {
      const user = await buildUser({ portfolioConversionStatus: "notStarted" });

      await buildPortfolio({
        holdings: [],
        owner: user.id,
        mode: PortfolioModeEnum.REAL
      });

      const response = await request(app)
        .get("/api/m2m/portfolios?sort=createdAt")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const expectedData = (
        await Portfolio.find({ owner: user.id }, null, { sort: { createdAt: 1 } }).populate("owner")
      ).map((portfolio) => portfolio.toJSON());

      const portfoliosReceived: PortfolioDocument[] = JSON.parse(response.text);
      expect(portfoliosReceived).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("should return status 200 and array of portfolios with filled prices when populateTicker is set to true", async () => {
      const response = await request(app)
        .get("/api/m2m/portfolios?populateTicker=true")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const expectedData = (
        (await Portfolio.findById(portfolio.id).populate("owner")) as PortfolioDocument
      ).toJSON();

      expect(JSON.parse(response.text)).toEqual([
        expect.objectContaining({
          ...JSON.parse(JSON.stringify(expectedData)),
          currentTicker: expect.objectContaining({
            price: PORTFOLIO_TICKER_PRICE
          }),
          calculatedPrice: PORTFOLIO_TICKER_PRICE,
          holdings: expect.arrayContaining([
            expect.objectContaining({
              asset: expect.objectContaining({
                currentTicker: expect.objectContaining({
                  price: PORTFOLIO_TICKER_PRICE
                })
              })
            })
          ])
        })
      ]);
    });

    it("should return status 200 and array of portfolios without filled prices when populateTicker is set to false", async () => {
      await buildPortfolio({ owner: user.id });
      await buildPortfolio({ owner: user.id });

      const response = await request(app)
        .get("/api/m2m/portfolios?sort=createdAt&populateTicker=false")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const expectedData = (await Portfolio.find({ owner: user.id }, null, { sort: { createdAt: 1 } })).map(
        (portfolio) => portfolio.toJSON()
      );
      const portfoliosReceived: PortfolioDocument[] = JSON.parse(response.text);

      expect(portfoliosReceived).toMatchObject(JSON.parse(JSON.stringify(expectedData)));

      const isArraySorted = (arr: any[]) => arr.slice(1).every((item, i) => arr[i] <= item);
      expect(isArraySorted(portfoliosReceived.map((portfolio) => new Date(portfolio.createdAt)))).toEqual(true);
    });
  });

  describe("GET /portfolios/:id", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL });
    });

    it("should return 403 when portfolio does not belong to user", async () => {
      // create another portfolio that will not be retrieved
      const otherPortfolio = await buildPortfolio();
      const response = await request(app)
        .get(`/api/m2m/portfolios/${otherPortfolio._id}`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(403);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: { description: "Inaccessible resource", message: "Portfolio does not belong to user" }
        })
      );
    });

    it("should return 400 when portfolio is not real", async () => {
      // create another portfolio that will not be retrieved
      const otherPortfolio = await buildPortfolio({ mode: "VIRTUAL", owner: user.id });
      const response = await request(app)
        .get(`/api/m2m/portfolios/${otherPortfolio._id}`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: expect.objectContaining({
            description: "Operation failed",
            message: "Portfolio given is not real"
          })
        })
      );
    });

    it("should return 200 and portfolio object", async () => {
      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio._id}`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const expectedData = (
        (await Portfolio.findById(portfolio.id).populate("owner")) as PortfolioDocument
      ).toJSON();
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });
  });

  describe("GET /portfolios/:id/available-holdings", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let subscription: SubscriptionDocument;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });

      const holdings = await Promise.all([
        // equities_uk is an available holding
        buildHoldingDTO(true, "equities_uk", 1, {
          price: 1
        }),
        // equities_us belongs to a pending sell order
        buildHoldingDTO(true, "equities_us", 1, {
          price: 1
        }),
        // equities_eu belongs to a restricted reward
        buildHoldingDTO(true, "equities_eu", 1, {
          price: 1
        }),
        // equities_china belongs to a restricted reward but part of it (0.1 quantity) has been sold in a charge transaction
        buildHoldingDTO(true, "equities_china", 0.9, {
          price: 1
        })
      ]);

      // create portfolio with holdings, pending sell orders, charge orders and restricted rewards
      [portfolio, subscription] = await Promise.all([
        buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL, holdings }),
        buildSubscription({ owner: user.id, price: "free_monthly", category: "FeeBasedSubscription" })
      ]);

      const [chargeTransaction, assetTransaction] = await Promise.all([
        buildChargeTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          chargeMethod: "holdings",
          subscription: subscription.id,
          status: "Settled",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        }),
        buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          portfolioTransactionCategory: "update"
        }),
        buildReward({
          targetUser: user.id,
          asset: "equities_eu",
          quantity: 1,
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          },
          unrestrictedAt: new Date(+new Date() + 24 * 60 * 60 * 1000) // One day after today
        }),
        buildReward({
          targetUser: user.id,
          asset: "equities_china",
          quantity: 1,
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          },
          unrestrictedAt: new Date(+new Date() + 24 * 60 * 60 * 1000) // One day after today
        })
      ]);

      await buildOrder({
        status: "Matched",
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
        side: "Sell",
        transaction: chargeTransaction.id,
        isin: ASSET_CONFIG["equities_china"].isin,
        quantity: 1
      });

      assetTransaction.orders = [
        await buildOrder({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          side: "Sell",
          transaction: chargeTransaction.id,
          isin: ASSET_CONFIG["equities_us"].isin,
          quantity: 1
        })
      ];
      await assetTransaction.save();

      // create another portfolio that will not be retrieved
      await buildPortfolio({ mode: "VIRTUAL" });
    });

    describe("when a portfolio that does not belong to user is passed", () => {
      it("should return status 403 with proper cause", async () => {
        // create another portfolio that will not be retrieved
        const otherPortfolio = await buildPortfolio();
        const response = await request(app)
          .get(`/api/m2m/portfolios/${otherPortfolio._id}/available-holdings`)
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(403);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "Inaccessible resource", message: "Portfolio does not belong to user" }
          })
        );
      });
    });

    describe("when a valid portfolio is passed", () => {
      it("should return status 200 and portfolio object", async () => {
        const response = await request(app)
          .get(`/api/m2m/portfolios/${portfolio._id}/available-holdings`)
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.arrayContaining([
            expect.objectContaining({
              asset: expect.objectContaining({
                currentTicker: expect.objectContaining({
                  price: 1
                })
              }),
              assetCommonId: "equities_uk",
              quantity: 1 // This is an asset without pending sell order or restricted rewards
            }),
            // There are pending sell orders for this asset, so it should be 0
            expect.not.objectContaining({
              assetCommonId: "equities_us"
            }),
            // This holding belongs to a restricted reward, so it should be 0
            expect.not.objectContaining({
              assetCommonId: "equities_eu"
            }),
            // This holding belongs to a restricted reward and also has a charge transaction deducted, but instead
            // of showing a negative value, we show 0
            expect.not.objectContaining({
              assetCommonId: "equities_china"
            })
          ])
        );
      });
    });
  });

  describe("GET /portfolios/:id/prices-by-tenor", () => {
    describe("when user has not passed portfolio ID", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      });
      afterAll(async () => await clearDb());

      it("should return 400", async () => {
        const response = await request(app)
          .get("/api/m2m/portfolios/prices-by-tenor")
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: expect.stringContaining("Portfolio does not exist")
            }
          })
        );
      });
    });

    describe("when user is not invested", () => {
      let owner: UserDocument;
      let portfolio: PortfolioDocument;

      const TODAY = new Date("2023-08-23");
      const data = [
        { timestamp: new Date("2023-08-16T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 16 Aug 2023
        { timestamp: new Date("2023-08-17T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 17 Aug 2023
        { timestamp: new Date("2023-08-18T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 18 Aug 2023
        { timestamp: new Date("2023-08-21T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 21 Aug 2023
        { timestamp: new Date("2023-08-22T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 22 Aug 2023
        { timestamp: new Date("2023-08-23T12:00:00.000Z").getTime(), value: 0, displayIntraday: false } // 23 Aug 2023
      ];

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        portfolio = await buildPortfolio({ owner: owner.id });
      });
      afterAll(async () => await clearDb());

      it("should return 200", async () => {
        const response = await request(app)
          .get(`/api/m2m/portfolios/${portfolio.id}/prices-by-tenor`)
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            "1w": {
              data
            },
            "1m": {
              data
            },
            "3m": {
              data
            },
            "6m": {
              data
            },
            "1y": {
              data
            },
            max: {
              data
            }
          })
        );
      });
    });

    describe("when user is invested for the last 3 days", () => {
      let owner: UserDocument;
      let portfolio: PortfolioDocument;

      const TODAY = new Date("2023-08-23T12:00:00.000Z");
      const oneWeekData = [
        { timestamp: new Date("2023-08-16T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 16 Aug 2023
        { timestamp: new Date("2023-08-17T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 17 Aug 2023
        { timestamp: new Date("2023-08-18T12:00:00.000Z").getTime(), value: 0, displayIntraday: false }, // 18 Aug 2023
        { timestamp: new Date("2023-08-21T12:00:00.000Z").getTime(), value: 10, displayIntraday: true }, // 21 Aug 2023
        { timestamp: new Date("2023-08-22T12:00:00.000Z").getTime(), value: 10, displayIntraday: true } // 22 Aug 2023
      ];
      const oneWeekDataDaily = oneWeekData.map(({ timestamp, value }) => ({
        timestamp: DateTime.fromJSDate(new Date(timestamp))
          .setZone("Europe/London", { keepLocalTime: true })
          .set({
            hour: 13,
            minute: 0
          })
          .toJSDate()
          .getTime(),
        value,
        displayIntraday: false
      }));

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        owner = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        portfolio = await buildPortfolio({
          owner: owner.id,
          holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })]
        });

        await Promise.all(
          // [0, 1, 2]
          [...Array(3).keys()].map(async (daysAgo) => {
            await Promise.all([
              buildDailyPortfolioTicker({
                portfolio: portfolio._id,
                date: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 10, EUR: 11, USD: 14 },
                currency: owner.currency
              }),
              buildIntraDayPortfolioTicker({
                portfolio: portfolio._id,
                timestamp: DateUtil.getDateOfDaysAgo(TODAY, daysAgo),
                pricePerCurrency: { GBP: 10, EUR: 11, USD: 14 },
                currency: owner.currency
              })
            ]);
          })
        );
      });
      afterAll(async () => await clearDb());

      it("should return 200", async () => {
        const response = await request(app)
          .get(`/api/m2m/portfolios/${portfolio.id}/prices-by-tenor`)
          .set("external-user-id", owner._id)
          .set("Accept", "application/json");

        expect(response.status).toBe(200);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            "1w": {
              data: [
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T12:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: true
                })
              ]
            },
            "1m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T12:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: true
                })
              ])
            },
            "3m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(), // 24/6 is weekend
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T12:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: false
                })
              ])
            },
            "6m": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2023-02-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-03-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-04-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T12:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: false
                })
              ])
            },
            "1y": {
              data: expect.arrayContaining([
                expect.objectContaining({
                  timestamp: new Date("2022-08-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-09-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-10-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-11-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2022-12-23T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-01-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-02-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-03-24T13:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-04-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-05-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-06-23T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                expect.objectContaining({
                  timestamp: new Date("2023-07-24T12:00:00.000Z").getTime(),
                  value: 0,
                  displayIntraday: false
                }),
                ...oneWeekDataDaily,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T12:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: false
                })
              ])
            },
            max: {
              data: [
                ...oneWeekData,
                expect.objectContaining({
                  timestamp: new Date("2023-08-23T12:00:00.000Z").getTime(),
                  value: 10,
                  displayIntraday: true
                })
              ]
            }
          })
        );
      });
    });
  });

  describe("POST /portfolios/:id/buy", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL });
      // create another portfolio that will not be retrieved
      await buildPortfolio({ mode: "VIRTUAL" });
    });

    it("should return status 400 when id references a VIRTUAL portfolio", async () => {
      const virtualPortfolio = await buildPortfolio({
        owner: user.id,
        mode: "VIRTUAL",
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${virtualPortfolio._id}/buy?allocationMethod=targetAllocation`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Portfolio given is not real"
          }
        })
      );
    });

    it("should return status 400 when orderAmount is missing", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({})
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'orderAmount' is required"
          }
        })
      );
    });

    it("should return status 400 when orderAmount is invalid", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount: "a@#$@ASD"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'orderAmount' , should be numeric"
          }
        })
      );
    });

    it("should return status 400 when orderAmount is more than the available cash", async () => {
      const orderAmount = 50;
      const available = 40;
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      const portfolio = await buildPortfolio({
        owner: user.id,
        holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })],
        cash: { GBP: { available, reserved: 0, settled: 0 } },
        mode: PortfolioModeEnum.REAL
      });
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You want to invest £50.00 buy you only have £40.00 deposited"
          }
        })
      );
    });

    it("should return status 400 when order amount is < MIN_ALLOWED_INVESTMENT", async () => {
      const orderAmount = MIN_ALLOWED_INVESTMENT - 0.01;
      const available = 40;
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      const portfolio = await buildPortfolio({
        owner: user.id,
        holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })],
        cash: { GBP: { available, reserved: 0, settled: 0 } },
        mode: PortfolioModeEnum.REAL
      });
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You need to invest at least £20.00."
          }
        })
      );
    });

    it("should return status 400 when allocationMethod=targetAllocation but user has no target allocation", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });

      const realPortfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } },
        initialHoldingsAllocation: [], // empty target allocation
        holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })]
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/buy?allocationMethod=targetAllocation`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Target allocation is not set up for this portfolio"
          }
        })
      );
    });

    it("should return status 400 when allocationMethod=holdings but user has no holdings", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });

      const realPortfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } },
        initialHoldingsAllocation: [], // empty target allocation
        holdings: [] // empty holdings
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Portfolio has no holdings"
          }
        })
      );
    });

    it("should succeed with status 200 when request is valid", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const createdAssetTransaction = (await AssetTransaction.findOne({
        status: "Pending"
      }).populate("orders")) as AssetTransactionDocument;
      const parsedTransaction = JSON.parse(response.text) as AssetTransactionDocument;

      expect(parsedTransaction).toMatchObject(JSON.parse(JSON.stringify(createdAssetTransaction)));
      expect(parsedTransaction).toEqual(
        expect.objectContaining({
          displayAmount: 5000,
          isCancellable: expect.anything(),
          executionWindow: expect.anything()
        })
      );
      expect(parsedTransaction.orders[0]).toEqual(
        expect.objectContaining({
          isCancellable: expect.anything(),
          executionWindow: expect.anything(),
          displayAmount: expect.anything(),
          displayQuantity: expect.anything()
        })
      );
    });

    it("should succeed with status 200 and user has a lifetime subscription", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id, price: "paid_low_lifetime_blackfriday_2023" });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const createdAssetTransaction = (await AssetTransaction.findOne({
        status: "Pending"
      }).populate("orders")) as AssetTransactionDocument;
      const parsedTransaction = JSON.parse(response.text) as AssetTransactionDocument;

      expect(parsedTransaction).toMatchObject(JSON.parse(JSON.stringify(createdAssetTransaction)));
      expect(parsedTransaction).toEqual(
        expect.objectContaining({
          displayAmount: 5000,
          isCancellable: expect.anything(),
          executionWindow: expect.anything()
        })
      );
      expect(parsedTransaction.orders[0]).toEqual(
        expect.objectContaining({
          isCancellable: expect.anything(),
          executionWindow: expect.anything(),
          displayAmount: expect.anything(),
          displayQuantity: expect.anything()
        })
      );

      const cashbacks = await CashbackTransaction.find({ linkedAssetTransaction: createdAssetTransaction.id });
      expect(cashbacks).toHaveLength(1);
    });

    it("should succeed with status 200 and submit stock orders to WK if we are within market hours", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      const TODAY = new Date("2023-10-30T15:00:00Z");

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_apple", quantity: 2, price: 10 },
        { assetId: "equities_microsoft", quantity: 2, price: 10 },
        { assetId: "equities_netflix", quantity: 2, price: 10 },
        { assetId: "equities_tesla", quantity: 2, price: 10 },
        { assetId: "equities_amazon", quantity: 2, price: 10 },
        { assetId: "equities_walmart", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const etfOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_china"].isin });
      expect(etfOrder).toEqual(
        expect.objectContaining({
          isSubmittedToBroker: false,
          submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
          fees: expect.objectContaining({
            realtimeExecution: {
              amount: 0,
              currency: "GBP"
            }
          })
        })
      );
      const stockOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_apple"].isin });
      expect(stockOrder).toEqual(
        expect.objectContaining({
          isSubmittedToBroker: true,
          providers: expect.objectContaining({
            wealthkernel: expect.objectContaining({
              id: STOCK_WK_ORDER_ID,
              status: "Pending"
            })
          }),
          submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
          fees: expect.objectContaining({
            realtimeExecution: {
              amount: 0,
              currency: "GBP"
            }
          })
        })
      );

      expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          aggregate: false,
          isin: ASSET_CONFIG["equities_apple"].isin,
          portfolioId: portfolio.providers.wealthkernel.id,
          side: "Buy"
        }),
        expect.anything()
      );
      expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(6);
    });

    it("should succeed with status 200 and not submit stock orders to WK if we are outside market hours", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      const TODAY = new Date("2023-10-30T11:00:00Z");

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_apple", quantity: 2, price: 10 },
        { assetId: "equities_microsoft", quantity: 2, price: 10 },
        { assetId: "equities_netflix", quantity: 2, price: 10 },
        { assetId: "equities_tesla", quantity: 2, price: 10 },
        { assetId: "equities_amazon", quantity: 2, price: 10 },
        { assetId: "equities_walmart", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const stockOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_apple"].isin });
      expect(stockOrder).toEqual(
        expect.objectContaining({
          isSubmittedToBroker: false
        })
      );

      expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
    });

    it("should succeed with status 200 and submit ETF orders to WK if we are within market hours and executeEtfOrdersInRealtime is set to true", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      const TODAY = new Date("2023-10-30T10:00:00Z");

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(WealthkernelService.EUInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?executeEtfOrdersInRealtime=true&allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const equitiesChinaOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_china"].isin });
      expect(equitiesChinaOrder.toObject()).toMatchObject({
        isSubmittedToBroker: true,
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 1,
            currency: "GBP"
          }
        })
      });

      const equitiesEuOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_eu"].isin });
      expect(equitiesEuOrder).toMatchObject({
        isSubmittedToBroker: true,
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 1,
            currency: "GBP"
          }
        })
      });

      expect(WealthkernelService.EUInstance.createOrder).toHaveBeenCalledTimes(2);
      expect(WealthkernelService.EUInstance.createOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          aggregate: false,
          isin: ASSET_CONFIG["equities_china"].isin,
          portfolioId: portfolio.providers.wealthkernel.id,
          side: "Buy"
        }),
        expect.anything()
      );
    });

    it("should succeed with status 200 and submit ETF orders to WK if we are within market hours and executeEtfOrdersInRealtime is unset and user is EU", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      const TODAY = new Date("2023-10-30T10:00:00Z");

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(WealthkernelService.EUInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const equitiesChinaOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_china"].isin });
      expect(equitiesChinaOrder.toObject()).toMatchObject({
        isSubmittedToBroker: true,
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 1,
            currency: "GBP"
          }
        })
      });

      const equitiesEuOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_eu"].isin });
      expect(equitiesEuOrder).toMatchObject({
        isSubmittedToBroker: true,
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 1,
            currency: "GBP"
          }
        })
      });

      expect(WealthkernelService.EUInstance.createOrder).toHaveBeenCalledTimes(2);
      expect(WealthkernelService.EUInstance.createOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          aggregate: false,
          isin: ASSET_CONFIG["equities_china"].isin,
          portfolioId: portfolio.providers.wealthkernel.id,
          side: "Buy"
        }),
        expect.anything()
      );
    });

    it("should succeed with status 200 and NOT submit ETF orders to WK if we are within market hours and executeEtfOrdersInRealtime is unset and user is UK", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      const TODAY = new Date("2023-10-30T10:00:00Z");

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=holdings`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const equitiesChinaOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_china"].isin });
      expect(equitiesChinaOrder.toObject()).toMatchObject({
        isSubmittedToBroker: false,
        submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 0,
            currency: "GBP"
          }
        })
      });

      const equitiesEuOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_eu"].isin });
      expect(equitiesEuOrder).toMatchObject({
        isSubmittedToBroker: false,
        submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 0,
            currency: "GBP"
          }
        })
      });

      expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
    });

    it("should succeed with status 200 and submit ETF orders to WK with amount > real-time execution fee", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      const TODAY = new Date("2023-10-30T10:00:00Z");

      Date.now = jest.fn(() => TODAY.valueOf());

      jest.spyOn(WealthkernelService.EUInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const user = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });

      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10, percentage: 1 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 99 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        })),
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(
          `/api/m2m/portfolios/${portfolio._id}/buy?executeEtfOrdersInRealtime=true&allocationMethod=targetAllocation`
        )
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const equitiesChinaOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_china"].isin });
      expect(equitiesChinaOrder).toMatchObject({
        isSubmittedToBroker: false,
        submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 0,
            currency: "GBP"
          }
        })
      });

      const equitiesEuOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_eu"].isin });
      expect(equitiesEuOrder).toMatchObject({
        isSubmittedToBroker: true,
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 1,
            currency: "GBP"
          }
        })
      });

      expect(WealthkernelService.EUInstance.createOrder).toHaveBeenCalledTimes(1);
      expect(WealthkernelService.EUInstance.createOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          aggregate: false,
          isin: ASSET_CONFIG["equities_eu"].isin,
          portfolioId: portfolio.providers.wealthkernel.id,
          side: "Buy"
        }),
        expect.anything()
      );
    });

    it("should succeed with status 200 when request is valid and is pending a gift", async () => {
      const gifter = await buildUser();
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const orderAmount = 20;
      const gift = await buildGift({
        gifter: gifter.id,
        targetUserEmail: user.email,
        consideration: {
          amount: Decimal.mul(orderAmount, 100).toNumber(),
          currency: "GBP"
        }
      });
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy?paymentMethod=gift&allocationMethod=holdings`)
        .send({
          orderAmount,
          gift: gift.id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const createdAssetTransaction = (await AssetTransaction.findOne({
        pendingGift: gift.id,
        status: "PendingGift"
      }).populate("orders pendingGift")) as AssetTransactionDocument;
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(createdAssetTransaction)));
    });

    it("should succeed with status 200, create an asset transaction and move user to inProgress when user has no holdings and is non-converted", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: false,
        portfolioConversionStatus: "notStarted"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10, percentage: 35 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 65 }
      ];

      const initialHoldingsAllocation = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(async (config) => ({
          assetCommonId: config.assetId,
          asset: await buildInvestmentProduct(true, {
            assetId: config.assetId,
            price: config.price
          }),
          percentage: config.percentage
        }))
      );

      const realPortfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } },
        initialHoldingsAllocation,
        holdings: []
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/buy?allocationMethod=targetAllocation`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const createdAssetTransaction = (await AssetTransaction.findOne({
        status: "Pending"
      }).populate("orders")) as AssetTransactionDocument;
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(createdAssetTransaction)));

      const updatedUser = (await User.findById(user.id)) as UserDocument;
      expect(updatedUser.portfolioConversionStatus).toEqual("inProgress");
    });

    it("should succeed with status 200 and create an asset transaction using the target allocation when user only has rewards", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      await buildReward({
        targetUser: user.id,
        asset: "equities_uk",
        quantity: 1,
        updatedAt: new Date(),
        status: "Settled",
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched"
            }
          }
        }
      });
      const TARGET_ALLOCATION_ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        percentage: number;
      }[] = [
        { assetId: "equities_china", percentage: 50 },
        { assetId: "equities_eu", percentage: 50 }
      ];

      await Promise.all(
        TARGET_ALLOCATION_ASSET_COMMON_IDS_CONFIG.map((config) => {
          return buildInvestmentProduct(true, { assetId: config.assetId });
        })
      );

      const realPortfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } },
        initialHoldingsAllocation: TARGET_ALLOCATION_ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        })),
        holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })]
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/buy?allocationMethod=targetAllocation`)
        .send({
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const transaction = (await AssetTransaction.findOne({ owner: user.id }).populate(
        "orders"
      )) as AssetTransactionDocument;
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(transaction)));

      expect(transaction.orders).toEqual(
        expect.arrayContaining([
          ...TARGET_ALLOCATION_ASSET_COMMON_IDS_CONFIG.map((config) => {
            return expect.objectContaining({
              isin: ASSET_CONFIG[config.assetId].isin
            });
          })
        ])
      );

      const updatedUser = (await User.findById(user.id)) as UserDocument;
      expect(updatedUser.portfolioConversionStatus).toEqual("completed");
    });

    describe("when request is valid with allocationMethod=targetAllocation", () => {
      let response: supertest.Response;
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10, percentage: 30 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
        { assetId: "real_estate_us", quantity: 2, price: 10, percentage: 40 }
      ];

      beforeEach(async () => {
        user = await buildUser({ kycStatus: "passed", portfolioConversionStatus: "completed" });
        await buildSubscription({ owner: user.id });

        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );
        portfolio = await buildPortfolio({
          owner: user.id,
          holdings,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          mode: PortfolioModeEnum.REAL,
          cash: { GBP: { available: 110, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        response = await request(app)
          .post(`/api/m2m/portfolios/${portfolio._id}/buy?allocationMethod=targetAllocation`)
          .send({ orderAmount: 100 })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
      });

      it("should return status 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should create one asset transaction of given amount", async () => {
        const transactions = await AssetTransaction.find({});
        expect(transactions.length).toBe(1);
        expect(transactions).toEqual([
          expect.objectContaining({
            consideration: {
              amount: 10000,
              currency: "GBP"
            }
          })
        ]);
      });
      it("should create valid number of buy orders with properly weighted amount", async () => {
        const orders = await Order.find({});
        expect(orders.length).toBe(3);

        const sortByIsin = (arr: any[]) => arr.sort((a, b) => (a.isin > b.isin ? 1 : -1));

        const expectedOrders = sortByIsin(
          ASSET_COMMON_IDS_CONFIG.map((config) => ({
            isin: ASSET_CONFIG[config.assetId].isin,
            side: "Buy",
            consideration: expect.objectContaining({
              originalAmount: config.percentage * 100,
              currency: "GBP"
            })
          }))
        );

        expect(sortByIsin(orders)).toEqual(expectedOrders.map((order) => expect.objectContaining(order)));
      });
    });

    describe("shouldPromptToMonthly flag", () => {
      let testUser: UserDocument;
      let testPortfolio: PortfolioDocument;

      beforeEach(async () => {
        // Create a test user with KYC and converted portfolio
        testUser = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          portfolioConversionStatus: "notStarted"
        });
        await buildSubscription({ owner: testUser.id });

        // Add investment products with prices for the assets
        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_apple", price: 150 }),
          buildInvestmentProduct(true, { assetId: "equities_microsoft", price: 200 })
        ]);

        // Create a real portfolio for testing with cash
        testPortfolio = await buildPortfolio({
          owner: testUser.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: {
              available: 5000,
              settled: 5000,
              reserved: 0
            }
          },
          isTargetAllocationSetup: true,
          initialHoldingsAllocation: [
            { assetCommonId: "equities_apple", percentage: 60 },
            { assetCommonId: "equities_microsoft", percentage: 40 }
          ]
        });
      });

      afterEach(async () => {
        await clearDb();
      });

      it("should be true when conditions are met (first transaction, no automation, no gift)", async () => {
        const response = await request(app)
          .post(`/api/m2m/portfolios/${testPortfolio._id}/buy?allocationMethod=targetAllocation`)
          .send({
            orderAmount: 100
          })
          .set("external-user-id", testUser._id)
          .set("Accept", "application/json");
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty("shouldPromptToMonthly", true);
      });

      it("should be false when user has active repeating automations", async () => {
        // Create an active automation using helper method
        await buildTopUpAutomation({
          owner: testUser.id,
          portfolio: testPortfolio.id,
          frequency: "monthly",
          active: true,
          nextCollectionAt: new Date(Date.now() + 86400000), // tomorrow
          amount: 100
        });

        const response = await request(app)
          .post(`/api/m2m/portfolios/${testPortfolio._id}/buy?allocationMethod=targetAllocation`)
          .set("external-user-id", testUser._id)
          .set("Accept", "application/json")
          .send({ orderAmount: 100 });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty("shouldPromptToMonthly", false);
      });

      it("should be false when this is not the first transaction", async () => {
        // Create a previous transaction using helper method
        const previousTransaction = await buildAssetTransaction({
          owner: testUser.id,
          portfolio: testPortfolio.id,
          status: "Settled",
          portfolioTransactionCategory: "buy",
          consideration: {
            amount: 100000, // £1000 in cents
            currency: "GBP"
          },
          originalInvestmentAmount: 100000,
          createdAt: new Date(Date.now() - 604800000) // 7 days ago
        });

        // Create an order for the transaction
        await buildOrder({
          transaction: previousTransaction.id,
          status: "Settled",
          side: "Buy",
          isin: "IE00B4L5Y983",
          quantity: 5,
          consideration: {
            amount: 100000,
            currency: "GBP"
          }
        });

        const response = await request(app)
          .post(`/api/m2m/portfolios/${testPortfolio._id}/buy?allocationMethod=targetAllocation`)
          .set("external-user-id", testUser._id)
          .set("Accept", "application/json")
          .send({ orderAmount: 100 });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty("shouldPromptToMonthly", false);
      });

      it("should be false when gift is used", async () => {
        // Create a gift using helper method
        const gift = await buildGift({
          targetUserEmail: testUser.email,
          consideration: {
            amount: 10000, // £100 in cents
            currency: "GBP"
          },
          status: "Pending"
        });

        const response = await request(app)
          .post(`/api/m2m/portfolios/${testPortfolio._id}/buy?allocationMethod=targetAllocation`)
          .set("external-user-id", testUser._id)
          .set("Accept", "application/json")
          .query({ paymentMethod: "gift" })
          .send({
            orderAmount: 100,
            gift: gift.id
          });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty("shouldPromptToMonthly", false);
      });
    });
  });

  describe("POST /portfolios/:id/invest-pending-deposit", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      await buildSubscription({ owner: user.id });
      portfolio = await buildPortfolio({ owner: user.id, mode: PortfolioModeEnum.REAL });
      // create another portfolio that will not be retrieved
      await buildPortfolio({ mode: "VIRTUAL" });
      jest.spyOn(eventEmitter, "emit");
    });

    it("should return status 400 when id references a VIRTUAL portfolio", async () => {
      const virtualPortfolio = await buildPortfolio({
        owner: user.id,
        mode: "VIRTUAL",
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${virtualPortfolio._id}/invest-pending-deposit?allocationMethod=holdings`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Portfolio given is not real"
          }
        })
      );
    });

    it("should return status 400 for body param orderAmount missing", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?allocationMethod=holdings`)
        .send({
          pendingDeposit: buildValidObjectId()
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'orderAmount' is required"
          }
        })
      );
    });

    it("should return status 400 for body param orderAmount not numeric", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?allocationMethod=holdings`)
        .send({
          orderAmount: "a@#$@ASD",
          pendingDeposit: buildValidObjectId()
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'orderAmount' , should be numeric"
          }
        })
      );
    });

    it("should return status 400 for body param pendingDeposit missing", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?allocationMethod=holdings`)
        .send({
          orderAmount: "50"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'pendingDeposit' is required"
          }
        })
      );
    });

    it("should return status 400 for body param pendingDeposit not ObjectId", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?allocationMethod=holdings`)
        .send({
          orderAmount: "50",
          pendingDeposit: "144"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for 'pendingDeposit'"
          }
        })
      );
    });

    it("should return status 400 for orderAmount < MIN_ALLOWED_INVESTMENT", async () => {
      const orderAmount = MIN_ALLOWED_INVESTMENT - 0.01;
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      const portfolio = await buildPortfolio({
        owner: user.id,
        holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })],
        mode: PortfolioModeEnum.REAL
      });
      const deposit = await buildDepositCashTransaction(
        {
          consideration: { amount: Decimal.mul(orderAmount, 100).toNumber(), currency: "GBP" }
        },
        user,
        portfolio
      );
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?allocationMethod=holdings`)
        .send({
          orderAmount: orderAmount,
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You need to invest at least £20.00."
          }
        })
      );
    });

    it("should return status 400 for a pending deposit that is not of same value as the order amount", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })],
        owner: user.id,
        cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
      });
      const deposit = await buildDepositCashTransaction(
        {
          consideration: { amount: 4000, currency: "GBP" }
        },
        user,
        portfolio
      );
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?allocationMethod=holdings`)
        .send({
          orderAmount: orderAmount,
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You want to 1-step deposit & invest £50.00 but you deposited £40.00"
          }
        })
      );
    });

    it("should return status 400 when allocationMethod=targetAllocation but user has no target allocation", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const realPortfolio = await buildPortfolio({
        owner: user.id,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } },
        initialHoldingsAllocation: [], // empty target allocation
        holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })]
      });
      const orderAmount = 50;

      const deposit = await buildDepositCashTransaction(
        {
          consideration: { amount: 5000, currency: "GBP" }
        },
        user,
        realPortfolio
      );

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/invest-pending-deposit?allocationMethod=targetAllocation`)
        .send({
          orderAmount: orderAmount,
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Target allocation is not set up for this portfolio"
          }
        })
      );
    });

    it("should succeed with status 200 and create an asset transaction that is pending deposit", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 0, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;
      const deposit = await buildDepositCashTransaction(
        {
          consideration: { amount: 5000, currency: "GBP" }
        },
        user,
        portfolio
      );

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?allocationMethod=holdings`)
        .send({
          orderAmount: orderAmount,
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const assetTransactions = (await AssetTransaction.find({ owner: user.id }).populate(
        "orders pendingDeposit"
      )) as AssetTransactionDocument[];
      const assetTransaction = assetTransactions[0];
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(assetTransaction)));

      expect(assetTransaction.status).toEqual("PendingDeposit");
      expect((assetTransaction.pendingDeposit as DepositCashTransactionDocument).id.toString()).toEqual(
        deposit.id.toString()
      );
      const parsedTransaction = JSON.parse(response.text) as AssetTransactionDocument;

      expect(parsedTransaction).toEqual(
        expect.objectContaining({
          isCancellable: expect.anything(),
          executionWindow: expect.anything()
        })
      );
      expect(parsedTransaction.orders[0]).toEqual(
        expect.objectContaining({
          isCancellable: expect.anything(),
          executionWindow: expect.anything(),
          displayAmount: expect.anything(),
          displayQuantity: expect.anything()
        })
      );
      expect(eventEmitter.emit).toBeCalledTimes(0);
    });

    it("should succeed with status 200 and create an asset transaction that is pending deposit for a user that is investing for first time", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: false,
        portfolioConversionStatus: "notStarted"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10, percentage: 35 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 65 }
      ];
      const initialHoldingsAllocation = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(async (config) => ({
          assetCommonId: config.assetId,
          asset: await buildInvestmentProduct(true, {
            assetId: config.assetId,
            price: config.price
          }),
          percentage: config.percentage
        }))
      );
      const realPortfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        initialHoldingsAllocation,
        holdings: []
      });
      const orderAmount = 50;
      const deposit = await buildDepositCashTransaction(
        {
          consideration: { amount: 5000, currency: "GBP" }
        },
        user,
        realPortfolio
      );

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/invest-pending-deposit?allocationMethod=targetAllocation`)
        .send({
          orderAmount: orderAmount,
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const assetTransactions = (await AssetTransaction.find({ owner: user.id }).populate(
        "orders pendingDeposit"
      )) as AssetTransactionDocument[];
      const assetTransaction = assetTransactions[0];
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(assetTransaction)));

      expect(assetTransaction.status).toEqual("PendingDeposit");
      expect((assetTransaction.pendingDeposit as DepositCashTransactionDocument).id.toString()).toEqual(
        deposit.id.toString()
      );

      expect(eventEmitter.emit).toBeCalledTimes(0);
    });

    it("should succeed with status 200 and create an asset transaction using the target allocation when user only has rewards", async () => {
      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      await buildReward({
        targetUser: user.id,
        asset: "equities_uk",
        quantity: 1,
        updatedAt: new Date(),
        status: "Settled",
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        },
        order: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched"
            }
          }
        }
      });
      const TARGET_ALLOCATION_ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        percentage: number;
      }[] = [
        { assetId: "equities_china", percentage: 50 },
        { assetId: "equities_eu", percentage: 50 }
      ];

      await Promise.all(
        TARGET_ALLOCATION_ASSET_COMMON_IDS_CONFIG.map((config) => {
          return buildInvestmentProduct(true, { assetId: config.assetId });
        })
      );

      const deposit = await buildDepositCashTransaction(
        {
          consideration: { amount: 5000, currency: "GBP" }
        },
        user,
        portfolio
      );

      const initialHoldingsAllocation = await Promise.all(
        TARGET_ALLOCATION_ASSET_COMMON_IDS_CONFIG.map(async (config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        }))
      );

      const realPortfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
        initialHoldingsAllocation,
        holdings: [await buildHoldingDTO(true, "equities_uk", 1, { price: 10 })]
      });
      const orderAmount = 50;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/invest-pending-deposit?allocationMethod=targetAllocation`)
        .send({
          orderAmount: orderAmount,
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const assetTransactions = (await AssetTransaction.find({ owner: user.id }).populate(
        "orders pendingDeposit"
      )) as AssetTransactionDocument[];
      const assetTransaction = assetTransactions[0];
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(assetTransaction)));

      expect(assetTransaction.orders).toEqual(
        expect.arrayContaining([
          ...TARGET_ALLOCATION_ASSET_COMMON_IDS_CONFIG.map((config) => {
            return expect.objectContaining({
              isin: ASSET_CONFIG[config.assetId].isin
            });
          })
        ])
      );

      const updatedUser = (await User.findById(user.id)) as UserDocument;
      expect(updatedUser.portfolioConversionStatus).toEqual("completed");
    });

    it("should succeed with status 200 and NOT submit stock orders to WK even if we are within market hours", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      const TODAY = new Date("2023-10-30T15:00:00Z");

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_apple", quantity: 2, price: 10 },
        { assetId: "equities_microsoft", quantity: 2, price: 10 },
        { assetId: "equities_netflix", quantity: 2, price: 10 },
        { assetId: "equities_tesla", quantity: 2, price: 10 },
        { assetId: "equities_amazon", quantity: 2, price: 10 },
        { assetId: "equities_walmart", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const deposit = await buildDepositCashTransaction(
        {
          consideration: { amount: 5000, currency: "GBP" }
        },
        user,
        portfolio
      );

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?allocationMethod=holdings`)
        .send({
          orderAmount: orderAmount,
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const etfOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_china"].isin });
      expect(etfOrder).toEqual(
        expect.objectContaining({
          isSubmittedToBroker: false
        })
      );
      const stockOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_apple"].isin });
      expect(stockOrder).toEqual(
        expect.objectContaining({
          isSubmittedToBroker: false
        })
      );

      expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
    });

    it("should succeed with status 200 and NOT submit ETF orders to WK even if we are within market hours", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      const TODAY = new Date("2023-10-30T10:00:00Z");

      Date.now = jest.fn(() => TODAY.valueOf());
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const user = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        hasConvertedPortfolio: true,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      const portfolio = await buildPortfolio({
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        owner: user.id,
        holdings,
        cash: { GBP: { available: 51, reserved: 0, settled: 0 } }
      });
      const orderAmount = 50;

      const deposit = await buildDepositCashTransaction(
        {
          consideration: { amount: 5000, currency: "GBP" }
        },
        user,
        portfolio
      );

      const response = await request(app)
        .post(
          `/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?executeEtfOrdersInRealtime=true&allocationMethod=holdings`
        )
        .send({
          orderAmount: orderAmount,
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const equitiesChinaOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_china"].isin });
      expect(equitiesChinaOrder).toMatchObject({
        isSubmittedToBroker: false,
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 1,
            currency: "GBP"
          }
        })
      });

      const equitiesEuOrder = await Order.findOne({ isin: ASSET_CONFIG["equities_eu"].isin });
      expect(equitiesEuOrder).toMatchObject({
        isSubmittedToBroker: false,
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME,
        fees: expect.objectContaining({
          realtimeExecution: {
            amount: 1,
            currency: "GBP"
          }
        })
      });

      expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledTimes(0);
    });

    describe("when request is valid with allocationMethod=targetAllocation", () => {
      let response: supertest.Response;
      let user: UserDocument;
      let portfolio: PortfolioDocument;
      let deposit: DepositCashTransactionDocument;

      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10, percentage: 30 },
        { assetId: "equities_eu", quantity: 2, price: 10, percentage: 30 },
        { assetId: "real_estate_us", quantity: 2, price: 10, percentage: 40 }
      ];

      beforeEach(async () => {
        user = await buildUser({ kycStatus: "passed", portfolioConversionStatus: "completed" });
        await buildSubscription({ owner: user.id });

        const holdings = await Promise.all(
          ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
            buildHoldingDTO(true, assetId, quantity, { price })
          )
        );
        portfolio = await buildPortfolio({
          owner: user.id,
          holdings,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          mode: PortfolioModeEnum.REAL,
          cash: { GBP: { available: 110, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        deposit = await buildDepositCashTransaction(
          {
            consideration: { amount: 10000, currency: "GBP" }
          },
          user,
          portfolio
        );

        response = await request(app)
          .post(`/api/m2m/portfolios/${portfolio._id}/invest-pending-deposit?allocationMethod=targetAllocation`)
          .send({
            orderAmount: 100,
            pendingDeposit: deposit._id
          })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
      });

      it("should return status 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should create one asset transaction of given amount", async () => {
        const transactions = await AssetTransaction.find({});
        expect(transactions.length).toBe(1);
        expect(transactions).toEqual([
          expect.objectContaining({
            consideration: {
              amount: 10000,
              currency: "GBP"
            }
          })
        ]);
      });
      it("should create valid number of buy orders with properly weighted amount", async () => {
        const orders = await Order.find({});
        expect(orders.length).toBe(3);

        const sortByIsin = (arr: any[]) => arr.sort((a, b) => (a.isin > b.isin ? 1 : -1));

        const expectedOrders = sortByIsin(
          ASSET_COMMON_IDS_CONFIG.map((config) => ({
            isin: ASSET_CONFIG[config.assetId].isin,
            side: "Buy",
            consideration: expect.objectContaining({
              originalAmount: config.percentage * 100,
              currency: "GBP"
            })
          }))
        );

        expect(sortByIsin(orders)).toEqual(expectedOrders.map((order) => expect.objectContaining(order)));
      });
    });
  });

  describe("POST /portfolios/:id/rebalance", () => {
    let user: UserDocument;
    let realPortfolio: PortfolioDocument;
    let nonRealReportfolio: PortfolioDocument;

    beforeEach(async () => {
      jest.spyOn(eventEmitter, "emit");

      user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      await buildSubscription({ owner: user.id });

      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 3, price: 10, percentage: 25 },
        { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
        { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
        { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );

      [realPortfolio, nonRealReportfolio] = await Promise.all([
        buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          holdings,
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        }),
        buildPortfolio({
          owner: user.id,
          mode: "VIRTUAL",
          initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.concat([
            { assetId: "equities_us", quantity: 1, price: 26, percentage: 30 }
          ]).map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        })
      ]);

      await buildIntraDayPortfolioTicker({
        portfolio: realPortfolio._id,
        timestamp: new Date(),
        pricePerCurrency: { GBP: 100 }
      });
    });

    it("POST /portfolios/:id/rebalance with a portfolio that is not real should fail with 400", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${nonRealReportfolio._id}/rebalance`)
        .send({})
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Portfolio given is not real"
          }
        })
      );
    });

    it("POST /portfolios/:id/rebalance with a portfolio whose owner does not also have a virtual portfolio should return status 200 and create a rebalance transaction", async () => {
      const TODAY = new Date("2022-07-17T11:00:00Z");
      Date.now = jest.fn(() => +TODAY);

      const userWithOnlyRealPortfolio = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        portfolioConversionStatus: "completed"
      });
      const aRealPortfolio = await buildPortfolio({
        owner: userWithOnlyRealPortfolio.id,
        mode: PortfolioModeEnum.REAL
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${aRealPortfolio._id}/rebalance`)
        .send({})
        .set("external-user-id", userWithOnlyRealPortfolio._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const rebalanceTransactionResponse = JSON.parse(response.text);
      const rebalanceTransaction = (await RebalanceTransaction.findOne({
        owner: userWithOnlyRealPortfolio._id
      }).populate("owner")) as RebalanceTransactionDocument;
      expect(rebalanceTransactionResponse).toMatchObject(JSON.parse(JSON.stringify(rebalanceTransaction)));
      expect(rebalanceTransaction).toEqual(
        expect.objectContaining({
          category: "RebalanceTransaction",
          portfolio: aRealPortfolio._id,
          targetAllocation: expect.arrayContaining([
            ...aRealPortfolio.initialHoldingsAllocation.map((allocation) => {
              return expect.objectContaining({
                percentage: allocation.percentage,
                assetCommonId: allocation.assetCommonId
              });
            })
          ])
        })
      );
      expect(rebalanceTransaction.owner.id.toString()).toEqual(userWithOnlyRealPortfolio._id.toString());
      // reparse rebalance transaction to stringify dates
      const reparsedRebalanceTransaction = JSON.parse(JSON.stringify(rebalanceTransaction));
      expect(reparsedRebalanceTransaction.buyExecutionWindow).toMatchObject({
        start: "2022-07-19T12:00:00.000Z",
        end: "2022-07-19T17:00:00.000Z"
      });
      expect(reparsedRebalanceTransaction.sellExecutionWindow).toMatchObject({
        start: "2022-07-18T12:00:00.000Z",
        end: "2022-07-18T17:00:00.000Z"
      });
    });

    it("POST /portfolios/:id/rebalance with a portfolio whose owner has not passed KYC should fail with 400", async () => {
      const userWithNotPassedKyc = await buildUser({ kycStatus: KycStatusEnum.FAILED });
      const aRealPortfolio = await buildPortfolio({ owner: userWithNotPassedKyc.id });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${aRealPortfolio._id}/rebalance`)
        .send({})
        .set("external-user-id", userWithNotPassedKyc._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "User has not passed kyc"
          }
        })
      );
    });

    it("POST /portfolios/:id/rebalance with a portfolio that already has a settled rebalance transaction should not fail with 400", async () => {
      await buildRebalanceTransaction({
        owner: user.id,
        portfolio: realPortfolio.id,
        rebalanceStatus: "Settled"
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/rebalance`)
        .send({})
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).not.toEqual(400);
    });

    it("POST /portfolios/:id/rebalance with a portfolio whose owner is not the same as the one passed in headers should fail with 403", async () => {
      const someOtherUser = await buildUser({
        kycStatus: KycStatusEnum.PASSED,
        portfolioConversionStatus: "completed"
      });
      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/rebalance`)
        .send({})
        .set("external-user-id", someOtherUser._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(403);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Inaccessible resource",
            message: "Portfolio does not belong to user"
          }
        })
      );
    });

    it("POST /portfolios/:id/rebalance with a valid portfolio should return status 200 and create a rebalance transaction based on the allocation of the real portfolio", async () => {
      const TODAY = new Date("2022-07-17T11:00:00Z");
      Date.now = jest.fn(() => +TODAY);

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/rebalance`)
        .send({})
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const rebalanceTransactionResponse = JSON.parse(response.text);
      const rebalanceTransaction = (await RebalanceTransaction.findOne({
        owner: user._id
      }).populate("owner")) as RebalanceTransactionDocument;
      expect(rebalanceTransactionResponse).toMatchObject(JSON.parse(JSON.stringify(rebalanceTransaction)));
      expect(rebalanceTransaction).toEqual(
        expect.objectContaining({
          category: "RebalanceTransaction",
          portfolio: realPortfolio._id,
          targetAllocation: expect.arrayContaining([
            ...realPortfolio.initialHoldingsAllocation.map((allocation) => {
              return expect.objectContaining({
                percentage: allocation.percentage,
                assetCommonId: allocation.assetCommonId
              });
            })
          ])
        })
      );
      expect(rebalanceTransaction.owner.id.toString()).toEqual(user._id.toString());
      // reparse rebalance transaction to stringify dates
      const reparsedRebalanceTransaction = JSON.parse(JSON.stringify(rebalanceTransaction));
      expect(reparsedRebalanceTransaction.buyExecutionWindow).toMatchObject({
        start: "2022-07-19T12:00:00.000Z",
        end: "2022-07-19T17:00:00.000Z"
      });
      expect(reparsedRebalanceTransaction.sellExecutionWindow).toMatchObject({
        start: "2022-07-18T12:00:00.000Z",
        end: "2022-07-18T17:00:00.000Z"
      });
      // the target allocation should not equal the outdated allocation of the virtual portfolio
      expect(rebalanceTransaction).not.toEqual(
        expect.objectContaining({
          category: "RebalanceTransaction",
          portfolio: realPortfolio._id,
          targetAllocation: expect.arrayContaining([
            ...nonRealReportfolio.initialHoldingsAllocation.map((allocation) => {
              return expect.objectContaining({
                percentage: allocation.percentage,
                assetCommonId: allocation.assetCommonId
              });
            })
          ])
        })
      );
    });
  });

  describe("GET /portfolios/:id/is-imbalanced", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      jest.spyOn(eventEmitter, "emit");

      user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      await buildSubscription({ owner: user.id });
    });

    it("should succeed with status 200 for balanced (unverified user)", async () => {
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 1, price: 20, percentage: 25 },
        { assetId: "equities_global", quantity: 1, price: 20, percentage: 25 },
        { assetId: "equities_uk", quantity: 1, price: 27, percentage: 30 },
        { assetId: "equities_eu", quantity: 1, price: 17, percentage: 19 },
        { assetId: "equities_us", quantity: 0, price: 17, percentage: 1 },
        { assetId: "equities_jp", quantity: 2, price: 1, percentage: 0 }
      ];

      const unverifiedUser = await buildUser({ kycStatus: "failed" });
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      portfolio = await buildPortfolio({
        owner: unverifiedUser._id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        })),
        holdings
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio._id}/is-imbalanced`)
        .set("external-user-id", unverifiedUser._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        isImbalanced: false
      });
    });

    it("should succeed with status 200 for balanced portfolio (no available holdings)", async () => {
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 2.5, price: 10, percentage: 25 },
        { assetId: "equities_global", quantity: 2.5, price: 10, percentage: 25 },
        { assetId: "equities_uk", quantity: 1, price: 30, percentage: 30 },
        { assetId: "equities_eu", quantity: 1, price: 20, percentage: 20 }
      ];

      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        })),
        holdings: []
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio._id}/is-imbalanced`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        isImbalanced: false
      });
    });

    it("should succeed with status 200 for balanced portfolio", async () => {
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 2.5, price: 10, percentage: 25 },
        { assetId: "equities_global", quantity: 2.5, price: 10, percentage: 25 },
        { assetId: "equities_uk", quantity: 1, price: 30, percentage: 30 },
        { assetId: "equities_eu", quantity: 1, price: 20, percentage: 20 }
      ];

      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity + 0.0001, { price })
        )
      );
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        })),
        holdings
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio._id}/is-imbalanced`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        isImbalanced: false
      });
    });

    it("should succeed with status 200 for imbalanced portfolio with average allocation diff >= 1", async () => {
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 1, price: 20, percentage: 25 },
        { assetId: "equities_global", quantity: 1, price: 20, percentage: 25 },
        { assetId: "equities_uk", quantity: 1, price: 27, percentage: 30 },
        { assetId: "equities_eu", quantity: 1, price: 17, percentage: 19 },
        { assetId: "equities_us", quantity: 0, price: 17, percentage: 1 },
        { assetId: "equities_jp", quantity: 2, price: 1, percentage: 0 }
      ];

      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        })),
        holdings
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio._id}/is-imbalanced`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        isImbalanced: true
      });
    });

    it("should succeed with status 200 for imbalanced portfolio with average allocation diff < 1 and an asset allocation diff >= 3", async () => {
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
        percentage: number;
      }[] = [
        { assetId: "equities_china", quantity: 1, price: 14.5, percentage: 15 },
        { assetId: "equities_global", quantity: 1, price: 14.5, percentage: 15 },
        { assetId: "equities_uk", quantity: 1, price: 33, percentage: 30 },
        { assetId: "equities_eu", quantity: 1, price: 9.5, percentage: 10 },
        { assetId: "equities_jp", quantity: 1, price: 9.5, percentage: 10 },
        { assetId: "equities_us", quantity: 1, price: 9.5, percentage: 10 },
        { assetId: "equities_global_ai", quantity: 1, price: 9.5, percentage: 10 }
      ];

      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        initialHoldingsAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
          assetCommonId: config.assetId,
          percentage: config.percentage
        })),
        holdings
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio._id}/is-imbalanced`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        isImbalanced: true
      });
    });
  });

  describe("GET /portfolios/:id/pending-cash-flows", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let assetTransactionForCashbacks: AssetTransactionDocument;

    beforeEach(async () => {
      jest.spyOn(eventEmitter, "emit");

      user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({ owner: user.id });
      assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });
    });
    afterEach(async () => {
      await clearDb();
    });

    it("should succeed with status 200 for an invested user", async () => {
      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio._id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(response.body).toEqual([]);
    });

    it("should fail with a 400 for a portfolio that does not exist", async () => {
      const response = await request(app)
        .get(`/api/m2m/portfolios/${1231}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    it("should return pending deposits and cashbacks", async () => {
      const [
        saltedgeBasedDeposit,
        bankTransferDeposit,
        executedTruelayerDeposit,
        anotherTruelayerExecutedDeposit,
        cashback
      ] = await Promise.all([
        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            saltedge: {
              id: faker.string.uuid(),
              customId: faker.string.uuid(),
              status: "accepted"
            }
          },
          portfolio: portfolio.id
        }),
        buildDepositCashTransaction({
          owner: user.id,
          providers: null,
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          portfolio: portfolio.id
        }),
        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            }
          },
          portfolio: portfolio.id
        }),
        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            }
          },
          portfolio: portfolio.id
        }),
        buildCashbackTransaction({
          status: "Pending",
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransactionForCashbacks.id
        })
      ]);

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      const responseIds = response.body.map((item: any) => item._id);

      expect(response.status).toEqual(200);
      expect(responseIds).toContain(saltedgeBasedDeposit.id);
      expect(responseIds).toContain(bankTransferDeposit.id);
      expect(responseIds).toContain(executedTruelayerDeposit.id);
      expect(responseIds).toContain(anotherTruelayerExecutedDeposit.id);
      expect(responseIds).toContain(cashback.id);
    });

    it("should not return settled deposits or cashbacks", async () => {
      await Promise.all([
        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "settled"
            }
          },
          portfolio: portfolio.id
        }),
        buildCashbackTransaction({
          status: "Settled",
          portfolio: portfolio.id,
          linkedAssetTransaction: assetTransactionForCashbacks.id
        })
      ]);

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(response.body).toEqual([]);
    });

    it("should not return pending deposits linked to pending or completed asset transactions", async () => {
      const pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            status: "executed"
          }
        },
        portfolio: portfolio.id
      });

      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "PendingDeposit",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 },
        pendingDeposit: pendingDepositLinkedToAssetTransaction
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(response.body).toEqual([]);
    });

    it("should return pending deposits linked to cancelled or rejected asset transactions", async () => {
      const pendingDepositLinkedToCancelledAssetTransaction = await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            status: "executed"
          }
        },
        portfolio: portfolio.id
      });

      const pendingDepositLinkedToRejectedAssetTransaction = await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            status: "executed"
          }
        },
        portfolio: portfolio.id
      });

      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Cancelled",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 },
        pendingDeposit: pendingDepositLinkedToCancelledAssetTransaction
      });

      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Rejected",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 },
        pendingDeposit: pendingDepositLinkedToRejectedAssetTransaction
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      const responseIds = response.body.map((item: any) => item._id);

      expect(response.status).toEqual(200);
      expect(responseIds).toContain(pendingDepositLinkedToCancelledAssetTransaction.id);
      expect(responseIds).toContain(pendingDepositLinkedToRejectedAssetTransaction.id);
      expect(response.status).toEqual(200);
    });

    it("should not return incomplete deposits", async () => {
      await Promise.all([
        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              version: "v3",
              status: "authorization_required"
            }
          },
          portfolio: portfolio.id
        }),
        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              version: "v3",
              status: "authorizing"
            }
          },
          bankAccount: portfolio.id
        })
      ]);

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(response.body).toEqual([]);
    });

    it("should not return cancelled cashbacks", async () => {
      await buildCashbackTransaction({
        status: "Cancelled",
        portfolio: portfolio.id,
        linkedAssetTransaction: assetTransactionForCashbacks.id
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(response.body).toEqual([]);
    });

    it("should return the bank account field populated in pending deposits by default", async () => {
      const bankAccount = await buildBankAccount({ owner: user.id });
      const deposit = await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            status: "executed"
          }
        },
        portfolio: portfolio.id,
        bankAccount: bankAccount.id
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(response.body.length).toEqual(1);
      expect(response.body[0]).toMatchObject(
        expect.objectContaining({
          _id: expect.stringMatching(deposit.id.toString())
        })
      );
      expect(response.body[0].bankAccount).toEqual(JSON.parse(JSON.stringify(bankAccount)));
    });

    it("should not return cashback that is linked to a pendingDeposit asset transaction that is linked to a failed deposit", async () => {
      const incompleteDepositLinkedToAssetTransaction = await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            version: "v3",
            status: "failed"
          }
        },
        status: "Rejected",
        bankAccount: user.bankAccounts[0].id
      });
      const portfolioBuyWithIncompleteDeposit = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "PendingDeposit",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 },
        pendingDeposit: incompleteDepositLinkedToAssetTransaction
      });
      await buildCashbackTransaction({
        status: "Pending",
        portfolio: portfolio.id,
        linkedAssetTransaction: portfolioBuyWithIncompleteDeposit.id
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const receivedTransactions = response.body;
      expect(receivedTransactions.length).toEqual(0);
    });

    it("should not return cashback that is linked to repeating investment with failed deposit", async () => {
      const automation = await buildTopUpAutomation({ owner: user.id });
      const failedDepositLinkedToRepeatingInvestment = await buildDepositCashTransaction({
        owner: user.id,
        linkedAutomation: automation,
        directDebit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Failed"
            }
          }
        },
        status: "Rejected",
        bankAccount: user.bankAccounts[0].id
      });
      const repeatingInvestmentWithFailedDeposit = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        linkedAutomation: automation,
        status: "PendingDeposit",
        portfolioTransactionCategory: "buy",
        originalInvestmentAmount: 1000,
        consideration: { currency: "GBP", amount: 983 },
        pendingDeposit: failedDepositLinkedToRepeatingInvestment
      });
      await buildCashbackTransaction({
        status: "Pending",
        portfolio: portfolio.id,
        linkedAssetTransaction: repeatingInvestmentWithFailedDeposit.id
      });
      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const receivedTransactions = response.body;
      expect(receivedTransactions.length).toEqual(0);
    });

    it("should not return deposit linked to automated investment", async () => {
      const automation = await buildTopUpAutomation({ owner: user, portfolio: portfolio, active: false });

      await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            version: "v3",
            status: "executed"
          }
        },
        bankAccount: user.bankAccounts[0].id,
        linkedAutomation: automation
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const receivedTransactions = response.body;
      expect(receivedTransactions.length).toEqual(0);
    });

    it("should return the newer deposit first", async () => {
      const [deposit1, deposit2] = await Promise.all([
        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            }
          },
          portfolio: portfolio.id,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
        }),

        buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            }
          },
          createdAt: new Date(),
          portfolio: portfolio.id
        })
      ]);

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      const responseIds = response.body.map((item: any) => item._id);

      expect(response.status).toEqual(200);
      expect(responseIds[0]).toEqual(deposit2.id);
      expect(responseIds[1]).toEqual(deposit1.id);
    });

    it("should return the savings withdrawals with 'Pending', 'PendingTopUp' status", async () => {
      const [pendingSavingsWithdrawal, pendingBuySavingsWithdrawal] = await Promise.all([
        buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          createdAt: new Date("2024-01-02")
        }),
        buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingTopUp",
          createdAt: new Date("2024-01-01")
        })
      ]);

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      const data = response.body;

      expect(response.status).toEqual(200);
      // This also checks sort order
      expect(data[0]).toMatchObject({
        id: pendingSavingsWithdrawal.id,
        displayTitle: "Savings GBP -> Cash Balance",
        displayAmount: pendingSavingsWithdrawal.consideration.amount,
        displayDate: pendingSavingsWithdrawal.createdAt.toISOString()
      });
      expect(data[1]).toMatchObject({
        id: pendingBuySavingsWithdrawal.id,
        displayTitle: "Savings GBP -> Cash Balance",
        displayAmount: pendingBuySavingsWithdrawal.consideration.amount,
        displayDate: pendingBuySavingsWithdrawal.createdAt.toISOString()
      });
    });

    it("should not return the savings withdrawals with 'Settled', 'Rejected' status", async () => {
      await Promise.all([
        buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Rejected"
        }),
        buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: new Date()
        })
      ]);

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      const responseIds = response.body.map((item: any) => item._id);

      expect(response.status).toEqual(200);
      expect(responseIds).toHaveLength(0);
    });

    it("should not return deposit linked to a savings topup", async () => {
      const deposit = await buildDepositCashTransaction(
        {
          depositAction: DepositActionEnum.DEPOSIT_AND_SAVE,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            }
          },
          status: "Pending",
          bankAccount: user.bankAccounts[0].id
        },
        user,
        portfolio
      );

      await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id,
        pendingDeposit: deposit.id,
        status: "PendingDeposit"
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const receivedTransactions = response.body;
      expect(receivedTransactions.length).toEqual(0);
    });

    it("should return deposit linked to a rejected credit ticket", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Rejected"
      });

      await buildDepositCashTransaction(
        {
          depositAction: DepositActionEnum.JUST_PAY,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          status: "Pending",
          bankAccount: user.bankAccounts[0].id,
          linkedCreditTicket: creditTicket.id
        },
        user,
        portfolio
      );

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const receivedTransactions = response.body;
      expect(receivedTransactions.length).toEqual(1);
    });

    it("should return deposit linked to a pending credit ticket", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending"
      });

      await buildDepositCashTransaction(
        {
          depositAction: DepositActionEnum.JUST_PAY,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          status: "Pending",
          bankAccount: user.bankAccounts[0].id,
          linkedCreditTicket: creditTicket.id
        },
        user,
        portfolio
      );

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const receivedTransactions = response.body;
      expect(receivedTransactions.length).toEqual(1);
    });

    it("should not return deposit linked to a credited credit ticket", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Credited"
      });

      await buildDepositCashTransaction(
        {
          depositAction: DepositActionEnum.JUST_PAY,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          depositMethod: DepositMethodEnum.BANK_TRANSFER,
          status: "Pending",
          bankAccount: user.bankAccounts[0].id,
          linkedCreditTicket: creditTicket.id
        },
        user,
        portfolio
      );

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/pending-cash-flows`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const receivedTransactions = response.body;
      expect(receivedTransactions.length).toEqual(0);
    });
  });

  describe("POST /portfolios/:id/submit-orders", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 },
        { assetId: "equities_apple", quantity: 2, price: 10 },
        { assetId: "equities_microsoft", quantity: 2, price: 1.01 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      portfolio = await buildPortfolio({
        owner: user.id,
        holdings,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
    });

    it("should update user portfolioConversionStatus to 'inProgress' for uninvested user with no portfolio holdings", async () => {
      const user = await buildUser({ portfolioConversionStatus: "notStarted" });
      await buildSubscription({ owner: user.id });
      const portfolio = await buildPortfolio({
        owner: user.id,
        holdings: [],
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
      await buildInvestmentProduct(true, {
        assetId: "equities_us",
        price: 10
      });

      WealthkernelService.UKInstance.createOrder = jest.fn((): any => {
        return {
          id: faker.string.uuid()
        };
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_us: {
              side: "buy",
              money: 5
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const updatedUser = await User.findOne({ _id: user.id });
      expect(updatedUser?.portfolioConversionStatus).toBe("inProgress");
    });

    it("should return status 400 when id references a VIRTUAL portfolio", async () => {
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: "VIRTUAL",
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_us: {
              side: "sell",
              quantity: 5
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Portfolio given is not real"
          }
        })
      );
    });

    it("should return status 400 when body param pendingOrders has invalid asset key", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_uss: {
              side: "buy",
              money: 5
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter 'pendingOrders'",
            message: `Invalid asset key '${["equities_uss"]}'`
          }
        })
      );
    });

    it("should return status 400 when body param pendingOrders has invalid side value", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_us: {
              side: "buyas",
              money: 5
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter 'pendingOrders'",
            message: "Invalid side value 'buyas' for asset 'equities_us'"
          }
        })
      );
    });

    it("should return status 400 when body param pendingOrders has invalid money value", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_us: {
              side: "buy",
              money: "asd$#%"
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter 'pendingOrders'",
            message: "Invalid money value 'asd$#%' for buy order for asset 'equities_us'"
          }
        })
      );
    });

    it("should return status 400 when body param pendingOrders has invalid quantity value", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_global: {
              side: "sell",
              quantity: "2!@#S"
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter 'pendingOrders'",
            message: "Invalid quantity value '2!@#S' for sell order for asset 'equities_global'"
          }
        })
      );
    });

    it("should return status 400 for an order for not existing holding", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_us: {
              side: "sell",
              quantity: 5
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Investment product equities_us is not available."
          }
        })
      );
    });

    it("should return status 400 when given more than one pending order", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: 10
            },
            equities_china: {
              side: "buy",
              money: 10
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Endpoint does not support multiple pending orders"
          }
        })
      );
    });

    it("should return status 400 for sell order and pre-existing sell pending orders which all total to more than the user's holdings", async () => {
      const transaction = await buildAssetTransaction({
        owner: user.id
      });
      const pendingOrder = await buildOrder({
        isin: ASSET_CONFIG["equities_eu"].isin,
        transaction: transaction.id,
        side: "Sell",
        quantity: 10
      });
      transaction.orders = [pendingOrder.id];
      await transaction.save();

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "sell",
              quantity: 5
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You have placed a sell order with quantity larger than what you hold"
          }
        })
      );
    });

    it("should return status 400 when paymentMethod is gift but gift field is missing", async () => {
      const orderAmount = 5;

      await Promise.all([
        buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 10
        }),
        buildGift({
          targetUserEmail: user.email,
          consideration: {
            amount: orderAmount * 100,
            currency: "GBP"
          }
        })
      ]);

      WealthkernelService.UKInstance.createOrder = jest.fn((): any => {
        return {
          id: faker.string.uuid()
        };
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders?paymentMethod=gift`)
        .send({
          pendingOrders: {
            equities_us: {
              side: "buy",
              money: 5
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    describe("when pending orders contain a valid ETF buy order", () => {
      let response: supertest.Response;

      beforeEach(async () => {
        await buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 10
        });

        WealthkernelService.UKInstance.createOrder = jest.fn((): any => {
          return {
            id: faker.string.uuid()
          };
        });

        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
          .send({
            pendingOrders: {
              equities_us: {
                side: "buy",
                money: 5
              }
            }
          })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
      });

      it("should succeed with status 200 with valid buy order", async () => {
        expect(response.status).toEqual(200);
        const parsedTransaction = JSON.parse(response.text) as AssetTransactionDocument;
        expect(parsedTransaction.orders[0].displayAmount).toBe(500);
        expect(parsedTransaction.orders[0].submissionIntent).toEqual(OrderSubmissionIntentEnum.AGGREGATE);
        expect(parsedTransaction.orders[0].isSubmittedToBroker).toEqual(false);
      });

      it("should emit an FirstInvestmentCreated event", async () => {
        expect(eventEmitter.emit).toBeCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should emit an InvestmentCreated event", async () => {
        expect(eventEmitter.emit).toBeCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({
            amount: 5,
            currency: "GBP",
            category: "etf",
            frequency: "one-off",
            fxFees: 0,
            isFirst: true,
            redeemedGift: false,
            side: "buy"
          })
        );
      });
    });

    describe("when pending orders contain a valid buy order and user has already invested before", () => {
      let response: supertest.Response;

      beforeEach(async () => {
        await buildAssetTransaction({ portfolio: portfolio, owner: user, status: "Settled" });
        await buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 10
        });

        WealthkernelService.UKInstance.createOrder = jest.fn((): any => {
          return {
            id: faker.string.uuid()
          };
        });

        jest.spyOn(eventEmitter, "emit");

        response = await request(app)
          .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
          .send({
            pendingOrders: {
              equities_us: {
                side: "buy",
                money: 5
              }
            }
          })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
      });

      it("should succeed with status 200 with valid buy order", async () => {
        expect(response.status).toEqual(200);
        const parsedTransaction = JSON.parse(response.text) as AssetTransactionDocument;
        expect(parsedTransaction.orders[0].displayAmount).toBe(500);
      });

      it("should NOT emit an FirstInvestmentCreated event", async () => {
        expect(eventEmitter.emit).not.toBeCalledWith(
          events.transaction.firstInvestmentCreation.eventId,
          expect.anything()
        );
      });

      it("should emit an InvestmentCreated event", async () => {
        expect(eventEmitter.emit).toBeCalledWith(
          events.transaction.investmentCreation.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({
            amount: 5,
            currency: "GBP",
            category: "etf",
            frequency: "one-off",
            fxFees: 0,
            isFirst: false,
            redeemedGift: false,
            side: "buy"
          })
        );
      });
    });

    it("should succeed with status 200 with valid buy order and gift payment method", async () => {
      const orderAmount = 5;

      const [, gift] = await Promise.all([
        buildInvestmentProduct(true, {
          assetId: "equities_us",
          price: 10
        }),
        buildGift({
          targetUserEmail: user.email,
          consideration: {
            amount: orderAmount * 100,
            currency: "GBP"
          }
        })
      ]);

      WealthkernelService.UKInstance.createOrder = jest.fn((): any => {
        return {
          id: faker.string.uuid()
        };
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders?paymentMethod=gift`)
        .send({
          pendingOrders: {
            equities_us: {
              side: "buy",
              money: 5
            }
          },
          gift: gift.id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          status: "PendingGift",
          pendingGift: expect.objectContaining({
            id: gift.id
          })
        })
      );
    });

    it("should succeed with status 200 with valid sell order", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "sell",
              quantity: 1
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
    });

    it("should succeed with status 200 and submit order realtime for ETF order within market hours", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      jest.spyOn(WealthkernelService.EUInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const TODAY = new Date("2023-10-30T15:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      const userWithRealtimeETFExecutionEnabled = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: userWithRealtimeETFExecutionEnabled.id });
      const portfolioWithRealtimeETFExecutionEnabled = await buildPortfolio({
        owner: userWithRealtimeETFExecutionEnabled.id,
        holdings: [],
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });

      const response = await request(app)
        .post(
          `/api/m2m/portfolios/${portfolioWithRealtimeETFExecutionEnabled._id}/submit-orders?executeEtfOrdersInRealtime=true`
        )
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: 5
            }
          }
        })
        .set("external-user-id", userWithRealtimeETFExecutionEnabled._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(WealthkernelService.EUInstance.createOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          aggregate: false,
          isin: ASSET_CONFIG["equities_eu"].isin,
          portfolioId: portfolioWithRealtimeETFExecutionEnabled.providers.wealthkernel.id,
          consideration: {
            amount: 4, // £5 - ��1 real-time fee
            currency: "GBP"
          },
          side: "Buy"
        }),
        expect.anything()
      );

      const updatedOrder = await Order.findOne({
        isin: ASSET_CONFIG["equities_eu"].isin
      });
      expect(updatedOrder.providers.wealthkernel.id).toEqual(STOCK_WK_ORDER_ID);
      expect(updatedOrder?.submissionIntent).toEqual(OrderSubmissionIntentEnum.REAL_TIME);
    });

    it("should succeed with status 200 and NOT submit order realtime for ETF order if outside market hours", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      jest.spyOn(WealthkernelService.EUInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const TODAY = new Date("2023-10-30T03:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      const userWithRealtimeETFExecutionEnabled = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
        portfolioConversionStatus: "completed"
      });
      await buildSubscription({ owner: userWithRealtimeETFExecutionEnabled.id });
      const portfolioWithRealtimeETFExecutionEnabled = await buildPortfolio({
        owner: userWithRealtimeETFExecutionEnabled.id,
        holdings: [],
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });

      const response = await request(app)
        .post(
          `/api/m2m/portfolios/${portfolioWithRealtimeETFExecutionEnabled._id}/submit-orders?executeEtfOrdersInRealtime=true`
        )
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: 50
            }
          }
        })
        .set("external-user-id", userWithRealtimeETFExecutionEnabled._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(WealthkernelService.EUInstance.createOrder).not.toHaveBeenCalled();

      const updatedOrder = await Order.findOne({
        isin: ASSET_CONFIG["equities_eu"].isin
      });
      expect(updatedOrder?.isSubmittedToBroker).toEqual(false);
    });

    it("should succeed with status 200 and submit order realtime for stock order within market hours", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const TODAY = new Date("2023-10-30T15:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_apple: {
              side: "sell",
              quantity: 1
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(WealthkernelService.UKInstance.createOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          aggregate: false,
          isin: ASSET_CONFIG["equities_apple"].isin,
          portfolioId: portfolio.providers.wealthkernel.id,
          side: "Sell"
        }),
        expect.anything()
      );

      const updatedOrder = await Order.findOne({
        isin: ASSET_CONFIG["equities_apple"].isin
      });
      expect(updatedOrder.providers.wealthkernel.id).toEqual(STOCK_WK_ORDER_ID);
      expect(updatedOrder?.submissionIntent).toEqual(OrderSubmissionIntentEnum.REAL_TIME);
    });

    it("should succeed with status 200 and NOT submit order realtime for stock order if outside market hours", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const TODAY = new Date("2023-10-30T11:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_apple: {
              side: "sell",
              quantity: 1
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
    });

    it("should not update user portfolioConversionStatus for invested user with no portfolio holdings", async () => {
      await buildInvestmentProduct(true, {
        assetId: "equities_us",
        price: 10
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
        .send({
          pendingOrders: {
            equities_us: {
              side: "buy",
              money: 5
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const updatedUser = await User.findOne({ _id: user.id });
      expect(updatedUser?.portfolioConversionStatus).toBe("completed");
    });

    describe("and active reward exist with 'Pending' status", () => {
      it("should succeed with status 200 for valid buy order", async () => {
        await Promise.all([
          buildInvestmentProduct(true, {
            assetId: "equities_us",
            price: 10
          }),
          buildReward({
            asset: "equities_us",
            targetUser: user.id,
            updatedAt: new Date(),
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: "reward-deposit-id",
                  status: "Created"
                }
              }
            }
          })
        ]);

        const response = await request(app)
          .post(`/api/m2m/portfolios/${portfolio._id}/submit-orders`)
          .send({
            pendingOrders: {
              equities_us: {
                side: "buy",
                money: 5
              }
            }
          })
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(200);

        const createdAssetTransaction = (await AssetTransaction.findOne({
          status: "Pending"
        }).populate("orders pendingDeposit")) as AssetTransactionDocument;
        expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(createdAssetTransaction)));
      });
    });
  });

  describe("POST /portfolios/:id/buy-asset-pending-deposit", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let deposit: DepositCashTransactionDocument;

    const orderAmount = 50;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });
      await buildSubscription({ owner: user.id });
      const ASSET_COMMON_IDS_CONFIG: {
        assetId: investmentUniverseConfig.AssetType;
        quantity: number;
        price: number;
      }[] = [
        { assetId: "equities_china", quantity: 2, price: 10 },
        { assetId: "equities_eu", quantity: 2, price: 10 },
        { assetId: "equities_apple", quantity: 2, price: 10 },
        { assetId: "real_estate_us", quantity: 2, price: 10 }
      ];
      const holdings = await Promise.all(
        ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
          buildHoldingDTO(true, assetId, quantity, { price })
        )
      );
      portfolio = await buildPortfolio({
        owner: user.id,
        holdings,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
      deposit = await buildDepositCashTransaction(
        {
          consideration: { amount: orderAmount * 100, currency: "GBP" }
        },
        user,
        portfolio
      );
    });

    it("should return status 400 with proper message, if portfolio is VIRTUAL", async () => {
      const userWithVirtualPortfolio = await buildUser();
      const virtualPortfolio = await buildPortfolio({
        owner: userWithVirtualPortfolio.id,
        mode: "VIRTUAL"
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${virtualPortfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            real_estate_us: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", userWithVirtualPortfolio._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: expect.stringContaining("Portfolio given is not real")
          }
        })
      );
    });

    it("should return status 400 when pendingOrders body param is missing", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "body param 'pendingOrders' is required"
          }
        })
      );
    });

    it("should return status 400 when pendingDeposit body param is missing", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: orderAmount
            }
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'pendingDeposit' is required"
          }
        })
      );
    });

    it("should return status 400 when pendingDeposit body param is not ObjectId", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: "something-invalid"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for 'pendingDeposit'"
          }
        })
      );
    });

    it("should return status 400 for an invalid asset key", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_euu: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter 'pendingOrders'",
            message: `Invalid asset key '${["equities_euu"]}'`
          }
        })
      );
    });

    it("should return status 400 for an invalid side value", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buyas",
              money: orderAmount
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter 'pendingOrders'",
            message: "Invalid side value 'buyas' for asset 'equities_eu'"
          }
        })
      );
    });

    it("should return status 400 for an invalid money value", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: "asd$#%"
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter 'pendingOrders'",
            message: "Invalid money value 'asd$#%' for buy order for asset 'equities_eu'"
          }
        })
      );
    });

    it("should return status 400 when given a sell order", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "sell",
              quantity: 1
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Endpoint does not support sell orders"
          }
        })
      );
    });

    it("should return status 400 when given more than one pending order", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: orderAmount
            },
            equities_china: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Endpoint does not support multiple pending orders"
          }
        })
      );
    });

    it("should return status 400 for a pending deposit that is not of same value as the order amount", async () => {
      const differentOrderAmount = 10;

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: differentOrderAmount
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You want to 1-step deposit & invest £10.00 but you deposited £50.00"
          }
        })
      );
    });

    it("should return status 400 when we don't have the given investment product", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_us: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Investment product equities_us is not available."
          }
        })
      );
    });

    it("should return status 400 for orderAmount < MIN_ALLOWED_ASSET_INVESTMENT", async () => {
      const orderAmount = MIN_ALLOWED_ASSET_INVESTMENT - 0.01;
      const depositLowerThanMinimumAllowedInvestment = await buildDepositCashTransaction(
        {
          consideration: { amount: orderAmount * 100, currency: "GBP" }
        },
        user,
        portfolio
      );
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: depositLowerThanMinimumAllowedInvestment._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "The submitted order does not pass our investment criteria"
          }
        })
      );
    });

    it("should return status 400 for quantity < MIN_ALLOWED_ASSET_QUANTITY", async () => {
      const veryExpensiveAssetPrice = 120000;

      await buildInvestmentProduct(true, { assetId: "commodities_gold", price: veryExpensiveAssetPrice });

      const orderAmountForVerySmallQuantity = Decimal.sub(MIN_ALLOWED_ASSET_QUANTITY, 0.00001).mul(
        veryExpensiveAssetPrice
      );
      const depositForVerySmallQuantityOrder = await buildDepositCashTransaction(
        {
          consideration: {
            amount: Decimal.mul(orderAmountForVerySmallQuantity, 100).toNumber(),
            currency: "GBP"
          }
        },
        user,
        portfolio
      );

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            commodities_gold: {
              side: "buy",
              money: orderAmountForVerySmallQuantity
            }
          },
          pendingDeposit: depositForVerySmallQuantityOrder._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "The submitted order does not pass our investment criteria"
          }
        })
      );
    });

    it("should return 200, create a new asset transaction pending deposit and a new order for a valid request with a USD traded asset", async () => {
      expect((await AssetTransaction.find()) as AssetTransactionDocument[]).toHaveLength(0);
      expect((await Order.find()) as OrderDocument[]).toHaveLength(0);

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            real_estate_us: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const allTransactions: AssetTransactionDocument[] = await AssetTransaction.find();
      expect(allTransactions.length === 1);
      const newAssetTransaction = allTransactions[0];
      expect(newAssetTransaction).toEqual(
        expect.objectContaining({
          status: "PendingDeposit",
          pendingDeposit: deposit._id,
          fees: expect.objectContaining({
            fx: {
              amount: new Decimal(orderAmount).mul(FX_FEE_SPREADS_WH.free).toDecimalPlaces(2).toNumber(),
              currency: "GBP"
            },
            commission: {
              amount: 0,
              currency: "GBP"
            }
          })
        })
      );

      const allOrders: OrderDocument[] = await Order.find();
      expect(allOrders.length === 1);
      const newOrder = allOrders[0];
      expect(newOrder.transaction).toEqual(newAssetTransaction._id);
      expect(newAssetTransaction.orders).toHaveLength(1);
      expect(newAssetTransaction.orders[0]).toEqual(newOrder._id);
    });

    it("should return 200, create a new asset transaction pending deposit and a new order for a valid request", async () => {
      expect((await AssetTransaction.find()) as AssetTransactionDocument[]).toHaveLength(0);
      expect((await Order.find()) as OrderDocument[]).toHaveLength(0);

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const allTransactions: AssetTransactionDocument[] = await AssetTransaction.find();
      expect(allTransactions.length === 1);
      const newAssetTransaction = allTransactions[0];
      expect(newAssetTransaction).toEqual(
        expect.objectContaining({
          status: "PendingDeposit",
          pendingDeposit: deposit._id,
          fees: expect.objectContaining({
            fx: {
              amount: 0, // No FX fee since there are not USD traded orders
              currency: "GBP"
            },
            commission: {
              amount: 0,
              currency: "GBP"
            }
          })
        })
      );

      const allOrders: OrderDocument[] = await Order.find();
      expect(allOrders.length === 1);
      const newOrder = allOrders[0];
      expect(newOrder.transaction).toEqual(newAssetTransaction._id);
      expect(newAssetTransaction.orders).toHaveLength(1);
      expect(newAssetTransaction.orders[0].toString()).toEqual(newOrder.id);

      await newAssetTransaction.populate("orders pendingDeposit");
      const parsedTransaction = JSON.parse(response.text) as AssetTransactionDocument;

      expect(parsedTransaction).toMatchObject(JSON.parse(JSON.stringify(newAssetTransaction)));
      expect(parsedTransaction).toEqual(
        expect.objectContaining({
          isCancellable: expect.anything(),
          executionWindow: expect.anything()
        })
      );
      expect(parsedTransaction.orders[0]).toEqual(
        expect.objectContaining({
          isCancellable: expect.anything(),
          executionWindow: expect.anything(),
          displayAmount: expect.anything(),
          displayQuantity: expect.anything()
        })
      );
    });

    it("should return 200, create a new asset transaction and order for a valid request with realtime ETF execution without submitting to WK", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();

      jest.spyOn(WealthkernelService.EUInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const userWithEnabledRealtimeETFExecution = await buildUser({
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      await buildSubscription({ owner: userWithEnabledRealtimeETFExecution.id });
      const portfolioWithEnabledRealtimeETFExecution = await buildPortfolio({
        owner: userWithEnabledRealtimeETFExecution.id,
        holdings: [],
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
      const depositForUser = await buildDepositCashTransaction(
        {
          consideration: { amount: orderAmount * 100, currency: "GBP" }
        },
        userWithEnabledRealtimeETFExecution,
        portfolioWithEnabledRealtimeETFExecution
      );

      const TODAY = new Date("2023-10-30T15:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      expect((await AssetTransaction.find()) as AssetTransactionDocument[]).toHaveLength(0);
      expect((await Order.find()) as OrderDocument[]).toHaveLength(0);

      const response = await request(app)
        .post(
          `/api/m2m/portfolios/${portfolioWithEnabledRealtimeETFExecution._id}/buy-asset-pending-deposit?executeEtfOrdersInRealtime=true`
        )
        .send({
          pendingOrders: {
            equities_eu: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: depositForUser._id
        })
        .set("external-user-id", userWithEnabledRealtimeETFExecution._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      expect(WealthkernelService.EUInstance.createOrder).not.toHaveBeenCalled();
    });

    it("should NOT submit order realtime for stock order even if within market hours", async () => {
      const STOCK_WK_ORDER_ID = faker.string.uuid();
      jest.spyOn(WealthkernelService.UKInstance, "createOrder").mockResolvedValue({ id: STOCK_WK_ORDER_ID });

      const TODAY = new Date("2023-10-30T15:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/buy-asset-pending-deposit`)
        .send({
          pendingOrders: {
            equities_apple: {
              side: "buy",
              money: orderAmount
            }
          },
          pendingDeposit: deposit._id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(WealthkernelService.UKInstance.createOrder).not.toHaveBeenCalled();
    });

    describe("when the user is uninvested", () => {
      let noHoldingsUser: UserDocument;
      let noHoldingsPortfolio: PortfolioDocument;
      let response: request.Response;
      let newDeposit: DepositCashTransactionDocument;

      beforeEach(async () => {
        noHoldingsUser = await buildUser({ portfolioConversionStatus: "notStarted" });
        await buildSubscription({ owner: noHoldingsUser.id });
        noHoldingsPortfolio = await buildPortfolio({
          owner: noHoldingsUser.id,
          holdings: [],
          mode: PortfolioModeEnum.REAL,
          cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
      });

      describe(" and deposit truelayer status is executed", () => {
        beforeEach(async () => {
          newDeposit = await buildDepositCashTransaction(
            {
              consideration: { amount: orderAmount * 100, currency: "GBP" },
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "executed"
                }
              }
            },
            user,
            portfolio
          );

          response = await request(app)
            .post(`/api/m2m/portfolios/${noHoldingsPortfolio.id}/buy-asset-pending-deposit`)
            .send({
              pendingOrders: {
                equities_eu: {
                  side: "buy",
                  money: orderAmount
                }
              },
              pendingDeposit: newDeposit.id
            })
            .set("external-user-id", noHoldingsUser.id)
            .set("Accept", "application/json");
        });

        it("should succeed with 200 response status", () => {
          expect(response.status).toEqual(200);
        });

        it("should NOT update user portfolio conversion status'", async () => {
          const updatedUser = await User.findOne({ _id: noHoldingsUser.id });
          expect(updatedUser?.portfolioConversionStatus).toBe("notStarted");
        });

        it("should create an asset transaction with orders", async () => {
          const allTransactions: AssetTransactionDocument[] = await AssetTransaction.find({
            portfolio: noHoldingsPortfolio.id
          }).populate("orders pendingDeposit");
          expect(allTransactions.length === 1);
          const newAssetTransaction = allTransactions[0];
          expect(newAssetTransaction.status).toEqual("PendingDeposit");
          expect((newAssetTransaction.pendingDeposit as DepositCashTransactionDocument).id?.toString()).toEqual(
            newDeposit.id.toString()
          );

          const allOrders: OrderDocument[] = await Order.find({ transaction: newAssetTransaction.id });
          expect(allOrders.length === 1);
          const newOrder = allOrders[0];
          expect(newOrder.transaction).toEqual(newAssetTransaction._id);
          expect(newAssetTransaction.orders).toHaveLength(1);
          expect(newAssetTransaction.orders[0].id.toString()).toEqual(newOrder.id.toString());

          expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(newAssetTransaction)));
        });
      });

      describe(" and deposit truelayer status is cancelled", () => {
        beforeEach(async () => {
          newDeposit = await buildDepositCashTransaction(
            {
              consideration: { amount: orderAmount * 100, currency: "GBP" },
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "failed"
                }
              }
            },
            user,
            portfolio
          );

          response = await request(app)
            .post(`/api/m2m/portfolios/${noHoldingsPortfolio.id}/buy-asset-pending-deposit`)
            .send({
              pendingOrders: {
                equities_eu: {
                  side: "buy",
                  money: orderAmount
                }
              },
              pendingDeposit: newDeposit.id
            })
            .set("external-user-id", noHoldingsUser.id)
            .set("Accept", "application/json");
        });

        it("should succeed with 200 response status", () => {
          expect(response.status).toEqual(200);
        });

        it("should NOT update user portfolio conversion status'", async () => {
          const updatedUser = await User.findOne({ _id: noHoldingsUser.id });
          expect(updatedUser?.portfolioConversionStatus).toBe("notStarted");
        });

        it("should create an asset transaction with orders", async () => {
          const allTransactions: AssetTransactionDocument[] = await AssetTransaction.find({
            portfolio: noHoldingsPortfolio.id
          }).populate("orders pendingDeposit");
          expect(allTransactions.length === 1);
          const newAssetTransaction = allTransactions[0];
          expect(newAssetTransaction.status).toEqual("PendingDeposit");
          expect((newAssetTransaction.pendingDeposit as DepositCashTransactionDocument).id.toString()).toEqual(
            newDeposit.id.toString()
          );

          const allOrders: OrderDocument[] = await Order.find({ transaction: newAssetTransaction.id });
          expect(allOrders.length === 1);
          const newOrder = allOrders[0];
          expect(newOrder.transaction).toEqual(newAssetTransaction._id);
          expect(newAssetTransaction.orders).toHaveLength(1);
          expect(newAssetTransaction.orders[0].id.toString()).toEqual(newOrder.id.toString());

          expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(newAssetTransaction)));
        });
      });
    });
  });

  describe("POST /portfolios/:id/allocation", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "notStarted" });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
    });

    it("should return status 400 with corresponding message when portfolio is not real", async () => {
      const nonRealPortfolio = await buildPortfolio({
        owner: user.id,
        mode: "VIRTUAL",
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
      const response = await request(app)
        .post(`/api/m2m/portfolios/${nonRealPortfolio._id}/allocation`)
        .send({})
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Portfolio given is not real"
          }
        })
      );
    });

    it("should return status 400 with corresponding message when allocation body param missing", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({})
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'allocation' is required"
          }
        })
      );
    });

    it("should return status 400 with corresponding message when allocation body param has invalid asset keys", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 3,
            equities_err: 5
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: `Invalid asset keys '${["equities_err"]}'`
          }
        })
      );
    });

    it("should return status 400 with corresponding message when allocation body param has invalid percentages for asset keys", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 3,
            equities_uk: "Sdf$#$"
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: `Invalid percentages values '${["Sdf$#$"]}'`
          }
        })
      );
    });

    it("should return status 400 with corresponding message when allocation body param percentages do not sum to 100", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 3,
            equities_uk: 13
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Percentages of 'allocation' do not sum to 100"
          }
        })
      );
    });

    it("should fail with status 400 when allocationCreationFlow has invalid value", async () => {
      jest.spyOn(eventEmitter, "emit");
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 45,
            equities_uk: 55
          },
          flow: "invalid value"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    it("should succeed with status 200 when allocationCreationFlow is an empty string", async () => {
      jest.spyOn(eventEmitter, "emit");

      Portfolio.findOneAndUpdate({ _id: portfolio.id }, { allocationCreationFlow: "builder" });
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 45,
            equities_uk: 55
          },
          flow: ""
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const updatedRealPortfolio: PortfolioDocument = (await Portfolio.findOne({
        owner: user._id,
        mode: "REAL"
      })) as PortfolioDocument;
      expect(updatedRealPortfolio.allocationCreationFlow).toEqual(portfolio.allocationCreationFlow);
    });

    it("should succeed with status 200, update portfolio allocation and emit event when for a new real portfolio", async () => {
      jest.spyOn(eventEmitter, "emit");
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 45,
            equities_uk: 55
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const updatedRealPortfolio: PortfolioDocument = (await Portfolio.findOne({
        owner: user._id,
        mode: "REAL"
      })) as PortfolioDocument;
      expect(updatedRealPortfolio).toMatchObject(
        expect.objectContaining({
          initialHoldingsAllocation: expect.arrayContaining([
            expect.objectContaining({
              percentage: 45,
              assetCommonId: "equities_eu"
            }),
            expect.objectContaining({
              percentage: 55,
              assetCommonId: "equities_uk"
            })
          ])
        })
      );
      expect(updatedRealPortfolio.allocationCreationFlow).toBeUndefined();

      expect(eventEmitter.emit).toBeCalledWith(
        events.portfolio.portfolioAllocation.eventId,
        expect.objectContaining({ id: user.id }),
        expect.objectContaining({ allocationCreationFlow: undefined })
      );
    });

    it("should succeed with status 200 and emit an event when allocationCreationFlow has value 'builder'", async () => {
      jest.spyOn(eventEmitter, "emit");
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 45,
            equities_uk: 55
          },
          flow: "builder"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const updatedRealPortfolio: PortfolioDocument = (await Portfolio.findOne({
        owner: user._id,
        mode: "REAL"
      })) as PortfolioDocument;
      expect(updatedRealPortfolio).toMatchObject(
        expect.objectContaining({
          allocationCreationFlow: "builder",
          initialHoldingsAllocation: expect.arrayContaining([
            expect.objectContaining({
              percentage: 45,
              assetCommonId: "equities_eu"
            }),
            expect.objectContaining({
              percentage: 55,
              assetCommonId: "equities_uk"
            })
          ])
        })
      );

      expect(eventEmitter.emit).toBeCalledWith(
        events.portfolio.portfolioAllocation.eventId,
        expect.objectContaining({ id: user.id }),
        expect.objectContaining({ allocationCreationFlow: "builder" })
      );
    });

    it("should succeed with status 200 and emit an event when allocationCreationFlow has value 'robo_advisor'", async () => {
      jest.spyOn(eventEmitter, "emit");
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 45,
            equities_uk: 55
          },
          flow: "robo_advisor"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const updatedRealPortfolio: PortfolioDocument = (await Portfolio.findOne({
        owner: user._id,
        mode: "REAL"
      })) as PortfolioDocument;
      expect(updatedRealPortfolio).toMatchObject(
        expect.objectContaining({
          allocationCreationFlow: "robo_advisor",
          initialHoldingsAllocation: expect.arrayContaining([
            expect.objectContaining({
              percentage: 45,
              assetCommonId: "equities_eu"
            }),
            expect.objectContaining({
              percentage: 55,
              assetCommonId: "equities_uk"
            })
          ])
        })
      );

      expect(eventEmitter.emit).toBeCalledWith(
        events.portfolio.portfolioAllocation.eventId,
        expect.objectContaining({ id: user.id }),
        expect.objectContaining({ allocationCreationFlow: "robo_advisor" })
      );
    });

    it("should succeed with status 200 and emit an event when allocationCreationFlow has value 'from_scratch'", async () => {
      jest.spyOn(eventEmitter, "emit");
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 45,
            equities_uk: 55
          },
          flow: "from_scratch"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const updatedRealPortfolio: PortfolioDocument = (await Portfolio.findOne({
        owner: user._id,
        mode: "REAL"
      })) as PortfolioDocument;
      expect(updatedRealPortfolio).toMatchObject(
        expect.objectContaining({
          allocationCreationFlow: "from_scratch",
          initialHoldingsAllocation: expect.arrayContaining([
            expect.objectContaining({
              percentage: 45,
              assetCommonId: "equities_eu"
            }),
            expect.objectContaining({
              percentage: 55,
              assetCommonId: "equities_uk"
            })
          ])
        })
      );

      expect(eventEmitter.emit).toBeCalledWith(
        events.portfolio.portfolioAllocation.eventId,
        expect.objectContaining({ id: user.id }),
        expect.objectContaining({ allocationCreationFlow: "from_scratch" })
      );
    });

    it("should succeed with status 200 without updating allocationCreationFlow value and emit an event without properties when allocationCreationFlow is set already", async () => {
      jest.spyOn(eventEmitter, "emit");

      const INITIAL_FLOW = "from_scratch";
      await Portfolio.findOneAndUpdate(
        { _id: portfolio.id },
        {
          initialHoldingsAllocation: [
            {
              percentage: 100,
              assetCommonId: "equities_eu"
            }
          ],
          allocationCreationFlow: INITIAL_FLOW
        }
      );
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 45,
            equities_uk: 55
          },
          flow: "builder"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const updatedRealPortfolio: PortfolioDocument = (await Portfolio.findOne({
        owner: user._id,
        mode: "REAL"
      })) as PortfolioDocument;
      expect(updatedRealPortfolio).toMatchObject(
        expect.objectContaining({
          allocationCreationFlow: INITIAL_FLOW,
          initialHoldingsAllocation: expect.arrayContaining([
            expect.objectContaining({
              percentage: 45,
              assetCommonId: "equities_eu"
            }),
            expect.objectContaining({
              percentage: 55,
              assetCommonId: "equities_uk"
            })
          ])
        })
      );

      expect(eventEmitter.emit).not.toBeCalledWith(
        events.portfolio.portfolioAllocation.eventId,
        expect.anything(),
        expect.anything()
      );
    });

    it("should succeed with status 200 and only update the portfolio allocation of the real portfolio when both virtual & real portfolios exist", async () => {
      jest.spyOn(eventEmitter, "emit");

      const convertedUser = await buildUser({ portfolioConversionStatus: "completed" });

      const [virtualPortfolio, realPortfolio] = await Promise.all([
        buildPortfolio({
          owner: convertedUser.id,
          mode: "VIRTUAL"
        }),
        buildPortfolio({
          owner: convertedUser.id,
          mode: PortfolioModeEnum.REAL,
          cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          initialHoldingsAllocation: [
            {
              percentage: 100,
              assetCommonId: "equities_us"
            }
          ]
        })
      ]);

      const response = await request(app)
        .post(`/api/m2m/portfolios/${realPortfolio._id}/allocation`)
        .send({
          allocation: {
            equities_eu: 45,
            equities_uk: 55
          }
        })
        .set("external-user-id", convertedUser._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const updatedPortfolio = JSON.parse(response.text);
      expect(updatedPortfolio).toMatchObject(
        expect.objectContaining({
          initialHoldingsAllocation: expect.arrayContaining([
            expect.objectContaining({
              percentage: 45,
              assetCommonId: "equities_eu"
            }),
            expect.objectContaining({
              percentage: 55,
              assetCommonId: "equities_uk"
            })
          ])
        })
      );

      const updatedVirtualPortfolio = await Portfolio.findById(virtualPortfolio._id);
      expect(updatedVirtualPortfolio?.initialHoldingsAllocation).toEqual([]);

      expect(eventEmitter.emit).not.toHaveBeenCalled();
    });

    describe("when allocation contains valid assets with zero percentage", () => {
      let convertedUser: UserDocument;
      let response: supertest.Response;

      beforeEach(async () => {
        jest.spyOn(eventEmitter, "emit");

        convertedUser = await buildUser({ portfolioConversionStatus: "completed" });
        const realPortfolio = await buildPortfolio({
          owner: convertedUser.id,
          mode: PortfolioModeEnum.REAL,
          cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        response = await request(app)
          .post(`/api/m2m/portfolios/${realPortfolio._id}/allocation`)
          .send({
            allocation: {
              equities_eu: 45,
              equities_uk: 55,
              equities_us: 0
            }
          })
          .set("external-user-id", convertedUser._id)
          .set("Accept", "application/json");
      });

      it("should succeed with status 200 and emit event", async () => {
        expect(response.status).toEqual(200);
        expect(eventEmitter.emit).toBeCalledWith(
          events.portfolio.portfolioAllocation.eventId,
          expect.objectContaining({ id: convertedUser.id }),
          expect.objectContaining({ allocationCreationFlow: undefined })
        );
      });

      it("should not save asset with zero percentage allocation", async () => {
        const updatedPortfolio = JSON.parse(response.text);
        expect(updatedPortfolio).toMatchObject(
          expect.objectContaining({
            initialHoldingsAllocation: expect.arrayContaining([
              expect.objectContaining({
                percentage: 45,
                assetCommonId: "equities_eu"
              }),
              expect.objectContaining({
                percentage: 55,
                assetCommonId: "equities_uk"
              })
            ])
          })
        );
      });
    });
  });

  describe("POST /portfolios/:id/personalisation-preferences", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
    });

    it("POST /portfolios/:id/personalisation-preferences (portfolio is not real) should return status 400 with proper cause", async () => {
      const nonRealPortfolio = await buildPortfolio({
        owner: user.id,
        mode: "VIRTUAL",
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
      const response = await request(app)
        .post(`/api/m2m/portfolios/${nonRealPortfolio._id}/personalisation-preferences`)
        .send({})
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Portfolio given is not real"
          }
        })
      );
    });

    it("POST /portfolios/:id/personalisation-preferences (with missing 'sectors' field) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/personalisation-preferences`)
        .send({
          personalisationPreferences: {
            assetClasses: ["equities"],
            geography: "global",
            risk: 10
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Missing field 'sectors'"
          }
        })
      );
    });

    it("POST /portfolios/:id/personalisation-preferences (with invalid 'geography' field) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/personalisation-preferences`)
        .send({
          personalisationPreferences: {
            assetClasses: ["equities"],
            sectors: ["general"],
            geography: "asda#$$",
            risk: 10
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: expect.stringContaining("Param 'personalisationPreferences.geography' has invalid value")
          }
        })
      );
    });

    it("POST /portfolios/:id/personalisation-preferences (with invalid value for 'sectors' field) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/personalisation-preferences`)
        .send({
          personalisationPreferences: {
            assetClasses: ["equities"],
            sectors: ["general", "sdasa&^#sd"],
            geography: "usa",
            risk: 10
          }
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: expect.stringContaining("Param 'personalisationPreferences.sectors' has invalid value")
          }
        })
      );
    });

    it("POST /portfolios/:id/personalisation-preferences with proper environment should succeed with status 200", async () => {
      const personalisationPreferences = {
        assetClasses: ["equities"],
        geography: "usa",
        risk: 10,
        sectors: ["general"]
      };

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/personalisation-preferences`)
        .send({
          personalisationPreferences
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const updatedPortfolio = (await Portfolio.findById(portfolio.id)) as PortfolioDocument;
      const expectedPersonalisationPreferences = JSON.parse(JSON.stringify(personalisationPreferences));
      expectedPersonalisationPreferences.risk =
        (personalisationPreferences.risk || RISK_CONFIG.default) / RISK_CONFIG.maxLevel;

      // It is more proper to use expect.toMatchObject but for some reason it does not work.
      // It prints 'serializes to the same string' so as an alternative we compare json strings
      expect(JSON.stringify(updatedPortfolio.personalisationPreferences)).toEqual(
        JSON.stringify(expectedPersonalisationPreferences)
      );
    });
  });

  describe("POST /portfolios/:id/withdraw", () => {
    it("should return status 400 with error message when request is made with invalid portfolio id", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      const portfolioId = faker.string.uuid();
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolioId}/withdraw`)
        .send({
          amount: 20
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Portfolio does not exist"
          }
        })
      );
    });

    it("should return status 400 with error message when request is made with not numeric amount body param", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      const portfolio = await buildPortfolio({ owner: user.id });
      const amount = "dg&45";
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw`)
        .send({
          amount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'amount' , should be numeric"
          }
        })
      );
    });

    it("should return status 400 with error message when portfolio does not belong to user", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      const portfolio = await buildPortfolio({ owner: (await buildUser())._id });
      const amount = 20;
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw`)
        .send({
          amount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(403);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Inaccessible resource",
            message: "Portfolio does not belong to user"
          }
        })
      );
    });

    it("should return status 500 with error message when request is for virtual portfolio", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      const portfolio = await buildPortfolio({ owner: user.id, mode: "VIRTUAL" });
      const amount = 20;
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw`)
        .send({
          amount,
          bankAccountId: user.bankAccounts[0].id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(500);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "An error occurred",
            message: "Portfolio is not real"
          }
        })
      );
    });

    it("should return status 400 with error message when request is made with amount greater than settled cash", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 30, reserved: 0, settled: 10 } }
      });
      const amount = 20;
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw`)
        .send({
          amount,
          bankAccountId: user.bankAccounts[0].id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Portfolio's settled cash is lower than the withdrawal amount"
          }
        })
      );
    });

    it("should succeed with status 200 and return the updated portfolio when valid request is made", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 40, reserved: 0, settled: 40 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
      const amount = 20;
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw`)
        .send({
          amount,
          bankAccountId: user.bankAccounts[0].id
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const withdrawal = await WithdrawalCashTransaction.findOne({ portfolio: portfolio.id });
      expect(withdrawal).toMatchObject(
        expect.objectContaining({
          owner: portfolio.owner,
          portfolio: portfolio._id,
          consideration: {
            amount: Decimal.mul(amount, 100).toNumber(),
            currency: "GBP"
          },
          bankReference: expect.anything()
        })
      );
      const actualPortfolio = (await Portfolio.findById(portfolio.id)) as PortfolioDocument;
      expect(actualPortfolio.cash.GBP.available).toEqual(
        Decimal.sub(portfolio.cash.GBP.available, amount).toNumber()
      );
      expect(actualPortfolio.cash.GBP.settled).toEqual(Decimal.sub(portfolio.cash.GBP.settled, amount).toNumber());
    });

    it("should succeed with status 200 and return the updated portfolio when request is made with body containing valid amount, bankAccountId", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 40, reserved: 0, settled: 40 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
      const amount = 20;
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw`)
        .send({
          amount,
          bankAccountId: user.bankAccounts[0].id
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const withdrawal = await WithdrawalCashTransaction.findOne({ portfolio: portfolio.id });
      expect(withdrawal).toMatchObject(
        expect.objectContaining({
          owner: portfolio.owner,
          portfolio: portfolio._id,
          consideration: {
            amount: Decimal.mul(amount, 100).toNumber(),
            currency: "GBP"
          },
          bankAccount: user.bankAccounts[0]._id
        })
      );
      const actualPortfolio = (await Portfolio.findById(portfolio.id)) as PortfolioDocument;
      expect(actualPortfolio.cash.GBP.available).toEqual(
        Decimal.sub(portfolio.cash.GBP.available, amount).toNumber()
      );
      expect(actualPortfolio.cash.GBP.settled).toEqual(Decimal.sub(portfolio.cash.GBP.settled, amount).toNumber());
    });

    it("should return status 400 with error message when request is made with inactive bank account", async () => {
      const user = await buildUser(
        { kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" },
        false
      );
      const bankAccount = await buildBankAccount({ owner: user.id, active: false });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 40, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
      const amount = 20;
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw`)
        .send({
          amount,
          bankAccountId: bankAccount.id
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "Bank account is not active"
          }
        })
      );
    });
  });

  describe("GET /portfolios/:id/asset-restriction", () => {
    it("should return status 400 with proper message, if portfolio is VIRTUAL", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: "VIRTUAL"
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/asset-restriction`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: expect.stringContaining("Portfolio given is not real")
          }
        })
      );
    });

    it("should return status 400 with proper message for not existing portfolio id", async () => {
      const user = await buildUser();

      const response = await request(app)
        .get("/api/m2m/portfolios/garbageId/asset-restriction")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Portfolio does not exist"
          }
        })
      );
    });

    it("should return status 400 with proper message for invalid assetId param", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, "equities_eu", 0.05, {}, { price: 50 })]
      });

      const invalidAssetId = "garbageAssetId";
      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/asset-restriction?assetId=${invalidAssetId}`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: `Param 'assetId' has invalid value '${invalidAssetId}', must be one of [${Object.values(
              AssetArrayConst
            )}]`
          }
        })
      );
    });

    it("should return 200, with correct restricted quantity", async () => {
      const EXPECTED_GIFT_RESTICTED_QUANTITY = 0.5;
      const EXPECTED_REWARD_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";

      const user = await buildUser({ portfolioConversionStatus: "completed" });
      const holdings = await Promise.all([
        buildHoldingDTO(true, ASSET, 2),
        buildHoldingDTO(true, "equities_us", 1),
        buildHoldingDTO(true, "equities_china", 1)
      ]);
      const giftedHoldings = new Map(
        Object.entries({
          [ASSET]: [
            {
              quantity: EXPECTED_GIFT_RESTICTED_QUANTITY,
              unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 1)
            }
          ]
        })
      ) as Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
      const portfolio = await buildPortfolio({
        owner: user.id,
        cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        mode: PortfolioModeEnum.REAL,
        holdings,
        giftedHoldings
      });

      await Promise.all([
        buildReward({
          targetUser: user.id,
          asset: ASSET,
          quantity: EXPECTED_REWARD_RESTICTED_QUANTITY,
          unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), RESTRICTED_HOLDING_PERIOD_DAYS + 5),
          status: "Settled",
          accepted: true,
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        }), // unrestricted reward
        buildReward({
          targetUser: user.id,
          asset: ASSET,
          quantity: EXPECTED_REWARD_RESTICTED_QUANTITY,
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5),
          status: "Settled",
          accepted: true,
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        }) // restricted for 5 more days
      ]);

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/asset-restriction?assetId=${ASSET}`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      expect(JSON.parse(response.text)).toEqual({
        assetCommonId: ASSET,
        restrictedQuantity: EXPECTED_GIFT_RESTICTED_QUANTITY + EXPECTED_REWARD_RESTICTED_QUANTITY,
        hasRestrictedQuantity: true
      });
    });
  });

  describe("GET /portfolios/:id/restricted-holdings", () => {
    it("should return status 400 with proper message, if portfolio is VIRTUAL", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: "VIRTUAL"
      });

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/restricted-holdings`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: expect.stringContaining("Portfolio given is not real")
          }
        })
      );
    });

    it("should return status 400 with proper message for not existing portfolio id", async () => {
      const user = await buildUser();

      const response = await request(app)
        .get("/api/m2m/portfolios/garbageId/restricted-holdings")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Portfolio does not exist"
          }
        })
      );
    });

    it("should return 200, with correct restricted holdings", async () => {
      const EXPECTED_GIFT_RESTICTED_QUANTITY = 0.5;
      const EXPECTED_REWARD_RESTICTED_QUANTITY = 0.5;
      const ASSET = "equities_uk";

      const user = await buildUser({ portfolioConversionStatus: "completed" });
      const holdings = await Promise.all([
        buildHoldingDTO(true, ASSET, 2),
        buildHoldingDTO(true, "equities_us", 1),
        buildHoldingDTO(true, "equities_china", 1)
      ]);
      const giftedHoldings = new Map(
        Object.entries({
          [ASSET]: [
            {
              quantity: EXPECTED_GIFT_RESTICTED_QUANTITY,
              unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 1)
            }
          ]
        })
      ) as Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
      const portfolio = await buildPortfolio({
        owner: user.id,
        cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        mode: PortfolioModeEnum.REAL,
        holdings,
        giftedHoldings
      });

      await Promise.all([
        buildReward({
          targetUser: user.id,
          asset: ASSET,
          quantity: EXPECTED_REWARD_RESTICTED_QUANTITY,
          unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), RESTRICTED_HOLDING_PERIOD_DAYS + 5),
          status: "Settled",
          accepted: true,
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        }), // unrestricted reward
        buildReward({
          targetUser: user.id,
          asset: ASSET,
          quantity: EXPECTED_REWARD_RESTICTED_QUANTITY,
          unrestrictedAt: DateUtil.getDateAfterNdays(new Date(), 5),
          status: "Settled",
          accepted: true,
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        }) // restricted for 5 more days
      ]);

      const response = await request(app)
        .get(`/api/m2m/portfolios/${portfolio.id}/restricted-holdings`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      expect(JSON.parse(response.text)).toEqual({
        restrictedAssets: [
          {
            assetCommonId: ASSET,
            restrictedQuantity: EXPECTED_GIFT_RESTICTED_QUANTITY + EXPECTED_REWARD_RESTICTED_QUANTITY
          }
        ],
        hasRestrictedQuantity: true
      });
    });
  });

  describe("POST /portfolios/:id/topup-savings", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    const orderAmount = 50;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed", kycStatus: "passed" });
      await buildSubscription({ owner: user.id });

      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
    });

    it("should return status 400 with proper message, if portfolio is VIRTUAL", async () => {
      const userWithVirtualPortfolio = await buildUser({ kycStatus: "passed" });
      const virtualPortfolio = await buildPortfolio({
        owner: userWithVirtualPortfolio.id,
        mode: "VIRTUAL"
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${virtualPortfolio._id}/topup-savings`)
        .send({
          orderAmount
        })
        .set("external-user-id", userWithVirtualPortfolio._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: expect.stringContaining("Portfolio given is not real")
          }
        })
      );
    });

    it("should return status 400 for an invalid savings product id", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/topup-savings`)
        .send({
          savingsProductId: "mmf_wealthyhood",
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "mmf_wealthyhood is not a valid Saving Product"
          }
        })
      );
    });

    it("should return status 400 for orderAmount > cash", async () => {
      const orderAmount = portfolio.cash.GBP.available + 10;
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/topup-savings`)
        .send({
          orderAmount: orderAmount,
          savingsProductId: "mmf_dist_gbp"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You want to invest £60.00 buy you only have £50.00 deposited"
          }
        })
      );
    });

    it("should return status 400 for orderAmount < MIN_ALLOWED_SAVINGS_INVESTMENT", async () => {
      const orderAmount = MIN_ALLOWED_SAVINGS_INVESTMENT - 0.01;
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/topup-savings`)
        .send({
          orderAmount: orderAmount,
          savingsProductId: "mmf_dist_gbp"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You need to invest in savings at least £1.00."
          }
        })
      );
    });

    it("should return 200, create a new savings topup transaction and a new order for a valid request", async () => {
      expect((await SavingsTopupTransaction.find()) as SavingsTopupTransactionDocument[]).toHaveLength(0);
      expect((await Order.find()) as OrderDocument[]).toHaveLength(0);

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/topup-savings`)
        .send({
          orderAmount: orderAmount,
          savingsProductId: "mmf_dist_gbp"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const allTransactions: SavingsTopupTransactionDocument[] = await SavingsTopupTransaction.find();
      expect(allTransactions.length === 1);
      const newSavingsTopupTransaction = allTransactions[0];
      expect(newSavingsTopupTransaction).toEqual(
        expect.objectContaining({
          status: "Pending"
        })
      );
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(newSavingsTopupTransaction)));

      const allOrders: OrderDocument[] = await Order.find({ transaction: newSavingsTopupTransaction.id });
      expect(allOrders).toHaveLength(0);
    });
  });

  describe("POST /portfolios/:id/withdraw-savings", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    const orderAmount = 50;

    beforeEach(async () => {
      user = await buildUser({ portfolioConversionStatus: "completed", kycStatus: "passed" });
      await buildSubscription({ owner: user.id });

      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([
          [
            "mmf_dist_gbp",
            {
              amount: 5000,
              currency: "GBX"
            }
          ]
        ])
      });
    });

    it("should return status 400 with proper message, if portfolio is VIRTUAL", async () => {
      const userWithVirtualPortfolio = await buildUser({ kycStatus: "passed" });
      const virtualPortfolio = await buildPortfolio({
        owner: userWithVirtualPortfolio.id,
        mode: "VIRTUAL"
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${virtualPortfolio._id}/withdraw-savings`)
        .send({
          orderAmount
        })
        .set("external-user-id", userWithVirtualPortfolio._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: expect.stringContaining("Portfolio given is not real")
          }
        })
      );
    });

    it("should return status 400 for an invalid savings product id", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw-savings`)
        .send({
          savingsProductId: "mmf_wealthyhood",
          orderAmount
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "mmf_wealthyhood is not a valid Saving Product"
          }
        })
      );
    });

    it("should return status 400 if orderAmount is missing", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw-savings`)
        .send({
          savingsProductId: "mmf_dist_gbp"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Param 'orderAmount' is required"
          }
        })
      );
    });

    it("should return status 400 if orderAmount exceeds saving holdings", async () => {
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw-savings`)
        .send({
          savingsProductId: "mmf_dist_gbp",
          orderAmount: portfolio.savings?.get("mmf_dist_gbp")?.amount + 10
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You have placed a sell order with quantity larger than what you hold"
          }
        })
      );
    });

    it("should return status 400 for orderAmount < MIN_ALLOWED_SAVINGS_WITHDRAWAL", async () => {
      const orderAmount = MIN_ALLOWED_SAVINGS_WITHDRAWAL - 0.01;
      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw-savings`)
        .send({
          orderAmount: orderAmount,
          savingsProductId: "mmf_dist_gbp"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "You need to withdraw savings of value at least £0.02."
          }
        })
      );
    });

    it("should return 200, create a new savings withdrawal for a valid request", async () => {
      const TODAY = new Date("2022-01-31T08:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      expect((await SavingsWithdrawalTransaction.find()) as SavingsWithdrawalTransactionDocument[]).toHaveLength(
        0
      );
      expect((await Order.find()) as OrderDocument[]).toHaveLength(0);

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw-savings`)
        .send({
          orderAmount: orderAmount,
          savingsProductId: "mmf_dist_gbp"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const allTransactions: SavingsWithdrawalTransactionDocument[] = await SavingsWithdrawalTransaction.find();
      expect(allTransactions.length === 1);
      const newSavingsWithdrawalTransaction = allTransactions[0];
      expect(newSavingsWithdrawalTransaction).toEqual(
        expect.objectContaining({
          status: "Pending"
        })
      );
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(newSavingsWithdrawalTransaction)));

      const allOrders: OrderDocument[] = await Order.find({ transaction: newSavingsWithdrawalTransaction.id });
      expect(allOrders).toHaveLength(0);
    });

    it("should return 200, create a new savings withdrawal for a valid request when we are within the execution window", async () => {
      const TODAY = new Date("2022-01-31T10:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      expect((await SavingsWithdrawalTransaction.find()) as SavingsWithdrawalTransactionDocument[]).toHaveLength(
        0
      );
      expect((await Order.find()) as OrderDocument[]).toHaveLength(0);

      // The user has a pending savings top-up
      await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: {
          amount: Decimal.mul(orderAmount, 100).toNumber(),
          currency: "GBP"
        },
        status: "Pending"
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw-savings`)
        .send({
          orderAmount: orderAmount,
          savingsProductId: "mmf_dist_gbp"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const allTransactions: SavingsWithdrawalTransactionDocument[] = await SavingsWithdrawalTransaction.find();
      expect(allTransactions.length === 1);
      const newSavingsWithdrawalTransaction = allTransactions[0];
      expect(newSavingsWithdrawalTransaction).toEqual(
        expect.objectContaining({
          status: "Pending"
        })
      );
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(newSavingsWithdrawalTransaction)));

      const allOrders: OrderDocument[] = await Order.find({ transaction: newSavingsWithdrawalTransaction.id });
      expect(allOrders).toHaveLength(0);
    });

    it("should return 200, create a new savings withdrawal for a valid request when the withdrawal is created as pending top-up", async () => {
      const TODAY = new Date("2022-01-31T08:00:00Z");
      Date.now = jest.fn(() => TODAY.valueOf());

      const user = await buildUser({ portfolioConversionStatus: "completed", kycStatus: "passed" });
      await buildSubscription({ owner: user.id });

      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: { GBP: { available: 50, reserved: 0, settled: 0 } },
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([
          [
            "mmf_dist_gbp",
            {
              amount: 0,
              currency: "GBX"
            }
          ]
        ])
      });

      expect((await SavingsWithdrawalTransaction.find()) as SavingsWithdrawalTransactionDocument[]).toHaveLength(
        0
      );
      expect((await Order.find()) as OrderDocument[]).toHaveLength(0);

      // The user has a pending savings top-up
      await buildSavingsTopup({
        owner: user.id,
        portfolio: portfolio.id,
        consideration: {
          amount: Decimal.mul(orderAmount, 100).toNumber(),
          currency: "GBP"
        },
        status: "Pending"
      });

      const response = await request(app)
        .post(`/api/m2m/portfolios/${portfolio._id}/withdraw-savings`)
        .send({
          orderAmount: orderAmount,
          savingsProductId: "mmf_dist_gbp"
        })
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const allTransactions: SavingsWithdrawalTransactionDocument[] = await SavingsWithdrawalTransaction.find();
      expect(allTransactions.length === 1);
      const newSavingsWithdrawalTransaction = allTransactions[0];
      expect(newSavingsWithdrawalTransaction).toEqual(
        expect.objectContaining({
          status: "PendingTopUp"
        })
      );
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(newSavingsWithdrawalTransaction)));

      const allOrders: OrderDocument[] = await Order.find({ transaction: newSavingsWithdrawalTransaction.id });
      expect(allOrders).toHaveLength(0);
    });
  });

  describe("GET /portfolios/:id/with-returns-by-tenor", () => {
    describe("when a user's portfolio value changes", () => {
      let portfolio: PortfolioDocument;
      let user: UserDocument;

      beforeEach(async () => {
        const TODAY = new Date("2021-11-25T00:00:00Z");
        const YESTERDAY = new Date("2021-11-24T00:00:00Z");
        Date.now = jest.fn(() => +TODAY);

        user = await buildUser({});
        portfolio = await buildPortfolio({
          owner: user.id,
          holdings: [await buildHoldingDTO(true, "equities_apple", 1, { price: 200 })],
          mode: PortfolioModeEnum.REAL
        });

        const transaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        transaction.orders = [
          await buildOrder({
            isin: ASSET_CONFIG["equities_apple"].isin,
            status: "Settled",
            side: "Buy",
            consideration: {
              originalAmount: 10000,
              amount: 10000,
              currency: "GBP"
            },
            transaction: transaction.id,
            filledAt: DateUtil.getDateOfDaysAgo(YESTERDAY, 365)
          })
        ];
        transaction.save();

        await Promise.all([
          buildDailyPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: 100 },
            date: DateUtil.getDateOfDaysAgo(YESTERDAY, 365)
          }),
          buildDailyPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: 140 },
            date: DateUtil.getDateOfDaysAgo(YESTERDAY, 31)
          }),
          buildDailyPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: 160 },
            date: DateUtil.getDateOfDaysAgo(YESTERDAY, 7)
          }),
          buildDailyPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: 180 },
            date: YESTERDAY
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio.id,
            pricePerCurrency: { GBP: 200 },
            timestamp: TODAY
          })
        ]);

        await RedisClientService.Instance.del(`portfolios:up_by:${portfolio.id}`);
        await RedisClientService.Instance.del(`portfolios:mwrr:${portfolio.id}`);
        await RedisClientService.Instance.del(`portfolios:value_at_mwrr:${portfolio.id}`);
      });

      it("should return status 200 with correct returns by tenor", async () => {
        const response = await request(app)
          .get(`/api/m2m/portfolios/${portfolio._id}/with-returns-by-tenor`)
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject({
          id: portfolio.id,
          owner: expect.objectContaining({ id: user.id }),
          calculatedPrice: 200,
          holdings: [
            {
              asset: expect.objectContaining({ commonId: "equities_apple" }),
              quantity: 1,
              sinceBuyReturns: expect.closeTo(1),
              assetCommonId: "equities_apple"
            }
          ],
          returnsValues: {
            max: expect.closeTo(1),
            "1y": expect.closeTo(1),
            "6m": expect.closeTo(1),
            "3m": expect.closeTo(1),
            "1m": expect.closeTo(0.43),
            "1w": expect.closeTo(0.25)
          },
          upByValues: {
            max: 100,
            "1y": 100,
            "6m": 100,
            "3m": 100,
            "1m": 60,
            "1w": 40
          }
        });
      });
    });

    describe("when the user creates his first asset transactions this week", () => {
      let portfolio: PortfolioDocument;
      let user: UserDocument;

      const ASSET_ID: investmentUniverseConfig.AssetType = "equities_us";
      const TODAY = new Date();
      const FIVE_DAYS_AGO = DateUtil.getDateOfDaysAgo(TODAY, 4);

      beforeEach(async () => {
        user = await buildUser();
        await buildSubscription({
          owner: user.id
        });
        const investmentProduct = await buildInvestmentProduct(false, {
          assetId: ASSET_ID
        });
        portfolio = await buildPortfolio({
          owner: user.id,
          providers: { wealthkernel: { id: "portfolio-id-1", status: "Active" } },
          holdings: [
            {
              quantity: 1,
              assetCommonId: ASSET_ID
            }
          ]
        });

        buildIntraDayAssetTicker({
          investmentProduct: investmentProduct.id,
          currency: "GBP",
          timestamp: FIVE_DAYS_AGO,
          dailyReturnPercentage: 0,
          pricePerCurrency: {
            GBP: 10
          }
        });

        const transaction1 = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        transaction1.orders = [
          await buildOrder({
            isin: ASSET_CONFIG[ASSET_ID].isin,
            status: "Settled",
            side: "Buy",
            consideration: {
              originalAmount: 2000,
              amount: 2000,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Pending", id: "123456", submittedAt: FIVE_DAYS_AGO } },
            transaction: transaction1.id,
            filledAt: FIVE_DAYS_AGO
          })
        ];
        transaction1.save();

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: 20 },
          timestamp: FIVE_DAYS_AGO
        });

        const transaction2 = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });
        transaction2.orders = [
          await buildOrder({
            isin: ASSET_CONFIG[ASSET_ID].isin,
            status: "Settled",
            side: "Sell",
            consideration: {
              originalAmount: 1000,
              amount: 1000,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Pending", id: "123456", submittedAt: TODAY } },
            transaction: transaction2.id,
            filledAt: TODAY
          })
        ];
        transaction2.save();

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: 10 },
          timestamp: TODAY
        });

        await RedisClientService.Instance.del(`portfolios:up_by:${portfolio.id}`);
        await RedisClientService.Instance.del(`portfolios:value_at_up_by:${portfolio.id}`);
        await RedisClientService.Instance.del(`portfolios:mwrr:${portfolio.id}`);
        await RedisClientService.Instance.del(`portfolios:value_at_mwrr:${portfolio.id}`);
        await RedisClientService.Instance.del(`intraday:portfolio:${portfolio.id}`);
      });

      it("should return status 200 with correct returns by tenor", async () => {
        const response = await request(app)
          .get(`/api/m2m/portfolios/${portfolio._id}/with-returns-by-tenor`)
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(response.status).toEqual(200);

        expect(JSON.parse(response.text)).toMatchObject({
          id: portfolio.id,
          owner: expect.objectContaining({ id: user.id }),
          holdings: [
            {
              asset: expect.objectContaining({ commonId: ASSET_ID }),
              quantity: 1,
              assetCommonId: ASSET_ID
            }
          ],
          returnsValues: expect.objectContaining({
            max: expect.closeTo(0, 1),
            "1y": expect.closeTo(0, 1),
            "6m": expect.closeTo(0, 1),
            "3m": expect.closeTo(0, 1),
            "1m": expect.closeTo(0, 1),
            "1w": expect.closeTo(0, 1)
          }),
          upByValues: {
            max: 0,
            "1y": 0,
            "6m": 0,
            "3m": 0,
            "1m": 0,
            "1w": 0
          }
        });
      });
    });
  });
});
